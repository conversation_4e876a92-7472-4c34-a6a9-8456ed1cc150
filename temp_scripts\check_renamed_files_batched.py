#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# 要检查的目录
RENAMED_DIR = "E:/ComfyUI/output/张瑾妍/renamed"

def check_renamed_files():
    """检查重命名后的文件"""
    if not os.path.exists(RENAMED_DIR):
        print(f"目录不存在: {RENAMED_DIR}")
        return
    
    files = os.listdir(RENAMED_DIR)
    files.sort()
    
    print(f"在目录 {RENAMED_DIR} 中找到 {len(files)} 个文件:")
    
    # 分批输出文件列表（每5个一组）
    for i in range(0, len(files), 5):
        batch = files[i:i+5]
        for file in batch:
            print(f"- {file}")
        if i + 5 < len(files):
            print()  # 加入空行分隔每批
    
    # 检查是否有缺失的表情
    expected_emotions = [
        "admiration", "amusement", "anger", "annoyance", "approval", 
        "caring", "confusion", "curiosity", "desire", "disappointment", 
        "disapproval", "disgust", "embarrassment", "excitement", "fear", 
        "gratitude", "grief", "joy", "love", "nervousness", 
        "neutral", "optimism", "pride", "realization", "relief", 
        "remorse", "sadness", "surprise"
    ]
    
    renamed_emotions = [os.path.splitext(file)[0].lower() for file in files]
    
    missing_emotions = [emotion for emotion in expected_emotions if emotion not in renamed_emotions]
    extra_emotions = [emotion for emotion in renamed_emotions if emotion not in expected_emotions]
    
    if missing_emotions:
        print("\n缺少以下表情:")
        for emotion in missing_emotions:
            print(f"- {emotion}")
    else:
        print("\n所有期望的表情都已成功重命名。")
    
    if extra_emotions:
        print("\n发现额外的表情:")
        for emotion in extra_emotions:
            print(f"- {emotion}")

if __name__ == "__main__":
    check_renamed_files()