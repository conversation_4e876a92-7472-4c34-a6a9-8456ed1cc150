import os
import re
import glob

# 情绪提示文件
EMOTION_PROMPTS = [
    "admiration.txt", "amusement.txt", "anger.txt", "annoyance.txt", 
    "approval.txt", "caring.txt", "confusion.txt", "curiosity.txt",
    "desire.txt", "disappointment.txt", "disapproval.txt", "disgust.txt", 
    "embarrassment.txt", "excitement.txt", "fear.txt", "gratitude.txt", 
    "grief.txt", "joy.txt", "love.txt", "nervousness.txt", 
    "neutral.txt", "optimism.txt", "pride.txt", "realization.txt", 
    "relief.txt", "remorse.txt", "sadness.txt", "surprise.txt"
]

# 输出目录
OUTPUT_DIR = "emotion_prompts_export"

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 读取所有情绪提示文件并导出
all_prompts = {}
for emotion_file in EMOTION_PROMPTS:
    try:
        filepath = os.path.join("emotion_prompts", emotion_file)
        
        # 读取文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
        # 保存内容
        all_prompts[emotion_file] = content
        
        # 输出到单个文件
        emotion_name = os.path.splitext(emotion_file)[0]
        with open(os.path.join(OUTPUT_DIR, f"{emotion_name}.txt"), 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"已导出: {emotion_name}")
        
    except Exception as e:
        print(f"处理 {emotion_file} 时出错: {str(e)}")

# 创建合并文件
try:
    with open(os.path.join(OUTPUT_DIR, "all_emotions.txt"), 'w', encoding='utf-8') as f:
        for emotion_file in EMOTION_PROMPTS:
            emotion_name = os.path.splitext(emotion_file)[0]
            f.write(f"--- {emotion_name} ---\n")
            f.write(all_prompts[emotion_file])
            f.write("\n\n")
    print("已创建合并文件: all_emotions.txt")
except Exception as e:
    print(f"创建合并文件时出错: {str(e)}")

# 输出每个文件的简单内容
try:
    with open(os.path.join(OUTPUT_DIR, "emotions_content.md"), 'w', encoding='utf-8') as f:
        f.write("# 情绪提示词内容\n\n")
        for emotion_file in EMOTION_PROMPTS:
            emotion_name = os.path.splitext(emotion_file)[0]
            content = all_prompts[emotion_file]
            
            # 仅提取第一行（主要描述）和关键部分
            first_line = content.split('\n')[0]
            f.write(f"## {emotion_name}\n")
            f.write(f"{first_line}\n\n")
            
            # 提取关键部分
            if "NO PANTS" in content:
                f.write("- 包含 NO PANTS 描述\n")
            if "LEGS EXTREMELY WIDELY SEPARATED" in content:
                f.write("- 包含大腿分开描述\n")
            if "white underwear" in content.lower() or "white panties" in content.lower():
                f.write("- 包含白色内裤描述\n")
            f.write("\n")
    
    print("已创建内容摘要: emotions_content.md")
except Exception as e:
    print(f"创建内容摘要时出错: {str(e)}")

print("\n导出完成! 所有表情提示词已导出到 emotion_prompts_export 目录") 