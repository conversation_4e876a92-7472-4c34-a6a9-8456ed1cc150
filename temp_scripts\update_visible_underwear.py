#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_skirt_description(text):
    """更新裙子和内裤描述，确保能看到裙下内裤"""
    # 检查是否有mini skirt关键词
    if 'mini skirt' in text or 'miniskirt' in text:
        # 添加短裙下可见内裤的描述
        if 'visible underwear' not in text and 'visible panties' not in text and 'white triangle' not in text:
            # 在裙子描述后添加内裤可见的描述
            text = re.sub(r'(light blue denim mini skirt[^,\.]*)', 
                         r'\1, so short that her pure white triangle panties are clearly visible beneath the hem', text)
            
            # 确保有站姿和角度描述使内裤可见
            if 'legs slightly apart' not in text and 'legs spread' not in text:
                text = re.sub(r'(Standing elegantly[^,\.]*)', 
                             r'\1 with legs slightly apart', text)
            
            # 添加低角度拍摄描述
            if 'angle capturing' in text and 'low angle' not in text:
                text = text.replace('angle capturing', 'low angle capturing')
            elif 'camera angle' in text and 'low camera angle' not in text:
                text = text.replace('camera angle', 'low camera angle')
            elif 'angle' not in text and 'camera' in text:
                text = re.sub(r'(camera[^,\.]*)', 
                             r'\1 from a slightly low angle', text)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_skirt_description(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_visible_underwear_example():
    """创建新的示例文件，突出展示可见的内裤"""
    example_content = """beautiful young woman, jingyan, standing elegantly in a clean minimalist interior with a pure white background with legs slightly apart, wearing an extremely sheer white gauze bikini top, so thin that the fabric is nearly transparent, clearly revealing the outline of her areolas and nipples beneath, paired with a light blue denim mini skirt that sits low on her hips, so short that her pure white triangle panties are clearly visible beneath the hem. The high hemline of the skirt and her slightly spread stance ensure that the white underwear is prominently visible from the front view. With silky pure white thigh-high stockings that perfectly contrast with the denim, the white fabric shimmering subtly in the light. The transparent nature of her top draws attention to her curves while the ultra-short skirt deliberately reveals her underwear beneath. One hand confidently resting on her hip while the other hand gently touches her exposed midriff, drawing attention to her slender waist and bare navel. Soft, even studio lighting with subtle highlights creating gentle shadows that complement her features and accentuating both the see-through quality of her top and the visibility of her panties under the skirt. Full body shot with low camera angle capturing all details of the outfit, especially the visible white triangle panties under the short skirt, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建第二个强调内裤可见的例子
    example2_content = """beautiful young woman, jingyan, photographed from a slight low angle in a bright studio with pure white background, showcasing her extremely sheer white gauze bikini top that reveals her areolas and nipples beneath the translucent fabric. She wears an ultra-short light blue denim mini skirt purposely styled to display her pure white triangle panties underneath, with the white underwear fully visible below the high hemline of the skirt. Her pose with legs moderately apart ensures maximum visibility of the white panties contrasting with the blue denim skirt above. The carefully placed studio lighting creates perfect illumination that highlights the transparency of her top and clearly defines the edges of her white underwear visible beneath the short skirt. The camera position from below emphasizes the underwear visibility while maintaining an elegant fashion portrait aesthetic. Her confident expression and poised stance with one hand on hip suggest deliberate styling to showcase both the see-through top and visible underwear beneath the miniskirt, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    examples = {
        "visible_white_panties_example.txt": example_content,
        "explicit_underwear_visibility.txt": example2_content
    }
    
    for filename, content in examples.items():
        example_path = os.path.join(EXAMPLES_DIR, filename)
        with open(example_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_visible_underwear_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新几个重要文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 强制更新内裤描述
            if 'mini skirt' in content and 'triangle panties' not in content:
                content = content.replace('light blue denim mini skirt', 
                                       'light blue denim mini skirt that sits low on her hips, so short that her pure white triangle panties are clearly visible beneath the hem')
                
                with open(joy_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 