#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 要检查的目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

def extract_all_emotions():
    """提取所有情绪文件中的手部描述"""
    print(f"提取 {EMOTION_PROMPTS_DIR} 中的所有手部描述\n")
    
    total_files_with_descriptions = 0
    total_files = 0
    
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_name = file_name.replace(".txt", "")
            file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
            emotion_files.append((emotion_name, file_path))
    
    # 按表情名称字母顺序排序
    emotion_files.sort()
    
    # 一次性输出所有结果
    for emotion_name, file_path in emotion_files:
        total_files += 1
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
            # 查找手部相关描述
            hand_patterns = [
                r'Her hands[^\.]+\.',
                r'One hand[^\.]+\.',
                r'Both hands[^\.]+\.',
                r'Slender fingers[^\.]+\.',
                r'Arms crossed[^\.]+\.',
                r'Extended slender[^\.]+\.'
            ]
            
            all_matches = []
            for pattern in hand_patterns:
                matches = re.findall(pattern, content)
                all_matches.extend(matches)
            
            if all_matches:
                total_files_with_descriptions += 1
                print(f"{emotion_name}:")
                for match in all_matches:
                    print(f"  - {match.strip()}")
                print()
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n总共检查了 {total_files} 个文件")
    print(f"找到 {total_files_with_descriptions} 个文件包含手部描述")

if __name__ == "__main__":
    extract_all_emotions()