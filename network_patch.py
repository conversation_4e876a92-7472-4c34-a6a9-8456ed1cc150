
# ComfyUI Network Optimization Patch
import asyncio
import aiohttp
import platform
import sys

def patch_aiohttp_for_windows():
    """为Windows优化aiohttp"""
    if platform.system() == "Windows":
        # 增加默认超时时间
        aiohttp.ClientTimeout.total = 1800  # 30分钟
        aiohttp.ClientTimeout.connect = 300  # 5分钟
        aiohttp.ClientTimeout.sock_read = 600  # 10分钟
        
        # 设置更大的缓冲区
        if hasattr(aiohttp, 'StreamReader'):
            aiohttp.StreamReader.DEFAULT_LIMIT = 2**16  # 64KB
        
        print("[PATCH] Applied Windows aiohttp optimizations")

def patch_asyncio_for_windows():
    """为Windows优化asyncio"""
    if platform.system() == "Windows":
        # 设置事件循环策略
        if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
            policy = asyncio.WindowsProactorEventLoopPolicy()
            asyncio.set_event_loop_policy(policy)
        
        # 增加默认超时
        if hasattr(asyncio, 'wait_for'):
            original_wait_for = asyncio.wait_for
            def patched_wait_for(aw, timeout=None, **kwargs):
                if timeout is None:
                    timeout = 1800  # 30分钟默认超时
                return original_wait_for(aw, timeout, **kwargs)
            asyncio.wait_for = patched_wait_for
        
        print("[PATCH] Applied Windows asyncio optimizations")

# 应用补丁
patch_aiohttp_for_windows()
patch_asyncio_for_windows()
