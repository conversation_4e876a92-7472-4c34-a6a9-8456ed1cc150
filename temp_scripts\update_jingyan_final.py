#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 情绪提示文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 排除不需要修改的文件
EXCLUDE_FILES = [
    "README.md", "PLEATED_SKIRT_GUIDE.md", "PLEATED_SKIRT_PREVIEW.md", 
    "negative_prompt.txt", "FINAL_INSTRUCTIONS.md", "COMBINED_SOLUTION.txt",
    "LEG_POSE_GUIDE.md", "WHITE_UNDERWEAR.txt", "skirt_trigger.txt",
    "EXTREME_SKIRT.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", "WIDE_LEG_POSE.txt",
    "FINAL_SOLUTION.md", "LIGHTING_GUIDE.md", "EXTREME_NEGATIVE.txt",
    "trigger.txt", "EXTREME_GUIDE.md", "README_USAGE.md"
]

# 定义替换函数
def replace_text(text, filename):
    # 记录是否进行了修改
    changes_made = False
    
    # 替换标记和人物名称
    if "PLEATED_SKIRT" in text and "jingyan" not in text:
        print(f"  - 修改 {filename} 中的人物名称和标记...")
        
        # 替换人物名称
        if "qingyi, 19-year-old" in text:
            text = text.replace("qingyi, 19-year-old", "jingyan, 20-year-old")
            changes_made = True
        
        # 替换标记
        if "PLEATED_SKIRT, black_pleated_miniskirt, pleated_skirt_not_pants, elegant_pleated_skirt," in text:
            text = text.replace(
                "PLEATED_SKIRT, black_pleated_miniskirt, pleated_skirt_not_pants, elegant_pleated_skirt,", 
                "LIGHT_BLUE_TOP, WHITE_SKIRT, casual_bedroom_setting, natural_pose,"
            )
            changes_made = True
    
    # 替换上衣描述
    top_pattern = r"Form-fitting black off-shoulder crop top[\s\S]*?upper body\."
    if re.search(top_pattern, text):
        print(f"  - 修改 {filename} 中的上衣描述...")
        text = re.sub(
            top_pattern,
            "Wearing a light blue off-shoulder top with long sleeves, the soft fabric hugging her curves while exposing her slender shoulders, the crop top design revealing a hint of her midriff.",
            text
        )
        changes_made = True
    
    # 替换裙子描述
    skirt_pattern = r"Elegant BLACK PLEATED MINI SKIRT[\s\S]*?sophisticated\."
    if re.search(skirt_pattern, text):
        print(f"  - 修改 {filename} 中的裙子描述...")
        text = re.sub(
            skirt_pattern,
            "Short white high-waisted mini skirt with simple pleats, the clean white fabric creating a fresh and innocent appearance while accentuating her slim waist and hips, the hem falling at upper thigh level to showcase her long legs.",
            text
        )
        changes_made = True
    
    # 替换场景描述
    scene_pattern = r"Sitting elegantly on a luxurious white bed[\s\S]*?skin\."
    if re.search(scene_pattern, text):
        print(f"  - 修改 {filename} 中的场景描述...")
        text = re.sub(
            scene_pattern,
            "Sitting casually on a neatly made bed with neutral bedding, the simple bedroom setting creating a relaxed and intimate atmosphere.",
            text
        )
        changes_made = True
    
    # 替换手部动作
    hands_pattern = r"Both hands gracefully placed behind her back[\s\S]*?appearance\."
    if re.search(hands_pattern, text):
        print(f"  - 修改 {filename} 中的手部动作...")
        text = re.sub(
            hands_pattern,
            "One hand resting naturally on the bed beside her, supporting her weight in a relaxed pose.",
            text
        )
        changes_made = True
    
    # 替换发型描述
    if "Long straight black hair with neat bangs across forehead." in text:
        print(f"  - 修改 {filename} 中的发型描述...")
        text = text.replace(
            "Long straight black hair with neat bangs across forehead.", 
            "Long straight dark brown hair with side-swept bangs framing her face beautifully."
        )
        changes_made = True
    
    # 替换光线描述
    if "Soft natural lighting in bright minimalist interior setting." in text:
        print(f"  - 修改 {filename} 中的光线描述...")
        text = text.replace(
            "Soft natural lighting in bright minimalist interior setting.", 
            "Soft natural daylight filtering through curtains, creating a gentle glow that highlights her features."
        )
        changes_made = True
    
    return text, changes_made

# 处理所有情绪文件
def update_emotion_files():
    files_updated = 0
    files_skipped = 0
    updated_files = []
    skipped_files = []
    
    # 获取所有.txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件")
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        
        # 跳过不需要修改的文件
        if filename in EXCLUDE_FILES:
            print(f"跳过: {filename} (在排除列表中)")
            files_skipped += 1
            skipped_files.append(filename)
            continue
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"处理: {filename}")
            # 替换内容
            updated_content, was_changed = replace_text(content, filename)
            
            # 如果内容有变化，写回文件
            if was_changed:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                print(f"已更新: {filename}")
                files_updated += 1
                updated_files.append(filename)
            else:
                print(f"无变化: {filename}")
                files_skipped += 1
                skipped_files.append(filename)
                
        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")
            files_skipped += 1
            skipped_files.append(filename + f" (错误: {str(e)})")
    
    return files_updated, files_skipped, updated_files, skipped_files

# 测试某个文件是否包含某个模式
def test_pattern(pattern, filename):
    try:
        with open(os.path.join(EMOTION_PROMPTS_DIR, filename), 'r', encoding='utf-8') as file:
            content = file.read()
        
        match = re.search(pattern, content)
        if match:
            print(f"在 {filename} 中找到匹配:\n{match.group(0)[:100]}...")
        else:
            print(f"在 {filename} 中没有找到匹配")
    except Exception as e:
        print(f"测试出错: {str(e)}")

if __name__ == "__main__":
    print("=" * 60)
    print("开始更新情绪提示词文件...")
    print("=" * 60)
    
    # 如果需要测试某个模式，取消注释下面的行
    # test_pattern(r"Form-fitting black off-shoulder crop top[\s\S]*?upper body\.", "joy.txt")
    # exit()
    
    updated, skipped, updated_files, skipped_files = update_emotion_files()
    
    print("\n" + "=" * 60)
    print(f"完成! 已更新 {updated} 个文件，跳过 {skipped} 个文件。")
    
    if updated > 0:
        print("\n已更新的文件:")
        for i, filename in enumerate(updated_files, 1):
            print(f"{i}. {filename}")
    
    if skipped > 0:
        print("\n跳过的文件:")
        for i, filename in enumerate(skipped_files, 1):
            print(f"{i}. {filename}")
    
    print("=" * 60) 