# ComfyUI V100 SXM2 16GB 优化指南

## 🚀 V100 SXM2 专用优化启动脚本

### 📋 优化模式说明

启动脚本提供5种针对V100 SXM2 16GB的专用优化模式：

#### 1. 平衡模式 (推荐)
**命令行参数**：
```bash
--normalvram --reserve-vram 2.0 --fp16-unet --fp32-vae --fp16-text-enc --disable-cuda-malloc --async-offload --preview-method auto
```

**特点**：
- ✅ **VRAM使用**: 14GB可用 (2GB预留)
- ✅ **稳定性**: 高稳定性，适合长时间运行
- ✅ **兼容性**: 支持大部分模型类型
- ✅ **性能**: 平衡的性能表现

**适用场景**: 日常使用、混合工作流、新手用户

#### 2. 最大VRAM模式
**命令行参数**：
```bash
--normalvram --reserve-vram 0.5 --fp16-unet --fp16-vae --fp16-text-enc --disable-cuda-malloc --async-offload --cache-none --preview-method latent2rgb
```

**特点**：
- 🔥 **VRAM使用**: 15.5GB可用 (0.5GB预留)
- 🔥 **激进优化**: VAE也使用FP16精度
- 🔥 **内存管理**: 禁用缓存，避免显存碎片
- ⚠️ **风险**: 可能出现显存不足错误

**适用场景**: 大型模型、复杂工作流、高级用户

#### 3. Flux优化模式
**命令行参数**：
```bash
--normalvram --reserve-vram 1.0 --fp16-unet --fp32-vae --fp16-text-enc --disable-cuda-malloc --async-offload --cache-lru 5 --preview-method auto --use-pytorch-cross-attention
```

**特点**：
- ⚡ **Flash Attention**: 启用PyTorch交叉注意力优化
- 🧠 **LRU缓存**: 5个节点结果缓存，适合模型切换
- 🎯 **Flux专用**: 针对Flux模型优化
- 💾 **VRAM使用**: 15GB可用 (1GB预留)

**适用场景**: Flux模型生成、图像编辑、频繁模型切换

#### 4. 视频生成模式
**命令行参数**：
```bash
--normalvram --reserve-vram 3.0 --fp16-unet --fp32-vae --fp16-text-enc --disable-cuda-malloc --async-offload --cache-none --preview-method latent2rgb
```

**特点**：
- 🎬 **视频优化**: 针对WAN2.1等视频模型
- 💾 **大预留**: 3GB预留，确保视频生成稳定
- 🚫 **无缓存**: 避免视频生成时的内存冲突
- 📺 **轻量预览**: 使用latent2rgb减少显存占用

**适用场景**: 视频生成、长序列处理、大批量渲染

#### 5. 标准模式
**命令行参数**：
```bash
--normalvram --reserve-vram 2.0 --fp16-unet --fp32-vae --disable-cuda-malloc --preview-method auto
```

**特点**：
- 🔧 **基础优化**: 基本的V100优化设置
- 🛡️ **保守配置**: 最大兼容性和稳定性
- 📊 **标准预留**: 2GB预留，适合一般使用

**适用场景**: 测试环境、兼容性测试、保守用户

## 🔧 V100 SXM2 专用优化解释

### 核心优化参数

#### `--normalvram`
- **作用**: 使用正常VRAM模式，避免lowvram的性能损失
- **V100优势**: 16GB VRAM足够，无需拆分模型

#### `--reserve-vram X.X`
- **作用**: 为系统和其他进程预留VRAM
- **V100建议**: 0.5-3.0GB，根据使用场景调整

#### `--fp16-unet`
- **作用**: UNet使用FP16精度，减少约50%显存占用
- **V100兼容**: 完全支持FP16计算

#### `--fp32-vae` / `--fp16-vae`
- **fp32-vae**: 保持VAE精度，避免黑图问题
- **fp16-vae**: 节省显存，但可能影响图像质量

#### `--fp16-text-enc`
- **作用**: 文本编码器使用FP16，节省显存
- **影响**: 对生成质量影响很小

#### `--disable-cuda-malloc`
- **作用**: 禁用cudaMallocAsync
- **V100必需**: V100与新版CUDA malloc有兼容性问题

#### `--async-offload`
- **作用**: 启用异步权重卸载
- **V100优势**: 改善内存管理，减少等待时间

### 高级优化参数

#### `--cache-lru N`
- **作用**: LRU缓存N个节点结果
- **适用**: 频繁切换模型的工作流

#### `--cache-none`
- **作用**: 禁用缓存，减少显存占用
- **适用**: 大型模型或显存紧张时

#### `--use-pytorch-cross-attention`
- **作用**: 启用PyTorch原生注意力机制
- **V100优势**: 可能提供更好的性能

#### `--preview-method`
- **auto**: 自动选择预览方法
- **latent2rgb**: 轻量级预览，节省显存

## 📊 性能对比表

| 模式 | 可用VRAM | 生成速度 | 稳定性 | 适用模型 |
|------|----------|----------|--------|----------|
| 平衡模式 | 14GB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 大部分模型 |
| 最大VRAM | 15.5GB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 大型模型 |
| Flux优化 | 15GB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | Flux系列 |
| 视频生成 | 13GB | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 视频模型 |
| 标准模式 | 14GB | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 通用模型 |

## 🎯 使用建议

### 新手用户
- 推荐使用 **平衡模式 (选项1)**
- 稳定可靠，适合学习和日常使用

### 高级用户
- **Flux模型**: 选择 **Flux优化模式 (选项3)**
- **大型模型**: 选择 **最大VRAM模式 (选项2)**
- **视频生成**: 选择 **视频生成模式 (选项4)**

### 故障排除
- **显存不足**: 降级到更保守的模式
- **生成黑图**: 使用fp32-vae模式
- **启动失败**: 使用标准模式

## 🔍 监控和调试

### VRAM监控
```bash
# 在另一个终端运行
nvidia-smi -l 1
```

### 性能测试
- 使用相同的工作流测试不同模式
- 记录生成时间和VRAM使用情况
- 根据实际需求选择最适合的模式

---

**注意**: 这些优化专门针对V100 SXM2 16GB设计，其他GPU可能需要不同的参数配置。
