import os
import re
import glob

# 获取prompt文件
prompt_files = glob.glob('emotion_prompts/*.txt')
exclude_files = ['README.md', 'README_USAGE.md', 'negative_prompt.txt']

# 更新计数器
updated_count = 0
skipped_count = 0

# 定义替换模式
legs_pattern = re.compile(r'(legs slightly parted|legs apart)', re.IGNORECASE)

# 创建更强的大腿姿势描述文件
try:
    wide_legs_prompt = """NSFW, WIDE_LEG_POSE MINISKIRT WHITE_UNDERWEAR, Beautiful woman with confident pose.

PARAMETERS: cfg:15 steps:45 sampler:DPM++_2M_Karras

CRITICAL FOCUS: WIDELY SPREAD LEGS WITH MAXIMUM VISIBILITY

FULL BODY photograph, STANDING WITH LEGS EXTREMELY WIDELY SEPARATED in a power stance.
Face directly towards camera with confident expression.
Slightly parted lips with hint of smile.

Form-fitting black off-shoulder top with plunging neckline revealing cleavage.

FOCUS: BLACK LATEX MICRO-MINISKIRT
* Extremely short shiny BLACK latex miniskirt with clearly defined sharp edges
* The hemline creates a DISTINCT HORIZONTAL LINE across upper thighs
* Bottom edge sits extremely high, barely covering her buttocks
* The LOWER EDGE of the miniskirt is clearly visible as a separate garment
* ABSOLUTELY NOT PANTS OR LEGGINGS - very clearly a skirt

FOCUS: LEGS POSITION AND STANCE
* Legs EXTREMELY WIDELY SPREAD apart in bold stance (NOT CLOSED, NOT TOGETHER)
* Feet positioned far apart, at least shoulder width or wider
* Inner thighs completely visible due to wide stance 
* Knees pointing slightly outward
* Weight evenly distributed on both feet in powerful pose
* MAXIMUM SEPARATION between thighs to fully display underwear
* Stance similar to a confident power pose or martial arts ready position

FOCUS: BRIGHT WHITE UNDERWEAR - MAXIMUM CONTRAST
* PURE WHITE silk underwear CLEARLY VISIBLE beneath the miniskirt
* BRIGHT WHITE panties (NOT BLACK, NOT PURPLE, NOT GREY - ONLY WHITE)
* SNOW WHITE underwear creating MAXIMUM contrast with the black skirt
* OBVIOUS COLOR DIFFERENCE between jet black skirt and pure white underwear
* White panties are a completely SEPARATE GARMENT from the skirt
* STARK WHITE underwear edge visible below the black skirt hemline
* White underwear FULLY DISPLAYED due to wide leg stance

Both arms positioned away from body or on hips, ensuring no obstruction of the white underwear.
Bright studio lighting specifically highlighting the WHITE color of the underwear.

Long straight black hair with neat bangs across forehead.
Specialized lighting from below to enhance visibility of white underwear between widely separated legs.
Underwear_is_pure_white extreme_color_contrast white_panties_visible black_skirt_white_panties pure_white_underwear bright_white_panties snow_white_underwear wide_stance legs_spread legs_apart bold_stance power_pose
High contrast lighting to emphasize the stark separation between black skirt and white underwear.
"""
    
    with open('emotion_prompts/WIDE_LEG_POSE.txt', 'w', encoding='utf-8') as file:
        file.write(wide_legs_prompt)
    
    print("已创建强化大腿姿势提示文件: emotion_prompts/WIDE_LEG_POSE.txt")
    updated_count += 1
except Exception as e:
    print(f"创建大腿姿势提示文件时出错: {str(e)}")

# 更新终极白色内裤文件
try:
    with open('emotion_prompts/ULTIMATE_WHITE_UNDERWEAR.txt', 'r', encoding='utf-8') as file:
        ultimate_content = file.read()
    
    # 强化大腿姿势描述
    enhanced_leg_content = ultimate_content.replace(
        "Standing with legs wide apart to fully display the separate garments", 
        "Standing with legs EXTREMELY WIDELY SPREAD APART in bold power stance, feet positioned well beyond shoulder width, inner thighs completely visible"
    )
    
    # 添加额外的腿部姿势标签
    if "CRITICAL TAGS:" in enhanced_leg_content:
        enhanced_leg_content = enhanced_leg_content.replace(
            "CRITICAL TAGS:", 
            "CRITICAL TAGS: wide_stance legs_extremely_spread maximum_leg_separation bold_stance power_pose legs_wide_apart "
        )
    
    with open('emotion_prompts/ULTIMATE_WHITE_UNDERWEAR.txt', 'w', encoding='utf-8') as file:
        file.write(enhanced_leg_content)
    
    print("已更新终极白色内裤文件，强化大腿姿势描述")
    updated_count += 1
except Exception as e:
    print(f"更新终极白色内裤文件时出错: {str(e)}")
    skipped_count += 1

# 更新现有提示文件中的腿部姿势描述
for file_path in prompt_files:
    # 跳过排除的文件
    if os.path.basename(file_path) in exclude_files:
        print(f"跳过文件: {file_path}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 修改内容，增强腿部姿势描述
        modified_content = legs_pattern.sub("legs WIDELY SPREAD APART in confident stance", content)
        
        # 如果内容中没有腿部姿势描述，添加一个
        if "legs" not in content.lower() and "NSFW" in content:
            add_position = content.find("NSFW") + 4
            modified_content = content[:add_position] + ", LEGS_SPREAD_WIDE" + content[add_position:]
            
            # 在文件中添加腿部姿势描述
            if "black hair" in modified_content:
                modified_content = modified_content.replace(
                    "black hair", 
                    "black hair.\nStanding with legs EXTREMELY WIDELY SEPARATED to fully display outfit, feet positioned well beyond shoulder width, inner thighs fully visible in confident power stance. "
                )
        
        # 如果文件内容被修改，写回文件
        if content != modified_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(modified_content)
            print(f"已更新腿部姿势描述: {file_path}")
            updated_count += 1
        else:
            print(f"文件不需要更新腿部姿势: {file_path}")
            skipped_count += 1
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        skipped_count += 1

# 更新极端负面提示词，特别强调不要紧闭大腿
try:
    with open('emotion_prompts/EXTREME_NEGATIVE.txt', 'r', encoding='utf-8') as file:
        negative_content = file.read()
    
    # 添加紧闭大腿相关的负面提示词
    if "closed legs" not in negative_content.lower():
        enhanced_negative = negative_content.strip() + """,
closed legs, legs together, thighs touching, knees together, shy pose, modest stance,
cramped pose, stiff posture, formal standing, military stance, attention pose,
legs straight down, parallel legs, conservative posture, timid stance, restrained pose,
legs close together, tight stance, restricted pose, narrow stance, compact posture,
feet close together, demure stance, prudish pose, closed-off body language
"""
        
        with open('emotion_prompts/EXTREME_NEGATIVE.txt', 'w', encoding='utf-8') as file:
            file.write(enhanced_negative)
        
        print("已更新极端负面提示词，添加紧闭大腿相关内容")
        updated_count += 1
except Exception as e:
    print(f"更新极端负面提示词时出错: {str(e)}")
    skipped_count += 1

# 创建姿势指南
try:
    pose_guide = """# 大腿敞开姿势指南

## 为什么需要强调大腿敞开？

1. **可见性问题**: 当大腿紧闭时，内裤和裙子之间的分离不明显，导致AI可能将整体解释为连体服装或裤子
2. **对比度增强**: 敞开的大腿增加了黑色裙子和白色内裤之间的视觉对比
3. **服装分离**: 宽大的站姿突出了裙子和内裤是两件分离的服装
4. **照明优化**: 分开的腿部允许光线更好地照射到内裤区域

## 有效的腿部姿势描述

### 最佳姿势术语:
- "LEGS EXTREMELY WIDELY SPREAD APART"（大腿极度分开）
- "BOLD POWER STANCE"（自信有力的站姿）
- "FEET POSITIONED WELL BEYOND SHOULDER WIDTH"（双脚位置远超肩宽）
- "INNER THIGHS COMPLETELY VISIBLE"（内侧大腿完全可见）
- "MAXIMUM SEPARATION BETWEEN THIGHS"（大腿之间最大程度分离）

### 姿势类比:
- "站姿如健身模特展示动作"
- "类似武术准备姿势"
- "自信的权力姿态，如超级英雄着陆姿势"
- "时尚模特走秀时的大步姿势"

## ComfyUI优化设置

### 使用WIDE_LEG_POSE.txt
该文件专门设计，强调:
- 极度分开的双腿姿势
- 肌肉张力和自信姿态
- 优化的光线照射内裤区域
- 强调大腿完全分离

### ControlNet姿态引导
如果可能，考虑使用ControlNet姿态引导来指定具体的大腿分开姿势:
1. 找一张有明显大腿分开姿势的参考图
2. 使用OpenPose或其他姿态估计器生成姿态图
3. 在ComfyUI中使用ControlNet-Pose节点引导生成

### 负面提示词优化
确保使用更新后的EXTREME_NEGATIVE.txt，它包含专门阻止生成紧闭大腿的术语:
- closed legs（闭合的双腿）
- legs together（并拢的双腿）
- thighs touching（大腿相触）
- shy pose（害羞姿势）等

## 姿势和服装的组合效果

大腿敞开的姿势不仅增强了内裤的可见性，还产生了以下综合效果:

1. **裙子边缘更明显**:
   - 拉伸的裙子布料创造更清晰的水平边缘
   - 强调裙子底部边缘和内裤之间的分离

2. **提高白色内裤的对比度**:
   - 敞开的大腿使内裤更多地暴露在光线下
   - 减少了光线遮挡和阴影形成

3. **增强整体构图**:
   - 动态、自信的姿势使图像更有视觉吸引力
   - 强调了女性的自信和力量感

## 故障排除

如果依然出现闭合大腿问题:

1. 增加CFG值至16-18
2. 添加更多关于腿部位置的标签
3. 确保负面提示词包含"closed legs, legs together"
4. 添加"confident_pose wide_stance power_pose"等积极标签

记住：姿势是解决内裤可见性问题的关键要素，与色彩和照明策略协同工作可以产生最佳效果。
"""
    
    with open('emotion_prompts/LEG_POSE_GUIDE.md', 'w', encoding='utf-8') as file:
        file.write(pose_guide)
    
    print("已创建大腿姿势指南: emotion_prompts/LEG_POSE_GUIDE.md")
    updated_count += 1
except Exception as e:
    print(f"创建姿势指南时出错: {str(e)}")
    skipped_count += 1

print(f"\n姿势优化完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。")
print("使用新的WIDE_LEG_POSE.txt文件作为提示词，结合EXTREME_NEGATIVE.txt作为负面提示词。")
print("将CFG值设置为15，步数为45，采样器为DPM++ 2M Karras以确保最佳效果。")
print("查看LEG_POSE_GUIDE.md获取详细的大腿姿势优化技术。") 