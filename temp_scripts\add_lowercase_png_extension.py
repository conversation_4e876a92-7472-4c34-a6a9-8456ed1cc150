#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil

# 目录路径
SOURCE_DIR = "E:/ComfyUI/output/张瑾妍/final"
TARGET_DIR = "E:/ComfyUI/output/张瑾妍/final_png"

def add_lowercase_png_extension():
    """添加小写.png后缀到所有文件"""
    # 确保目标目录存在
    if not os.path.exists(TARGET_DIR):
        os.makedirs(TARGET_DIR)
    
    # 获取所有文件
    files = os.listdir(SOURCE_DIR)
    
    renamed_count = 0
    for file in files:
        if os.path.isfile(os.path.join(SOURCE_DIR, file)):
            # 新文件名（添加小写.png后缀）
            new_name = f"{file}.png"
            
            # 原文件路径和新文件路径
            source_path = os.path.join(SOURCE_DIR, file)
            target_path = os.path.join(TARGET_DIR, new_name)
            
            # 复制文件并使用新名称
            shutil.copy2(source_path, target_path)
            print(f"已处理: {file} -> {new_name}")
            renamed_count += 1
    
    print(f"\n总共处理了 {renamed_count} 个文件，添加了小写.png后缀")
    
    # 显示最终结果
    if os.path.exists(TARGET_DIR):
        final_files = os.listdir(TARGET_DIR)
        final_files.sort()
        
        print("\n最终的文件列表:")
        for file in final_files:
            print(f"- {file}")

if __name__ == "__main__":
    add_lowercase_png_extension()