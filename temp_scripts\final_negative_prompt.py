# 创建特别的负面提示词文件，帮助避免裙子被识别为裤子

negative_prompt = """
pants, trousers, leggings, jeans, slacks, dress pants, sweatpants, capris, shorts, yoga pants,
hands covering body, hands covering intimate areas, hands in frame, arms covering body, 
hands blocking view, fingers hiding, obscured view, covered intimate areas,
single piece outfit, dress, bodysuit, jumpsuit, unitard, one-piece, 
merged clothing, merged undergarment, fused garments, clothing ambiguity,
extra clothing, overdressed, conservative attire, modest clothing,
blurry details, low resolution, poor quality, bad anatomy, 
censored, censorship, blurred, covered, concealed, hidden,
text, watermark, signature, logo
"""

# 写入文件
try:
    with open('emotion_prompts/negative_prompt.txt', 'w', encoding='utf-8') as file:
        file.write(negative_prompt.strip())
    print("已创建负面提示词文件: emotion_prompts/negative_prompt.txt")
except Exception as e:
    print(f"创建负面提示词文件时出错: {str(e)}")

# 创建配套的README文件，提供使用说明
readme_content = """# 提示词使用指南

## 负面提示词
`negative_prompt.txt` 中包含了专门用于避免裙子被识别为裤子的负面提示词。
在ComfyUI中使用时：

1. 将 `negative_prompt.txt` 的内容复制到KSampler节点的负面提示词输入框中
2. 或者使用"Load Text"节点加载该文件，然后连接到CLIPTextEncode节点

## 推荐设置
为确保生成效果最佳：

1. CFG值设置为8-9，强制模型严格遵守提示词
2. 使用支持NSFW内容的模型，如某些特定fine-tuned版本
3. 建议使用DPM++ 2M Karras采样器
4. 步数设置为30-40
5. 全身图像建议分辨率为512x768或768x1024

## 触发文件
`trigger.txt` 包含了特殊的触发词，可以帮助模型更好地理解要生成的内容。
可以选择将其中的某些关键词添加到您的正面提示中。

## 常见问题排除
如果依然出现裙子被识别为裤子的情况：
1. 增加CFG值到9
2. 在正面提示中明确添加"NO PANTS, micro mini skirt, separate white underwear visible"
3. 确保使用了所有的负面提示词
4. 尝试不同的模型或采样器
"""

# 写入README文件
try:
    with open('emotion_prompts/README_USAGE.md', 'w', encoding='utf-8') as file:
        file.write(readme_content)
    print("已创建使用说明文件: emotion_prompts/README_USAGE.md")
except Exception as e:
    print(f"创建说明文件时出错: {str(e)}")

print("\n所有文件创建完成! 请在ComfyUI中使用negative_prompt.txt的内容作为负面提示词，并将CFG值设置为8-9以获得最佳效果。") 