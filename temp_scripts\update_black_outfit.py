import os
import re

# 文件夹路径
folder_path = 'emotion_prompts'

# 当前的上衣描述
current_top_description = "Tight-fitting semi-transparent light grey sports bra with plunging neckline creating deep cleavage, the thin moisture-wicking material stretched across her chest revealing subtle nipple contours, cut extremely short to fully expose her navel and bare midriff, the sporty elasticated band hugging tightly under her breasts enhancing their natural shape."

# 新的上衣描述 - 基于图片中的黑色露肩长袖上衣，带深V
new_top_description = "Form-fitting black off-shoulder crop top with ultra-deep V-neck plunging between her impressive breasts, long sleeves covering arms but leaving shoulders completely bare, the smooth stretchy fabric emphasizing her voluptuous chest while exposing her slender waist and navel, tight enough to outline every curve of her upper body."

# 当前的下装描述
current_bottom_description = "Ultra low-rise nude-colored yoga leggings almost matching her skin tone giving an illusory naked appearance from a distance, sitting perilously low on her hips with the waistband barely covering her pubic area, the center seam creating a pronounced camel toe effect, the ultra-thin almost transparent material molding perfectly to every intimate contour, clearly outlining her anatomy through the tightly stretched flesh-toned fabric."

# 新的下装描述 - 基于图片中的黑色短裙，加入能看到内裤的细节
new_bottom_description = "Extremely short black mini skirt barely covering her upper thighs, riding up when she sits to reveal glimpses of black panties underneath, the tight fabric hugging her curves perfectly while creating a tantalizing high-cut silhouette that emphasizes her long slender legs and shapely hips, often shifting to reveal the edge of her underwear when she moves."

# 补充床上场景的描述
bed_scene_description = "Sitting elegantly on a luxurious white bed with fluffy pillows and pristine sheets, the soft bedding creating a perfect contrast with her black outfit and pale skin."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 统计
updated_count = 0
skipped_count = 0

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 替换上衣描述
            content = content.replace(current_top_description, new_top_description)
            
            # 替换下装描述
            content = content.replace(current_bottom_description, new_bottom_description)
            
            # 分析并添加床上场景描述
            lines = content.split('\n')
            has_bed_scene = False
            
            # 检查是否已经有床的描述
            for line in lines:
                if any(word in line.lower() for word in ["bed", "pillow", "sheet", "bedroom"]):
                    has_bed_scene = True
                    break
            
            # 如果没有床的描述，添加一个
            if not has_bed_scene:
                # 找到一个合适的位置添加场景描述（在衣着描述之后）
                for i, line in enumerate(lines):
                    if new_bottom_description in line:
                        lines.insert(i + 1, bed_scene_description)
                        break
            
            # 重新组合内容
            updated_content = '\n'.join(lines)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print(f"已更新为黑色露肩上衣和短裙: {filename}")
            updated_count += 1
                
        except Exception as e:
            print(f"处理文件时出错 {filename}: {str(e)}")
            skipped_count += 1

print(f"\n更新完成: {updated_count} 个文件已更新，{skipped_count} 个文件已跳过")
print("所有文件已更新，服装风格改为黑色露肩上衣(带深V)和黑色短裙(能看到内裤)，并添加床上场景。")
