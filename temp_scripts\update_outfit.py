import os

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 更新衬衫和裙子描述
old_shirt_description = "Crisp white button-up shirt with collar, sleeves rolled up to mid-forearm."
new_shirt_description = "Crisp white button-up shirt with deep V-neck revealing subtle cleavage, sleeves rolled up to mid-forearm."

old_skirt_description = "Tight black miniskirt hugging hips."
new_skirt_description = "Very short tight black miniskirt barely covering essentials, hugging hips tightly."

# 删除领带描述
tie_description = "Black tie loosely hanging down from collar."

# 更新手部描述（不再提及领带）
old_hand_description = "One hand gently touching collar or tie in thoughtful gesture."
new_hand_description = "One hand gently touching collar or chest in thoughtful gesture."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 删除领带描述行
        lines = content.split('\n')
        lines = [line for line in lines if line != tie_description]
        
        # 重新组合内容
        content = '\n'.join(lines)
        
        # 替换衬衫、裙子和手部描述
        content = content.replace(old_shirt_description, new_shirt_description)
        content = content.replace(old_skirt_description, new_skirt_description)
        content = content.replace(old_hand_description, new_hand_description)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"Updated outfit in: {filename}")

print("All files have been updated with new outfit descriptions (V-neck shirt, shorter skirt, no tie).")