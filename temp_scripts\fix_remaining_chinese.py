#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 要修复的特定文件
FILES_TO_FIX = {
    "emotion_prompts/amusement.txt": [
        (r'修长的手指轻掩嘴角，另', "Slender fingers lightly covering the corner of her mouth, other")
    ],
    "emotion_prompts/approval.txt": [
        (r'伸出修长的拇指，其他手指优雅地卷曲，展示完美的手形', "Extended slender thumb, other fingers elegantly curled, displaying perfect hand shape")
    ],
    "emotion_prompts/disapproval.txt": [
        (r'双臂交叉于胸前，修长的手指轻轻敲击手臂，展示不满', "Arms crossed over her chest, slender fingers lightly tapping against her arm, displaying dissatisfaction")
    ]
}

def fix_file(file_path, replacements):
    """修复文件中的中文内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 进行所有替换
        for chinese_text, english_text in replacements:
            content = content.replace(chinese_text, english_text)
        
        # 检查是否有变化
        if content == original_content:
            print(f"文件 {file_path} 没有变化")
            return False
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已修复文件: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def fix_all_files():
    """修复所有指定的文件"""
    fixed_files = []
    
    for file_path, replacements in FILES_TO_FIX.items():
        if fix_file(file_path, replacements):
            fixed_files.append(os.path.basename(file_path))
    
    print(f"\n总共修复了 {len(fixed_files)} 个文件")
    if fixed_files:
        print("已修复的文件:")
        for file_name in fixed_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    fix_all_files()