#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# 创建示例目录
EXAMPLES_DIR = "aidma_examples"
os.makedirs(EXAMPLES_DIR, exist_ok=True)

# 创建不同色彩和谐的示例

# 1. 浅蓝色背景 + 白色上衣 + 黑色短裙
blue_white_black = """aidma<PERSON><PERSON><PERSON><PERSON><PERSON>, beautiful young woman, jingyan, 
standing in a clean minimalist interior with soft powder blue background,
modern pristine studio setting,
serene atmosphere with soft even lighting,
wearing a crisp white deep V-neck top that dramatically plunges to reveal cleavage,
paired with a glossy black ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by <PERSON> style,
fashion portrait composition,
professional studio photography aesthetic,
elegant color harmony with blue-white-black palette,
contemporary minimalist setting with pristine studio ambience."""

# 2. 米色背景 + 深红上衣 + 黑色短裙
beige_red_black = """aidma<PERSON><PERSON><PERSON><PERSON><PERSON>, beautiful young woman, jingyan, 
standing in a clean minimalist interior with warm beige background,
modern pristine studio setting,
serene atmosphere with soft even lighting,
wearing a deep burgundy red deep V-neck top that dramatically plunges to reveal cleavage,
paired with a glossy black ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
rich color harmony with beige-burgundy-black palette,
contemporary minimalist setting with pristine studio ambience."""

# 3. 淡绿色背景 + 黑色上衣 + 白色短裙
mint_black_white = """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing in a clean minimalist interior with soft mint green background,
modern pristine studio setting,
serene atmosphere with soft even lighting,
wearing a sleek black deep V-neck top that dramatically plunges to reveal cleavage,
paired with a pristine white ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
fresh color harmony with mint-black-white palette,
contemporary minimalist setting with pristine studio ambience."""

# 4. 深灰色背景 + 粉色上衣 + 白色短裙
grey_pink_white = """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing in a clean minimalist interior with deep charcoal grey background,
modern pristine studio setting,
serene atmosphere with soft even lighting,
wearing a soft blush pink deep V-neck top that dramatically plunges to reveal cleavage,
paired with a crisp white ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
sophisticated color harmony with grey-pink-white palette,
contemporary minimalist setting with pristine studio ambience."""

# 写入示例文件
examples = [
    {"filename": "blue_white_black.txt", "content": blue_white_black},
    {"filename": "beige_red_black.txt", "content": beige_red_black},
    {"filename": "mint_black_white.txt", "content": mint_black_white},
    {"filename": "grey_pink_white.txt", "content": grey_pink_white}
]

for example in examples:
    filepath = os.path.join(EXAMPLES_DIR, example["filename"])
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(example["content"])
    print(f"已创建色彩和谐示例: {filepath}")

# 创建扩展的色彩指南
color_guide = """# 室内纯净背景色彩和谐指南

## 色彩和谐基本原则
- 保持背景与服装的色彩和谐是创建专业时尚照片的关键
- 使用互补色、三元色、或类似色方案可以创造不同的视觉效果
- 善用对比度突出主体，但避免过于鲜艳的色调喧宾夺主
- 保持简洁，通常不超过3种主要色调

## 经典色彩组合

### 浅蓝色背景 + 白色上衣 + 黑色短裙
- 清新、现代、专业的视觉效果
- 蓝色背景营造冷静、沉稳的氛围
- 黑白服装提供强烈对比，突出人物
- 适合正式、商业或时尚风格的照片

### 米色背景 + 深红上衣 + 黑色短裙
- 温暖、高贵、成熟的视觉效果
- 米色背景营造柔和、温馨的氛围
- 深红色上衣成为视觉焦点，展现热情
- 黑色短裙增添稳重感，平衡整体色调
- 适合优雅、成熟或浪漫风格的照片

### 淡绿色背景 + 黑色上衣 + 白色短裙
- 清新、自然、活力的视觉效果
- 淡绿色背景营造自然、健康的氛围
- 黑白服装与背景形成鲜明对比
- 整体色调给人春天、新生的感觉
- 适合年轻、活力或自然风格的照片

### 深灰色背景 + 粉色上衣 + 白色短裙
- 优雅、时尚、现代的视觉效果
- 深灰色背景营造高级、专业的氛围
- 粉色上衣增添女性化和温柔感
- 白色短裙提供平衡和清爽感
- 适合高端时尚、都市或现代风格的照片

## 色彩选择技巧
1. 考虑人物肤色 - 选择能够衬托肤色的背景和服装色调
2. 考虑情绪表达 - 不同色调能够唤起不同情绪反应
3. 考虑视觉重点 - 使用色彩引导观者的视线
4. 保持一致性 - 确保所有元素在色调上和谐统一

## 提示词中的色彩描述
- 准确使用色彩词汇：如"powder blue"比简单的"blue"更具体
- 添加质感描述：如"glossy black"、"crisp white"增强视觉感受
- 明确指出色彩组合："color harmony with grey-pink-white palette"
- 使用情绪化的色彩词汇："warm beige"、"soft blush pink"等

## 色彩组合示例文件
- `blue_white_black.txt` - 清新现代的蓝白黑配色
- `beige_red_black.txt` - 温暖高贵的米色深红黑配色
- `mint_black_white.txt` - 清新自然的薄荷绿黑白配色
- `grey_pink_white.txt` - 优雅时尚的灰粉白配色"""

# 写入色彩指南文件
color_guide_path = os.path.join(EXAMPLES_DIR, "COLOR_HARMONY_GUIDE.md")
with open(color_guide_path, 'w', encoding='utf-8') as f:
    f.write(color_guide)
print(f"已创建色彩和谐指南: {color_guide_path}")

print("\n所有色彩和谐示例和指南创建完成！")
print("使用这些示例参考不同的色彩搭配方案。") 