#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

def fix_file_formatting(file_path):
    """修复文件的格式问题"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 1. 修复多余的句点（例如：",."）
        content = re.sub(r',\.', r'.', content)
        
        # 2. 修复"head to toe"、"entire figure"、"body language"后缺少句点的问题
        for phrase in ["head to toe", "entire figure", "body language"]:
            pattern = r'({0})(?!\.)(?=\s+[a-zA-Z])'.format(re.escape(phrase))
            content = re.sub(pattern, r'\1.', content)
        
        # 3. 修复"High"和"Ultra"前缺少句点的问题
        content = re.sub(r'(?<!\.)(?<!\s\.)(?<!\.)\s+(High\s+resolution)', r'. \1', content)
        content = re.sub(r'(?<!\.)(?<!\s\.)(?<!\.)\s+(Ultra\s+high)', r'. \1', content)
        
        # 4. 修复句点后空格后小写字母的问题（替换为大写字母）
        def capitalize_after_period(match):
            period = match.group(1)
            space = match.group(2)
            lowercase = match.group(3)
            return period + space + lowercase.upper()
        
        content = re.sub(r'(\.)(\s+)([a-z])', capitalize_after_period, content)
        
        # 5. 修复小写字母后接大写字母的问题（可能缺少句点）
        def add_period_between_lowercase_uppercase(match):
            lowercase = match.group(1)
            space = match.group(2)
            uppercase = match.group(3)
            return lowercase + '. ' + uppercase
        
        content = re.sub(r'([a-z])(\s+)([A-Z])', add_period_between_lowercase_uppercase, content)
        
        # 6. 修复可能出现的多个连续句点
        content = re.sub(r'\.{2,}', '.', content)
        
        # 7. 确保"photograph,"后面的句点正确
        content = re.sub(r'photograph,\s*\.', r'photograph.', content)
        
        # 8. 确保aidmaNSFWunlock前有正确的空格和句点
        if "aidmaNSFWunlock" in content:
            content = re.sub(r'([^\.]\s+)aidmaNSFWunlock', r'\1. aidmaNSFWunlock', content)
            content = re.sub(r'\.+\s*\.\s*aidmaNSFWunlock', r'. aidmaNSFWunlock', content)
        
        # 检查内容是否有变化
        if content != original_content:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            return True, content
        else:
            return False, content
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False, None

def fix_all_files():
    """修复所有表情文件的格式"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append((file_name, os.path.join(EMOTION_PROMPTS_DIR, file_name)))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要处理")
    
    # 修复所有文件
    files_updated = []
    files_skipped = []
    
    for file_name, file_path in emotion_files:
        print(f"处理文件: {file_name}")
        updated, content = fix_file_formatting(file_path)
        if updated:
            files_updated.append(file_name)
            print(f"  - 已更新文件格式")
            
            # 显示前100个字符，检查更新效果
            if content:
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"  - 更新后内容预览: {preview}")
        else:
            files_skipped.append(file_name)
            print(f"  - 无需更新格式")
    
    print(f"\n格式修复完成！")
    print(f"已更新 {len(files_updated)} 个文件")
    print(f"跳过 {len(files_skipped)} 个文件")
    
    if files_updated:
        print("\n更新的文件列表:")
        for file_name in files_updated:
            print(f"  - {file_name}")
    
    if files_skipped:
        print("\n跳过的文件列表:")
        for file_name in files_skipped:
            print(f"  - {file_name}")

if __name__ == "__main__":
    fix_all_files() 