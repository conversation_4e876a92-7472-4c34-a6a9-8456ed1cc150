@echo off
REM ============================================================================
REM ComfyUI V100 SXM2 16GB Optimized Startup Script
REM ============================================================================

setlocal enabledelayedexpansion

echo ============================================================================
echo                ComfyUI V100 SXM2 16GB Optimized Startup
echo ============================================================================
echo.

REM GPU Performance Mode Selection
echo [GPU MODE] Select V100 SXM2 16GB optimization profile:
echo.
echo 1. Balanced Mode        (Recommended: 14GB VRAM, stable performance)
echo 2. Maximum VRAM Mode    (15GB VRAM, aggressive memory management)
echo 3. Flux Optimized       (Optimized for Flux models with Flash Attention)
echo 4. Video Generation     (Optimized for WAN2.1 video generation)
echo 5. Standard Mode        (General V100 configuration)
echo.
set /p "choice=Select optimization mode (1-5, Enter for default 1): "
if "%choice%"=="" set choice=1

REM Set V100 SXM2 specific optimizations based on choice
if "!choice!"=="1" (
    set "GPU_ARGS=--normalvram --reserve-vram 2.0 --fp16-unet --fp32-vae --fp16-text-enc --disable-cuda-malloc --async-offload"
    set "PREVIEW_ARGS=--preview-method auto"
    echo [GPU] Balanced Mode: 14GB VRAM, 2GB reserved, FP16 UNet, FP32 VAE
) else if "!choice!"=="2" (
    set "GPU_ARGS=--normalvram --reserve-vram 0.5 --fp16-unet --fp16-vae --fp16-text-enc --disable-cuda-malloc --async-offload --cache-none"
    set "PREVIEW_ARGS=--preview-method latent2rgb"
    echo [GPU] Maximum VRAM Mode: 15GB VRAM, aggressive memory management
) else if "!choice!"=="3" (
    set "GPU_ARGS=--normalvram --reserve-vram 1.0 --fp16-unet --fp32-vae --fp16-text-enc --disable-cuda-malloc --async-offload --cache-lru 5"
    set "PREVIEW_ARGS=--preview-method auto --use-pytorch-cross-attention"
    echo [GPU] Flux Optimized: Flash Attention, LRU cache, optimized for Flux models
) else if "!choice!"=="4" (
    set "GPU_ARGS=--normalvram --reserve-vram 3.0 --fp16-unet --fp32-vae --fp16-text-enc --disable-cuda-malloc --async-offload --cache-none"
    set "PREVIEW_ARGS=--preview-method latent2rgb"
    echo [GPU] Video Generation: Optimized for WAN2.1 video generation
) else (
    set "GPU_ARGS=--normalvram --reserve-vram 2.0 --fp16-unet --fp32-vae --disable-cuda-malloc"
    set "PREVIEW_ARGS=--preview-method auto"
    echo [GPU] Standard Mode: General V100 configuration
)

echo.
echo [INFO] Activating ComfyUI conda environment...
call conda activate comfyui
if errorlevel 1 (
    echo [ERROR] Failed to activate conda environment
    pause
    exit /b 1
)

echo [INFO] Starting ComfyUI with V100 SXM2 optimizations...
echo [INFO] GPU Arguments: !GPU_ARGS!
echo [INFO] Preview Arguments: !PREVIEW_ARGS!
echo [INFO] Access URL: http://localhost:8188
echo [INFO] To stop ComfyUI, press Ctrl+C
echo.

REM Start ComfyUI with V100 SXM2 specific optimizations
python main.py --listen 0.0.0.0 --port 8188 !GPU_ARGS! !PREVIEW_ARGS!

echo.
echo [INFO] ComfyUI has been stopped
pause
