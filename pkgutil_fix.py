# pkgutil compatibility fix for Python 3.12+
# This module provides backward compatibility for packages that still use pkgutil.ImpImporter

import pkgutil
import importlib.util
import sys

class ImpImporter:
    """Compatibility class for pkgutil.ImpImporter removed in Python 3.12"""

    def __init__(self, path=None):
        self.path = path

    def find_module(self, fullname, path=None):
        """Find module using modern importlib"""
        try:
            spec = importlib.util.find_spec(fullname, path)
            if spec is not None:
                return ImpLoader(spec)
        except (ImportError, ValueError, ModuleNotFoundError):
            pass
        return None

class ImpLoader:
    """Compatibility loader class"""

    def __init__(self, spec):
        self.spec = spec

    def load_module(self, fullname):
        """Load module using modern importlib"""
        if fullname in sys.modules:
            return sys.modules[fullname]

        module = importlib.util.module_from_spec(self.spec)
        sys.modules[fullname] = module
        try:
            self.spec.loader.exec_module(module)
        except Exception:
            if fullname in sys.modules:
                del sys.modules[fullname]
            raise
        return module

# Apply the fix
if not hasattr(pkgutil, 'ImpImporter'):
    pkgutil.ImpImporter = ImpImporter
    print("[FIX] Applied pkgutil.ImpImporter compatibility patch for Python 3.12+")
