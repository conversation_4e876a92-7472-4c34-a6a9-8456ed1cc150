#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 人物姓名
CHARACTER_NAME = "张瑾妍"

# 角色描述
CHARACTER_DESCRIPTION = f"""
{CHARACTER_NAME}，23岁，现代酒馆的人气女招待。时尚亮丽，举止优雅，站在典雅的酒馆环境中。她身着深蓝色牛仔衬衫和白色超短裙，搭配黑色丝袜，展现出迷人的曲线。她的深棕色长发带着微卷的波浪，侧分的刘海巧妙地勾勒出她的脸庞。微微敞开的牛仔衬衫和短裙组合展现了她的时尚品味，而黑色丝袜则增添了几分优雅。她站在柔和的酒馆灯光下，木质装饰与酒架在背景中若隐若现，营造出温馨而神秘的氛围。
"""

# 开场第一段文字
OPENING_PARAGRAPH = f"""
当你踏入古色古香的酒馆，目光不由自主地被吧台后那位优雅的女招待所吸引。{CHARACTER_NAME}正以熟练的动作调制一杯复杂的鸡尾酒，她的手法如同在进行一场精妙的舞蹈。注意到你的到来，她抬起头，嘴角微微上扬，眼中闪烁着好奇与友善的光芒。"欢迎光临，"她轻声说道，声音如同醇厚的威士忌般令人沉醉，"今天想要尝试什么特别的吗？"
"""

def update_emotion_file(file_path, emotion_name):
    """更新表情文件，添加角色描述和开场文字"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 检查文件是否已经包含角色姓名
        if CHARACTER_NAME in content:
            # 已经包含角色姓名，可能已经更新过
            print(f"文件 {file_path} 已包含角色姓名，跳过")
            return False
        
        # 找到开头添加角色姓名（替换jingyan)
        content = content.replace("jingyan", CHARACTER_NAME)
        
        # 根据表情定制开场文字
        custom_opening = customize_opening(OPENING_PARAGRAPH, emotion_name)
        
        # 创建新的内容
        new_content = f"{content}\n\n角色描述：{CHARACTER_DESCRIPTION}\n\n开场白：{custom_opening}"
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(new_content)
        
        print(f"已更新文件: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def customize_opening(opening, emotion):
    """根据表情定制开场文字"""
    # 基于不同的表情调整开场白的细节
    emotion_adjustments = {
        "joy": "她的笑容灿烂如阳光，整个人散发着欢乐的气息。",
        "sadness": "她强忍着内心的悲伤，勉强挤出一丝微笑。",
        "anger": "她眼中闪烁着隐忍的怒火，却保持着专业的微笑。",
        "fear": "她眼神中透露出一丝不安，似乎在担忧着什么。",
        "surprise": "她眼睛微微睁大，显然对你的到来感到惊讶。",
        "disgust": "她眉头微皱，似乎在极力掩饰内心的不适。",
        "love": "她看向你的眼神中带着温柔与爱意，让人心跳加速。",
        "admiration": "她眼中流露出赞赏的光芒，似乎对你印象深刻。",
        "amusement": "她嘴角上扬，眼中闪烁着愉快的笑意。",
        "annoyance": "她眉头微蹙，似乎有什么事情让她感到烦扰。",
        "approval": "她轻轻点头，眼神中透露出赞同与认可。",
        "caring": "她眼中流露出关切的目光，温暖而体贴。",
        "confusion": "她微微歪头，眼中带着困惑的神情。",
        "curiosity": "她目光中闪烁着好奇的光芒，似乎对你充满兴趣。",
        "desire": "她眼神中带着隐晦的渴望，让人心神不宁。",
        "disappointment": "她眼中闪过一丝失落，但很快就恢复了平静。",
        "disapproval": "她微微皱眉，眼神中带着不认同的意味。",
        "embarrassment": "她脸颊微红，眼神有些闪躲，明显感到尴尬。",
        "excitement": "她眼中闪烁着兴奋的光芒，整个人都显得活力四射。",
        "gratitude": "她眼中流露出感激的神情，让人感到温暖。",
        "grief": "她眼中隐藏着深深的悲痛，却努力保持着专业的微笑。",
        "nervousness": "她手指微微颤抖，眼神中透露出一丝紧张。",
        "neutral": "她表情平静，眼神中看不出特别的情绪。",
        "optimism": "她眼中充满希望的光芒，整个人散发着积极的能量。",
        "pride": "她挺直腰背，眼神中透露出自豪与尊严。",
        "realization": "她眼中闪过一丝顿悟的光芒，似乎刚刚明白了什么。",
        "relief": "她轻轻呼出一口气，眼中的紧张被释然所取代。",
        "remorse": "她眼中带着歉疚的神色，似乎为某事感到后悔。"
    }
    
    # 获取表情调整，如果没有找到对应的表情，则使用默认文本
    emotion_text = emotion_adjustments.get(emotion, "")
    
    # 如果有情绪调整，添加到开场白中
    if emotion_text:
        # 在第一句话后添加情绪描述
        parts = opening.split('。', 1)
        if len(parts) > 1:
            return f"{parts[0]}。 {emotion_text} {parts[1]}"
        else:
            return f"{opening} {emotion_text}"
    
    return opening

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(file_name)
    
    print(f"找到 {len(emotion_files)} 个表情文件需要更新")
    
    # 更新所有表情文件
    updated_files = []
    
    for file_name in emotion_files:
        # 提取表情名（去掉.txt后缀）
        emotion_name = file_name.replace(".txt", "")
        file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
        
        if update_emotion_file(file_path, emotion_name):
            updated_files.append(file_name)
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    update_all_emotion_files() 