#!/usr/bin/env python3
"""
V100 INT4模型加载器
支持多种量化格式的INT4模型
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import gc

class V100INT4Loader:
    """V100专用INT4模型加载器"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.models = {}
        
    def load_bnb_int4_model(self, model_path, **kwargs):
        """使用BitsAndBytes加载INT4模型"""
        print(f"使用BitsAndBytes加载INT4模型: {model_path}")
        
        # BitsAndBytes配置
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            llm_int8_enable_fp32_cpu_offload=True
        )
        
        try:
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                quantization_config=bnb_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                **kwargs
            )
            
            tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            
            print(f"✓ 模型加载成功，显存使用: {torch.cuda.memory_allocated()/1024**3:.2f}GB")
            
            return model, tokenizer
            
        except Exception as e:
            print(f"✗ BitsAndBytes加载失败: {e}")
            return None, None
    
    def load_gptq_model(self, model_path, **kwargs):
        """加载GPTQ量化模型"""
        print(f"加载GPTQ模型: {model_path}")
        
        try:
            from auto_gptq import AutoGPTQForCausalLM
            
            model = AutoGPTQForCausalLM.from_quantized(
                model_path,
                device="cuda:0",
                use_triton=False,  # V100不支持Triton
                use_safetensors=True,
                trust_remote_code=True,
                **kwargs
            )
            
            tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            
            print(f"✓ GPTQ模型加载成功")
            return model, tokenizer
            
        except ImportError:
            print("✗ auto-gptq未安装，请运行: pip install auto-gptq")
            return None, None
        except Exception as e:
            print(f"✗ GPTQ加载失败: {e}")
            return None, None
    
    def load_awq_model(self, model_path, **kwargs):
        """加载AWQ量化模型"""
        print(f"加载AWQ模型: {model_path}")
        
        try:
            from awq import AutoAWQForCausalLM
            
            model = AutoAWQForCausalLM.from_quantized(
                model_path,
                fuse_layers=True,
                trust_remote_code=True,
                safetensors=True,
                **kwargs
            )
            
            tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            
            print(f"✓ AWQ模型加载成功")
            return model, tokenizer
            
        except ImportError:
            print("✗ autoawq未安装，请运行: pip install autoawq")
            return None, None
        except Exception as e:
            print(f"✗ AWQ加载失败: {e}")
            return None, None
    
    def auto_load_int4_model(self, model_path, **kwargs):
        """自动检测并加载INT4模型"""
        print(f"自动检测INT4模型类型: {model_path}")
        
        # 检测模型类型
        if os.path.exists(os.path.join(model_path, "quantize_config.json")):
            print("检测到GPTQ模型")
            return self.load_gptq_model(model_path, **kwargs)
        elif os.path.exists(os.path.join(model_path, "quant_config.json")):
            print("检测到AWQ模型")
            return self.load_awq_model(model_path, **kwargs)
        else:
            print("使用BitsAndBytes进行动态量化")
            return self.load_bnb_int4_model(model_path, **kwargs)
    
    def clear_memory(self):
        """清理显存"""
        for model_name in list(self.models.keys()):
            del self.models[model_name]
        
        gc.collect()
        torch.cuda.empty_cache()
        print("✓ 显存已清理")

# 使用示例
if __name__ == "__main__":
    loader = V100INT4Loader()
    
    # 示例：加载模型
    # model, tokenizer = loader.auto_load_int4_model("path/to/your/int4/model")
    
    print("V100 INT4模型加载器已准备就绪")
