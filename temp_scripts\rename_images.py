#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import shutil
from datetime import datetime
from PIL import Image

# 设置路径
OUTPUT_DIR = "output/沈清宜"
TARGET_DIR = "output_renamed"
EMOTION_DIR = "emotion_prompts"

def get_emotion_files():
    """获取所有情感文件的名称，按字母顺序排序"""
    emotions = []
    for file_name in os.listdir(EMOTION_DIR):
        if file_name.endswith(".txt"):
            emotion_name = file_name.replace(".txt", "")
            emotions.append(emotion_name)
    return sorted(emotions)

def create_directory(directory):
    """创建目录，如果目录已存在则跳过"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def get_image_info(image_path):
    """获取图像的创建时间和尺寸"""
    creation_time = os.path.getctime(image_path)
    with Image.open(image_path) as img:
        width, height = img.size
    return creation_time, (width, height)

def rename_images():
    """重命名图像文件"""
    # 确保目标目录存在
    create_directory(TARGET_DIR)
    
    # 获取所有情感类型
    emotions = get_emotion_files()
    print(f"找到 {len(emotions)} 种情感类型: {', '.join(emotions)}")
    
    # 获取所有PNG图像
    png_files = []
    for file_name in os.listdir(OUTPUT_DIR):
        file_path = os.path.join(OUTPUT_DIR, file_name)
        if file_name.endswith(".png") and os.path.isfile(file_path):
            # 移除对文件名前缀的限制，处理所有PNG文件
            creation_time, size = get_image_info(file_path)
            png_files.append((file_name, file_path, creation_time, size))
    
    # 按创建时间排序
    png_files.sort(key=lambda x: x[2])
    
    # 如果图像数量与情感类型数量不匹配，则给出警告
    if len(png_files) != len(emotions):
        print(f"警告: PNG文件数量 ({len(png_files)}) 与情感类型数量 ({len(emotions)}) 不匹配")
        print("将按时间顺序重命名文件，可能需要手动调整")
    
    # 映射情感与图像
    emotion_images = {}
    for i, emotion in enumerate(emotions):
        if i < len(png_files):
            emotion_images[emotion] = png_files[i]
    
    # 重命名并复制文件
    copied_files = []
    for emotion, (old_name, old_path, creation_time, size) in emotion_images.items():
        # 提取编号
        match = re.search(r'(\d+)', old_name)
        number = match.group(1) if match else "00"
        
        # 创建新文件名
        creation_date = datetime.fromtimestamp(creation_time).strftime('%Y%m%d')
        new_name = f"{emotion}.png"
        new_path = os.path.join(TARGET_DIR, new_name)
        
        # 复制文件
        try:
            shutil.copy2(old_path, new_path)
            copied_files.append((emotion, old_name, new_name))
            print(f"已复制并重命名: {old_name} → {new_name}")
        except Exception as e:
            print(f"复制文件时出错: {old_name} → {new_name}: {str(e)}")
    
    # 生成重命名报告
    print("\n重命名完成，共处理 {} 个文件".format(len(copied_files)))
    print("\n对应关系:")
    for emotion, old_name, new_name in copied_files:
        print(f"  {emotion}: {old_name} → {new_name}")

def main():
    rename_images()

if __name__ == "__main__":
    main()
