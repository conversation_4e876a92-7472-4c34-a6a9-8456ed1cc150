import os
import re

# 新的着装和风格模板
style_updates = {
    "glasses": {
        "from": r"Wearing stylish red-framed glasses that accentuate her expressive features\.",
        "to": "Wearing thin-framed round black glasses that give an intellectual look."
    },
    "shirt": {
        "from": r"Wearing crisp white button-up shirt with loosened tie at collar\.",
        "to": "Crisp white button-up shirt with collar, sleeves rolled up to mid-forearm."
    },
    "tie": {
        "from": "",  # 新增
        "to": "Black tie loosely hanging down from collar."
    },
    "skirt": {
        "from": r"Tight black pencil skirt hugging hips and thighs\.",
        "to": "Tight black miniskirt hugging hips."
    },
    "stockings": {
        "from": r"Sheer black stockings on legs with subtle sheen\.",
        "to": "Sheer black pantyhose showing legs with subtle shine."
    },
    "hair": {
        "from": r"(?:Perfectly styled hair framing face delicately\.|Hair.*|Neatly styled hair.*)",
        "to": "Long straight black hair with neat bangs across forehead."
    },
    "hands": {
        "from": r"Hands crossed behind her back, not visible from front view\.",
        "to": "One hand gently touching collar or tie in thoughtful gesture."
    },
    "lighting": {
        "from": r"(?:.*lighting.*with subtle reflection on red glasses\.|.*lighting.*)",
        "to": "Soft natural lighting in bright minimalist interior setting."
    }
}

# 已经更新的文件列表
updated_files = ['admiration.txt', 'joy.txt', 'neutral.txt', 'README.md', 'admiration_glasses.txt']

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 获取所有需要更新的文件
files_to_update = []
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in updated_files:
        files_to_update.append(os.path.join(folder_path, filename))

# 更新每个文件
for file_path in files_to_update:
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # 应用所有更新
    for update_key, update_info in style_updates.items():
        from_pattern = update_info["from"]
        to_text = update_info["to"]
        
        if from_pattern:
            # 替换现有内容
            content = re.sub(from_pattern, to_text, content)
        else:
            # 特殊情况：为领带添加新行（如果尚未存在）
            if update_key == "tie" and "Black tie loosely hanging down from collar." not in content:
                # 在衬衫行后添加领带行
                content = content.replace(
                    "Crisp white button-up shirt with collar, sleeves rolled up to mid-forearm.",
                    "Crisp white button-up shirt with collar, sleeves rolled up to mid-forearm.\nBlack tie loosely hanging down from collar."
                )
    
    # 确保眼睛描述包含"through lenses"
    if "through lenses" not in content and "eye" in content.lower():
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "eye" in line.lower() and "through lenses" not in line.lower():
                if line.endswith('.'):
                    lines[i] = line[:-1] + " through lenses."
                else:
                    lines[i] = line + " through lenses."
        content = '\n'.join(lines)
    
    # 处理手的特殊情况 - 删除任何其他手部描述
    lines = content.split('\n')
    new_lines = []
    has_hand_line = False
    
    for line in lines:
        # 跳过其他手/手臂描述行
        if ("hand" in line.lower() or "arm" in line.lower()) and "One hand gently touching collar" not in line:
            continue
        elif "One hand gently touching collar" in line:
            has_hand_line = True
            new_lines.append(line)
        else:
            new_lines.append(line)
    
    # 如果没有添加手部描述，添加一个
    if not has_hand_line:
        # 在适当的位置添加手部描述（在发型描述后）
        for i, line in enumerate(new_lines):
            if "Long straight black hair with neat bangs" in line:
                new_lines.insert(i+1, "One hand gently touching collar or tie in thoughtful gesture.")
                break
    
    content = '\n'.join(new_lines)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"Updated: {os.path.basename(file_path)}")

print("All remaining emotion files have been updated.")