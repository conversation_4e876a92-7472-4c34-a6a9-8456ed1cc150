{# TEMPLATE VAR SETTINGS #}
{%- set url_root = pathto('', 1) %}
{%- if url_root == '#' %}{% set url_root = '' %}{% endif %}
{%- if not embedded and docstitle %}
{%- set titlesuffix = " | "|safe + docstitle|e %}
{%- else %}
{%- set titlesuffix = "" %}
{%- endif %}
{%- set lang_attr = 'en' if language == None else (language | replace('_', '-')) %}
{% import 'theme_variables.jinja' as theme_variables %}

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="{{ lang_attr }}" > <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" lang="{{ lang_attr }}">
<!--<![endif]-->

<head>
  <meta charset="utf-8">
  {{ metatags }}
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  {% block htmltitle %}
  <title>{{ title|striptags|e }}{{ titlesuffix }}</title>
  {% endblock %}
  <script src="{{ pathto('_static/js/ga.js', 1) }}"></script>
  <script src="{{ pathto('_static/js/redirect.js', 1) }}"></script>
  {# FAVICON #}
  <link rel="shortcut icon" href="{{ pathto('_static/images/favicon.png', 1) }}" />
  {# CANONICAL URL #}
  {% if theme_canonical_url %}
  <link rel="canonical" href="{{ theme_canonical_url }}{{ pagename }}.html" />
  {% endif %}
  <meta property="og:title" content="{{ title|striptags|e }}{{ titlesuffix }}">
  <meta name="description" content="{{ theme_variables.og['description'] }}">
  <meta property="og:description" content="{{ theme_variables.og['description'] }}">
  <!--<meta property="og:image" content="https://mmf.sh/img/logo.png">-->
  <!--<meta property="twitter:image" content="https://mmf.sh/img/logo.png">-->
  <meta name="twitter:image:alt" content="Image for xFormers">
  <meta name="twitter:card" content="summary_large_image">
  {# CSS #}

  {# OPENSEARCH #}
  {% if not embedded %}
  {% if use_opensearch %}
  <link rel="search" type="application/opensearchdescription+xml"
    title="{% trans docstitle=docstitle|e %}Search within {{ docstitle }}{% endtrans %}"
    href="{{ pathto('_static/opensearch.xml', 1) }}" />
  {% endif %}

  {% endif %}

  <link rel="stylesheet" href="{{ pathto('_static/' + style, 1) }}" type="text/css" />
  <!-- <link rel="stylesheet" href="{{ pathto('_static/pygments.css', 1) }}" type="text/css" /> -->
  {%- for css in css_files %}
  {%- if css|attr("rel") %}
  <link rel="{{ css.rel }}" href="{{ pathto(css.filename, 1) }}" type="text/css" {% if css.title is not none %}
    title="{{ css.title }}" {% endif %} />
  {%- else %}
  <link rel="stylesheet" href="{{ pathto(css, 1) }}" type="text/css" />
  {%- endif %}
  {%- endfor %}
  {%- for cssfile in extra_css_files %}
  <link rel="stylesheet" href="{{ pathto(cssfile, 1) }}" type="text/css" />
  {%- endfor %}

  {%- block linktags %}
  {%- if hasdoc('about') %}
  <link rel="author" title="{{ _('About these documents') }}" href="{{ pathto('about') }}" />
  {%- endif %}
  {%- if hasdoc('genindex') %}
  <link rel="index" title="{{ _('Index') }}" href="{{ pathto('genindex') }}" />
  {%- endif %}
  {%- if hasdoc('search') %}
  <link rel="search" title="{{ _('Search') }}" href="{{ pathto('search') }}" />
  {%- endif %}
  {%- if hasdoc('copyright') %}
  <link rel="copyright" title="{{ _('Copyright') }}" href="{{ pathto('copyright') }}" />
  {%- endif %}
  {%- if next %}
  <link rel="next" title="{{ next.title|striptags|e }}" href="{{ next.link|e }}" />
  {%- endif %}
  {%- if prev %}
  <link rel="prev" title="{{ prev.title|striptags|e }}" href="{{ prev.link|e }}" />
  {%- endif %}
  {%- endblock %}
  {%- block extrahead %} {% endblock %}

  {# Keep modernizr in head - http://modernizr.com/docs/#installing #}
  <script src="{{ pathto('_static/js/modernizr.min.js', 1) }}"></script>

  {% include "fonts.html" %}
</head>

<div class="container-fluid header-holder tutorials-header" id="header-holder">
  <div class="container">
    <div class="header-container">
      <a class="header-logo" href="{{ theme_variables.external_urls['home'] }}"><img
          src="{{ pathto('_static/logo.png', 1) }}" style="padding-right: 90px;" width="280px" height="53px"></a>

      <div class="main-menu">
        <ul>
          <li>
            <a href="{{ theme_variables.external_urls['github'] }}"> xFormers Github</a>
          </li>
        </ul>
      </div>

      <a class="main-menu-open-button" href="#" data-behavior="open-mobile-menu"></a>
    </div>

  </div>
</div>


<body class="pytorch-body">

  {% block extrabody %} {% endblock %}

  {# SIDE NAV, TOGGLES ON MOBILE #}

  <div class="table-of-contents-link-wrapper">
    <span>Table of Contents</span>
    <a href="#" class="toggle-table-of-contents" data-behavior="toggle-table-of-contents"></a>
  </div>

  <nav data-toggle="wy-nav-shift" class="pytorch-left-menu" id="pytorch-left-menu">
    <div class="pytorch-side-scroll">
      <div class="pytorch-menu pytorch-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        <div class="pytorch-left-menu-search">
          {% block sidebartitle %}

          {% if theme_display_version %}
          {%- set nav_version = version %}
          {% if READTHEDOCS and current_version %}
          {%- set nav_version = current_version %}
          {% endif %}
          {% if nav_version %}
          <div class="version">
            {{ nav_version }}
          </div>
          {% endif %}
          {% endif %}

          {% include "searchbox.html" %}

          {% endblock %}
        </div>

        {% block menu %}
        {#
        The singlehtml builder doesn't handle this toctree call when the
        toctree is empty. Skip building this for now.
        #}
        {% if 'singlehtml' not in builder %}
        {% set global_toc = toctree(maxdepth=1,
        collapse=theme_collapse_navigation|tobool,
        includehidden=theme_includehidden|tobool,
        titles_only=theme_titles_only|tobool) %}
        {% endif %}
        {% if global_toc %}
        {{ global_toc }}
        {% else %}
        <!-- Local TOC -->
        <div class="local-toc">{{ toc }}</div>
        {% endif %}
        {% endblock %}
      </div>
    </div>
  </nav>

  <div class="pytorch-container">
    <div class="pytorch-page-level-bar" id="pytorch-page-level-bar">
      <div class="pytorch-breadcrumbs-wrapper">
        {% include "breadcrumbs.html" %}
      </div>

      <div class="pytorch-shortcuts-wrapper" id="pytorch-shortcuts-wrapper">
        Shortcuts
      </div>
    </div>

    <section data-toggle="wy-nav-shift" id="pytorch-content-wrap" class="pytorch-content-wrap">
      <div class="pytorch-content-left">

        {% if theme_pytorch_project == 'tutorials' %}

        <div class="pytorch-call-to-action-links">
          <div id="tutorial-type">{{ pagename }}</div>

          <div id="google-colab-link">
            <img class="call-to-action-img" src="{{ pathto('_static/images/pytorch-colab.svg', 1) }}" />
            <div class="call-to-action-desktop-view">Run in Google Colab</div>
            <div class="call-to-action-mobile-view">Colab</div>
          </div>
          <div id="download-notebook-link">
            <img class="call-to-action-notebook-img" src="{{ pathto('_static/images/pytorch-download.svg', 1) }}" />
            <div class="call-to-action-desktop-view">Download Notebook</div>
            <div class="call-to-action-mobile-view">Notebook</div>
          </div>
          <div id="github-view-link">
            <img class="call-to-action-img" src="{{ pathto('_static/images/pytorch-github.svg', 1) }}" />
            <div class="call-to-action-desktop-view">View on GitHub</div>
            <div class="call-to-action-mobile-view">GitHub</div>
          </div>
        </div>

        {% endif %}

        {%- block content %}
        {% if theme_style_external_links|tobool %}
        <div class="rst-content style-external-links">
          {% else %}
          <div class="rst-content">
            {% endif %}
            <div role="main" class="main-content" itemscope="itemscope" itemtype="http://schema.org/Article">
              {%- block document %}
              <article itemprop="articleBody" id="pytorch-article" class="pytorch-article">
                {% block body %}{% endblock %}
              </article>
              {% if self.comments()|trim %}
              <div class="articleComments">
                {% block comments %}{% endblock %}
              </div>
              {% endif%}
            </div>
            {%- endblock %}
            {% include "footer.html" %}
          </div>
          {%- endblock %}
        </div>

        <!-- <div class="pytorch-content-right" id="pytorch-content-right">
          <div class="pytorch-right-menu" id="pytorch-right-menu">
            <div class="pytorch-side-scroll" id="pytorch-side-scroll-right">
              {{ toc }}
            </div>
          </div>
        </div> -->
    </section>
  </div>

  {% include "versions.html" %}

  {% if not embedded %}

  {% if sphinx_version >= "1.8.0" %}
  <script type="text/javascript" id="documentation_options" data-url_root="{{ pathto('', 1) }}"
    src="{{ pathto('_static/documentation_options.js', 1) }}"></script>
  {%- for scriptfile in script_files %}
  {{ js_tag(scriptfile) }}
  {%- endfor %}
  {% else %}
  <script type="text/javascript">
    var DOCUMENTATION_OPTIONS = {
      URL_ROOT: '{{ url_root }}',
      VERSION: '{{ release|e }}',
      LANGUAGE: '{{ language }}',
      COLLAPSE_INDEX: false,
      FILE_SUFFIX: '{{ '' if no_search_suffix else file_suffix }}',
      HAS_SOURCE: {{ has_source| lower }},
    SOURCELINK_SUFFIX: '{{ sourcelink_suffix }}'
           };
  </script>
  {%- for scriptfile in script_files %}
  <script type="text/javascript" src="{{ pathto(scriptfile, 1) }}"></script>
  {%- endfor %}
  {% endif %}

  {% endif %}

  <script type="text/javascript" src="{{ pathto('_static/js/vendor/popper.min.js', 1) }}"></script>
  <script type="text/javascript" src="{{ pathto('_static/js/vendor/bootstrap.min.js', 1) }}"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>
  <script type="text/javascript" src="{{ pathto('_static/js/theme.js', 1) }}"></script>

  <script type="text/javascript">
    jQuery(function () {
      SphinxRtdTheme.Navigation.enable({{ 'true' if theme_sticky_navigation | tobool else 'false' }});
      });
  </script>

  {%- block footer %} {% endblock %}

  <!-- Begin Footer -->


  <footer class="site-footer">
    <div class="container footer-container">
      <div class="footer-logo-wrapper">
        <a href="{{ theme_variables.external_urls['home'] }}" class="footer-logo"></a>
      </div>
      <div class="footer-links-wrapper">
        <div class="footer-links-col">
          <ul>
            <li class="list-title"><a href="{{ theme_variables.external_urls['home'] }}">fairscale</a></li>
            <li><a href="{{ theme_variables.external_urls['get_started'] }}">Get Started</a></li>
            <li><a href="{{ theme_variables.external_urls['contributing'] }}">Contributing</a></li>
          </ul>
        </div>

        <div class="footer-links-col">
          <ul>
            <li class="list-title"><a href="{{ theme_variables.external_urls['resources'] }}">Resources</a></li>
            <li><a href="{{ theme_variables.external_urls['docs'] }}">Docs</a></li>
            <li><a href="{{ theme_variables.external_urls['github_issues'] }}" target="_blank">Github Issues</a></li>
            <li><a href="https://opensource.facebook.com/legal/terms">Terms of Use</a></li>
            <li><a href="https://opensource.facebook.com/legal/privacy">Privacy Policy</a></li>
          </ul>
        </div>
      </div>
    </div>
    </div>
  </footer>

  {% include "cookie_banner.html" %}

  <!-- End Footer -->

  <!-- Begin Mobile Menu -->

  <div class="mobile-main-menu">
    <div class="container-fluid">
      <div class="container">
        <div class="mobile-main-menu-header-container">
          <a class="header-logo" href="{{ theme_variables.external_urls['home'] }}" aria-label="fairscale"></a>
          <a class="main-menu-close-button" href="#" data-behavior="close-mobile-menu"></a>
        </div>
      </div>
    </div>

    <div class="mobile-main-menu-links-container">
      <div class="main-menu">
        <ul>
          <li>
            <a href="{{ theme_variables.external_urls['get_started'] }}">Get Started</a>
          </li>

          <li {%- if theme_pytorch_project=='tutorials' %} class="active" {%- endif %}>
            <a href="{{ theme_variables.external_urls['docs'] }}">Docs</a>
          </li>

          <li>
            <a href="{{ theme_variables.external_urls['github'] }}">Github</a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- End Mobile Menu -->

  <script type="text/javascript" src="{{ pathto('_static/js/vendor/anchor.min.js', 1) }}"></script>

  <script type="text/javascript">
    $(document).ready(function () {
      mobileMenu.bind();
      mobileTOC.bind();
      pytorchAnchors.bind();
      sideMenus.bind();
      scrollToAnchor.bind();
      highlightNavigation.bind();
      mainMenuDropdown.bind();
      filterTags.bind();

      // Add class to links that have code blocks, since we cannot create links in code blocks
      $("article.pytorch-article a span.pre").each(function (e) {
        $(this).closest("a").addClass("has-code");
      });
    })
  </script>
</body>

</html>
