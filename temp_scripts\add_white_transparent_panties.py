#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_underwear(text):
    """移除黑纱内衣的描述，添加白色透明内裤的描述"""
    
    # 移除关于黑纱内衣的各种描述
    text = re.sub(r'with highly transparent black lace lingerie visible underneath(,)? and', 'with', text)
    text = re.sub(r'with highly transparent black lace lingerie visible underneath', '', text)
    text = re.sub(r', with highly transparent black lace lingerie underneath,', ',', text)
    text = re.sub(r'with highly transparent black lace lingerie underneath', '', text)
    text = re.sub(r', highly transparent black lace lingerie,', ',', text)
    text = re.sub(r'highly transparent black lace lingerie accentuating her', '', text)
    
    # 移除黑纱内衣与皮肤对比的描述
    text = re.sub(r'The black lace lingerie creates a striking contrast against her fair skin, the intricate patterns visible through her partially unbuttoned denim shirt\.', '', text)
    
    # 移除关键词中的black lace lingerie
    text = text.replace(', black lace lingerie', '')
    text = text.replace('black lace lingerie, ', '')
    text = text.replace('black lace lingerie,', '')
    text = text.replace('black lace lingerie', '')
    
    # 添加白色透明内裤的描述
    if "mini skirt" in text and "transparent white panties" not in text:
        text = re.sub(r'(white ultra-short mini skirt)', r'\1 with transparent white panties clearly visible underneath', text)
    
    if "see-through white top" in text:
        text = re.sub(r'(see-through white top)', r'\1, transparent white panties', text)
    
    # 如果上面的替换没有成功，尝试在服装描述中添加白色透明内裤
    if "She is wearing" in text and "transparent white panties" not in text:
        text = re.sub(r'She is wearing', 'She is wearing transparent white panties and', text)
    
    # 添加内裤与皮肤的对比描述
    if "The scene captures" in text and "transparent white panties" not in text:
        text = re.sub(r'(The scene captures)', r'The transparent white panties create a tantalizing contrast against her skin, visible beneath the hem of her mini skirt. \1', text)
    
    # 添加关键词
    if "aidmaNSFWunlock" in text and "transparent white panties" not in text:
        text = text.replace("aidmaNSFWunlock", "transparent white panties, aidmaNSFWunlock")
    
    # 处理可能出现的双重逗号和空格问题
    text = text.replace(',,', ',')
    text = re.sub(r'\s+\.', '.', text)
    text = re.sub(r'\.\s+\.', '.', text)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_underwear(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_white_panties_example():
    """创建白色透明内裤示例文件"""
    example_content = """Ultra high quality, photo, masterpiece, ultra high resolution, realistic, photorealistic, beautiful and youthful Japanese model, 19 years old, with petite body, extremely large breasts with perfect proportions, slender, slender_waist and perfect face, exquisite eyes, luscious lips, blonde_hair, yellow_eyes. Standing in a modern office with elegant minimalist design. She is wearing transparent white panties and cutoff jeans, black stockings that highlight her shapely legs, and a see-through white top, revealing her extremely large breasts with perfect proportions. Her hands hidden, with jewelry adorning her delicate wrists. Her standing confidently, looking_up, mouth slightly open, face in closeup, seductive look, 3/4 angle view, creating a captivating portrait against the professional backdrop. The transparent white panties create a tantalizing contrast against her skin, visible beneath the hem of her mini skirt. Professional office lighting with soft natural light from large windows, highlighting her features perfectly. The scene captures a perfect balance between professional environment and alluring beauty, with tightly cropped composition emphasizing her face and upper body. DSLR photo, three quarter view, face focus, office setting, black stockings, transparent white panties, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    example_path = os.path.join(EXAMPLES_DIR, "white_transparent_panties.txt")
    with open(example_path, 'w', encoding='utf-8') as file:
        file.write(example_content)
    print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_white_panties_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已更新
            content = update_underwear(content)
            
            with open(joy_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 