#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

def fix_duplicates(text):
    """修复重复的词语"""
    
    # 修复重复的"extremely"
    text = text.replace("extremely extremely", "extremely")
    
    # 修复重复的"with perfect proportions"
    text = text.replace("with perfect proportions with perfect proportions", "with perfect proportions")
    
    # 修复其他可能的重复
    text = text.replace("extremely large breasts with perfect proportions with perfect proportions", "extremely large breasts with perfect proportions")
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    for file_path in txt_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 检查是否有重复词语
            if "extremely extremely" in content or "with perfect proportions with perfect proportions" in content:
                # 修复重复词语
                original_content = content
                content = fix_duplicates(content)
                
                # 如果内容有变化，则更新文件
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as file:
                        file.write(content)
                    updated += 1
                    print(f"已修复文件: {file_path}")
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 修复了 {updated} 个文件")
    print(f"- 跳过了 {processed - updated} 个文件")
    
    return processed, updated

def fix_all_files():
    """修复所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 修复了 {total_updated} 个文件")
    print(f"- 跳过了 {total_processed - total_updated} 个文件")

if __name__ == "__main__":
    fix_all_files() 