INFO: Loaded GPU profile configuration from: config/gpu_profiles.yaml
INFO: GPU detector initialized successfully
INFO: Detected GPU: Tesla V100-SXM2-16GB with 16GB memory
INFO: Detected GPU profile: v100_sxm2_16gb
INFO: Detected Flux models: 1 files
INFO: Detected Flux models: 1 files
INFO: Detected Flux models: 1 files
INFO: Detected Flux Kontext models: 44 files
INFO: Detected Flux Kontext models: 44 files
INFO: Detected Flux Kontext models: 44 files
INFO: Detected video models: 53 files
INFO: Detected video models: 53 files
INFO: Detected video models: 4 files
INFO: Detected video models: 4 files
INFO: Startup logger initialized
INFO: Startup logger initialized
INFO: Log file: logs\comfyui_startup_20250802.log
INFO: Log file: logs\comfyui_startup_20250802.log
INFO: Model Detection Results: {
  "detected_models": [
    "flux",
    "flux",
    "flux",
    "flux_kontext",
    "flux_kontext",
    "flux_kontext",
    "video",
    "video",
    "video",
    "video"
  ],
  "selected_profile": "mixed_workload",
  "detection_time": "2025-08-02T10:24:11.493422"
}
INFO: Model Detection Results: {
  "detected_models": [
    "flux",
    "flux",
    "flux",
    "flux_kontext",
    "flux_kontext",
    "flux_kontext",
    "video",
    "video",
    "video",
    "video"
  ],
  "selected_profile": "mixed_workload",
  "detection_time": "2025-08-02T10:24:11.493422"
}
INFO: \u2713 Detected models: flux, flux, flux, flux_kontext, flux_kontext, flux_kontext, video, video, video, video
INFO: \u2713 Detected models: flux, flux, flux, flux_kontext, flux_kontext, flux_kontext, video, video, video, video
INFO: \u2713 Selected optimization profile: mixed_workload
INFO: \u2713 Selected optimization profile: mixed_workload
INFO: Using model-specific optimization for V100: mixed_workload
INFO: Using GPU profile: mixed_workload
INFO: GPU profile validation passed
INFO: Applied GPU configuration 'mixed_workload' with 8 arguments
INFO: Applied Optimization: {
  "profile_name": "mixed_workload",
  "optimization_args": [
    "--reserve-vram",
    "2",
    "--fp16-unet",
    "--fp16-text-enc",
    "--disable-cuda-malloc",
    "--async-offload",
    "--cache-lru",
    "10"
  ],
  "args_count": 8,
  "applied_time": "2025-08-02T10:24:11.494423"
}
INFO: Applied Optimization: {
  "profile_name": "mixed_workload",
  "optimization_args": [
    "--reserve-vram",
    "2",
    "--fp16-unet",
    "--fp16-text-enc",
    "--disable-cuda-malloc",
    "--async-offload",
    "--cache-lru",
    "10"
  ],
  "args_count": 8,
  "applied_time": "2025-08-02T10:24:11.494423"
}
INFO: \u2713 Applied 8 optimization parameters
INFO: \u2713 Applied 8 optimization parameters
INFO: \u2713 Optimization arguments: --reserve-vram 2 --fp16-unet --fp16-text-enc --disable-cuda-malloc --async-offload --cache-lru 10
INFO: \u2713 Optimization arguments: --reserve-vram 2 --fp16-unet --fp16-text-enc --disable-cuda-malloc --async-offload --cache-lru 10
