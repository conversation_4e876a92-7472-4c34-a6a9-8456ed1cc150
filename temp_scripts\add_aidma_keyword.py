#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 情绪提示文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 排除不需要修改的文件
EXCLUDE_FILES = [
    "README.md", "PLEATED_SKIRT_GUIDE.md", "PLEATED_SKIRT_PREVIEW.md", 
    "negative_prompt.txt", "FINAL_INSTRUCTIONS.md", "COMBINED_SOLUTION.txt",
    "LEG_POSE_GUIDE.md", "WHITE_UNDERWEAR.txt", "skirt_trigger.txt",
    "EXTREME_SKIRT.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", "WIDE_LEG_POSE.txt",
    "FINAL_SOLUTION.md", "LIGHTING_GUIDE.md", "EXTREME_NEGATIVE.txt",
    "trigger.txt", "EXTREME_GUIDE.md", "README_USAGE.md"
]

# 定义替换函数
def ensure_keyword(text, filename):
    # 记录是否进行了修改
    changes_made = False
    
    # 确保文件中包含aidmaNSFWunlock关键词
    if not "aidmaNSFWunlock" in text:
        print(f"  - 在 {filename} 中添加aidmaNSFWunlock关键词...")
        # 将关键词添加到NSFW后面
        if "NSFW," in text:
            text = text.replace("NSFW,", "NSFW, aidmaNSFWunlock,")
            changes_made = True
        elif "NSFW" in text:
            text = text.replace("NSFW", "NSFW, aidmaNSFWunlock")
            changes_made = True
        else:
            # 如果没有NSFW关键词，添加到文件开头
            text = "NSFW, aidmaNSFWunlock, " + text
            changes_made = True
    
    # 确保"pleated_skirt_not_pants"关键词也存在
    if not "pleated_skirt_not_pants" in text and "PLEATED_SKIRT" in text:
        print(f"  - 在 {filename} 中添加pleated_skirt_not_pants关键词...")
        text = text.replace("PLEATED_SKIRT,", "PLEATED_SKIRT, pleated_skirt_not_pants,")
        changes_made = True
    
    # 检查urban_street_corner关键词
    if not "urban_street_corner" in text:
        print(f"  - 在 {filename} 中添加urban_street_corner关键词...")
        # 查找现有的场景标记
        scene_markers = ["indoor_couch", "outdoor_setting", "casual_bedroom_setting"]
        for marker in scene_markers:
            if marker in text:
                text = text.replace(marker, "urban_street_corner")
                changes_made = True
                break
        
        # 如果没有找到现有的场景标记，添加新的
        if not changes_made:
            if "arabesque" in text:
                text = text.replace("arabesque", "arabesque, urban_street_corner")
                changes_made = True
            elif "fashion_portrait" in text:
                text = text.replace("fashion_portrait", "fashion_portrait, urban_street_corner")
                changes_made = True
            elif "jingyan" in text:
                text = text.replace("jingyan", "jingyan, urban_street_corner")
                changes_made = True
    
    return text, changes_made

# 处理所有情绪文件
def update_emotion_files():
    files_updated = 0
    files_skipped = 0
    updated_files = []
    skipped_files = []
    
    # 获取所有.txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件")
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        
        # 跳过不需要修改的文件
        if filename in EXCLUDE_FILES:
            print(f"跳过: {filename} (在排除列表中)")
            files_skipped += 1
            skipped_files.append(filename)
            continue
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"处理: {filename}")
            # 替换内容
            updated_content, was_changed = ensure_keyword(content, filename)
            
            # 如果内容有变化，写回文件
            if was_changed:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                print(f"已更新: {filename}")
                files_updated += 1
                updated_files.append(filename)
            else:
                print(f"无变化: {filename}")
                files_skipped += 1
                skipped_files.append(filename)
                
        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")
            files_skipped += 1
            skipped_files.append(filename + f" (错误: {str(e)})")
    
    return files_updated, files_skipped, updated_files, skipped_files

# 创建新的最终示例
def create_final_example():
    example_dir = "aidma_examples"
    os.makedirs(example_dir, exist_ok=True)
    
    example_content = """NSFW, aidmaNSFWunlock, beautiful young woman, jingyan, 
standing at stylish urban street corner,
modern city architecture background,
evening atmosphere with golden hour lighting,
white turtleneck shirt form-fitting design,
kaki body suit perfectly contouring figure,
white silky leggings with subtle sheen,
hands gracefully shielding part of face,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional photography aesthetic,
closeup view capturing elegant details,
contemporary urban setting with city ambience."""
    
    filepath = os.path.join(example_dir, "final_street_corner_example.txt")
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(example_content)
    
    print(f"已创建最终示例: {filepath}")

if __name__ == "__main__":
    print("=" * 60)
    print("最终确认: 确保所有文件中都包含了aidmaNSFWunlock关键词")
    print("=" * 60)
    
    updated, skipped, updated_files, skipped_files = update_emotion_files()
    create_final_example()
    
    print("\n" + "=" * 60)
    print(f"完成! 已更新 {updated} 个文件，跳过 {skipped} 个文件。")
    
    if updated > 0:
        print("\n已更新的文件:")
        for i, filename in enumerate(updated_files, 1):
            print(f"{i}. {filename}")
    
    if skipped > 0:
        print("\n跳过的文件:")
        for i, filename in enumerate(skipped_files, 1):
            print(f"{i}. {filename}")
    
    print("=" * 60) 