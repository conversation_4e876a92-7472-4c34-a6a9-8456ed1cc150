#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 要检查的目录
DIRS_TO_CHECK = [
    "emotion_prompts",
    "aidma_examples"
]

def check_chinese_characters(file_path):
    """检查文件中是否包含中文字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            # 检查中文字符范围 (Unicode范围: U+4E00 - U+9FFF)
            if re.search(r'[\u4e00-\u9fff]', content):
                # 找出所有中文字符和它们的上下文
                chinese_matches = re.finditer(r'[^\u4e00-\u9fff]*?([\u4e00-\u9fff]+)[^\u4e00-\u9fff]*?', content)
                matches = []
                for match in chinese_matches:
                    # 获取中文字符及其前后10个字符的上下文
                    start = max(0, match.start(1) - 10)
                    end = min(len(content), match.end(1) + 10)
                    context = content[start:end]
                    matches.append(f"    - 中文: {match.group(1)}, 上下文: '...{context}...'")
                return True, matches
        return False, []
    except Exception as e:
        return False, [f"    - 错误: {str(e)}"]

def check_directory(dir_path):
    """检查目录中所有文本文件是否包含中文字符"""
    if not os.path.exists(dir_path):
        print(f"目录 {dir_path} 不存在")
        return
    
    files_with_chinese = []
    total_files = 0
    
    for file_name in os.listdir(dir_path):
        if file_name.endswith(".txt"):
            total_files += 1
            file_path = os.path.join(dir_path, file_name)
            has_chinese, matches = check_chinese_characters(file_path)
            if has_chinese:
                files_with_chinese.append((file_name, matches))
    
    if not files_with_chinese:
        print(f"目录 {dir_path} 中的 {total_files} 个文件都不包含中文字符")
    else:
        print(f"目录 {dir_path} 中有 {len(files_with_chinese)} 个文件包含中文字符:")
        for file_name, matches in files_with_chinese:
            print(f"- {file_name}:")
            for match in matches:
                print(match)

def check_all_directories():
    """检查所有指定目录"""
    for dir_path in DIRS_TO_CHECK:
        print(f"\n检查目录: {dir_path}")
        check_directory(dir_path)

if __name__ == "__main__":
    check_all_directories()