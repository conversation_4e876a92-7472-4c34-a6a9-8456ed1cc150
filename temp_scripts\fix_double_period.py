#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 定义表情文件目录
EMOTIONS_DIR = "emotion_prompts"

# 要排除的文件
EXCLUDE_FILES = [
    "negative_prompt.txt", "trigger.txt", "skirt_trigger.txt", 
    "WHITE_UNDERWEAR.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", 
    "WIDE_LEG_POSE.txt", "EXTREME_SKIRT.txt", "EXTREME_NEGATIVE.txt",
    "COMBINED_SOLUTION.txt"
]

def fix_double_periods():
    """修复所有情绪文件中服装描述的双句点问题"""
    # 获取所有表情文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 排除特殊文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"准备修复 {len(emotion_files)} 个表情文件中的双句点问题...")
    
    # 修复计数
    fixed_count = 0
    skipped_count = 0
    
    # 正则表达式模式，用于识别和替换双句点
    double_period_pattern = r"thighs\.\."
    single_period_replacement = "thighs."
    
    for file in emotion_files:
        filename = os.path.basename(file)
        print(f"\n处理文件: {filename}")
        
        with open(file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有双句点
        if re.search(double_period_pattern, content):
            # 替换双句点为单句点
            new_content = re.sub(double_period_pattern, single_period_replacement, content)
            
            # 保存修改
            with open(file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"  ✓ 已修复双句点问题")
            fixed_count += 1
        else:
            print(f"  ✓ 未发现双句点问题")
            skipped_count += 1
    
    print(f"\n完成! 修复了 {fixed_count} 个文件，跳过了 {skipped_count} 个文件。")

def verify_clothing_descriptions():
    """验证所有文件的服装描述是否正确且一致"""
    # 获取所有表情文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 排除特殊文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"\n验证 {len(emotion_files)} 个表情文件的服装描述...")
    
    # 标准服装描述
    standard_description = (
        "Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage, "
        "paired with a glossy black ultra-short mini skirt that sits low on her hips, "
        "clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs."
    )
    
    # 正则表达式模式，用于提取服装描述
    clothing_pattern = r"Wearing.+?(?=\.\s*Standing|$)"
    
    # 验证结果
    consistent_count = 0
    inconsistent_count = 0
    inconsistent_files = []
    
    for file in emotion_files:
        filename = os.path.basename(file)
        
        with open(file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取服装描述
        match = re.search(clothing_pattern, content)
        if match:
            extracted_description = match.group(0)
            
            # 检查是否与标准描述一致
            if extracted_description.strip() == standard_description.strip():
                consistent_count += 1
            else:
                inconsistent_count += 1
                inconsistent_files.append(filename)
        else:
            inconsistent_count += 1
            inconsistent_files.append(filename)
    
    # 报告验证结果
    print(f"\n验证结果: {consistent_count} 个文件的服装描述一致，{inconsistent_count} 个文件不一致")
    
    if inconsistent_count > 0:
        print("\n不一致的文件:")
        for filename in inconsistent_files:
            print(f"  - {filename}")
        
        print("\n建议再次运行 fix_clothing_conflicts.py 修复这些文件")
    else:
        print("\n✓ 所有文件的服装描述已经标准化且一致")

if __name__ == "__main__":
    fix_double_periods()
    verify_clothing_descriptions() 