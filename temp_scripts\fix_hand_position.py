#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 情绪提示文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 排除不需要修改的文件
EXCLUDE_FILES = [
    "README.md", "PLEATED_SKIRT_GUIDE.md", "PLEATED_SKIRT_PREVIEW.md", 
    "negative_prompt.txt", "FINAL_INSTRUCTIONS.md", "COMBINED_SOLUTION.txt",
    "LEG_POSE_GUIDE.md", "WHITE_UNDERWEAR.txt", "skirt_trigger.txt",
    "EXTREME_SKIRT.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", "WIDE_LEG_POSE.txt",
    "FINAL_SOLUTION.md", "LIGHTING_GUIDE.md", "EXTREME_NEGATIVE.txt",
    "trigger.txt", "EXTREME_GUIDE.md", "README_USAGE.md"
]

# 定义替换函数
def update_hand_pose(text, filename):
    # 记录是否进行了修改
    changes_made = False
    
    # 替换手部描述
    hand_patterns = [
        r"One hand resting naturally on the bed[\s\S]*?pose\.",
        r"One hand gently raised near her face[\s\S]*?features\.",
        r"One hand gently touching collar or chest[\s\S]*?gesture\."
    ]
    
    for pattern in hand_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的手部姿势描述...")
            text = re.sub(
                pattern,
                "Hands gracefully shielding part of her face in an artful, arabesque-inspired pose, creating a sense of mystery and elegance while framing her expressive eyes.",
                text
            )
            changes_made = True
            break
    
    return text, changes_made

# 处理所有情绪文件
def update_emotion_files():
    files_updated = 0
    files_skipped = 0
    updated_files = []
    skipped_files = []
    
    # 获取所有.txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件")
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        
        # 跳过不需要修改的文件
        if filename in EXCLUDE_FILES:
            print(f"跳过: {filename} (在排除列表中)")
            files_skipped += 1
            skipped_files.append(filename)
            continue
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"处理: {filename}")
            # 替换内容
            updated_content, was_changed = update_hand_pose(content, filename)
            
            # 如果内容有变化，写回文件
            if was_changed:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                print(f"已更新: {filename}")
                files_updated += 1
                updated_files.append(filename)
            else:
                print(f"无变化: {filename}")
                files_skipped += 1
                skipped_files.append(filename)
                
        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")
            files_skipped += 1
            skipped_files.append(filename + f" (错误: {str(e)})")
    
    return files_updated, files_skipped, updated_files, skipped_files

if __name__ == "__main__":
    print("=" * 60)
    print("开始更新手部姿势为遮挡面部的艺术姿势...")
    print("=" * 60)
    
    updated, skipped, updated_files, skipped_files = update_emotion_files()
    
    print("\n" + "=" * 60)
    print(f"完成! 已更新 {updated} 个文件，跳过 {skipped} 个文件。")
    
    if updated > 0:
        print("\n已更新的文件:")
        for i, filename in enumerate(updated_files, 1):
            print(f"{i}. {filename}")
    
    if skipped > 0:
        print("\n跳过的文件:")
        for i, filename in enumerate(skipped_files, 1):
            print(f"{i}. {filename}")
    
    print("=" * 60) 