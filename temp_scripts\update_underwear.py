import os
import re
import glob

# 定义要排除的文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 定义要替换的文本
old_underwear = r"black panties"
new_underwear = r"white thong"

# 获取emotion_prompts文件夹中的所有txt文件
prompt_files = glob.glob('emotion_prompts/*.txt')

# 初始化计数器
updated_count = 0
skipped_count = 0

# 处理每个文件
for file_path in prompt_files:
    # 检查是否为排除文件
    if os.path.basename(file_path) in exclude_files:
        print(f"跳过文件: {file_path}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换内裤描述
        if old_underwear in content:
            new_content = content.replace(old_underwear, new_underwear)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
            
            print(f"已更新文件: {file_path}")
            updated_count += 1
        else:
            print(f"文件中没有找到需要替换的内容: {file_path}")
            skipped_count += 1
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        skipped_count += 1

print(f"\n更新完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。")
