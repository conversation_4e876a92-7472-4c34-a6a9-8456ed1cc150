#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_outfit_description(text):
    """更新服装描述为黑色蕾丝内衣套装"""
    # 替换白色比基尼上衣和牛仔短裙的描述
    # 检查是否包含bikini和mini skirt的描述
    new_outfit_included = 'black lace lingerie' in text or 'demi-cup' in text or 'lace robe' in text
    
    if not new_outfit_included and ('bikini' in text or 'mini skirt' in text or 'miniskirt' in text):
        # 替换整个服装描述段落
        pattern = r'wearing an? .+?(bikini top).+?(mini skirt).+?(thigh-high stockings).+?(exposing|revealing)'
        if re.search(pattern, text, re.IGNORECASE | re.DOTALL):
            text = re.sub(
                pattern,
                'wearing a black lace lingerie set that includes a demi-cup bra revealing a significant portion of her breasts with intricate lace detailing, matching thong-style panties with elegant lace patterns, and a sheer black lace robe draped over her shoulders and arms. A black lace choker rests around her neck, complementing the lingerie set. Her smooth, slightly glossy skin contrasts beautifully with the dark lace, revealing',
                text,
                flags=re.IGNORECASE | re.DOTALL
            )
        else:
            # 替换单独的服装部分描述
            if 'bikini top' in text or 'gauze bikini' in text:
                text = re.sub(
                    r'(extremely sheer white gauze bikini top[^,\.]+?)(,|\.)',
                    r'black lace demi-cup bra revealing a significant portion of her breasts with intricate lace detailing\2',
                    text
                )
            
            if 'light blue denim mini skirt' in text:
                text = re.sub(
                    r'(light blue denim mini skirt[^,\.]+?)(,|\.)',
                    r'matching black lace thong-style panties with elegant patterns\2',
                    text
                )
            
            if 'white thigh-high stockings' in text or 'white silk stockings' in text:
                text = re.sub(
                    r'(white.+?thigh-high stockings[^,\.]+?)(,|\.)',
                    r'sheer black lace robe draped elegantly over her shoulders and arms\2',
                    text
                )
            
            # 添加黑色蕾丝颈链描述
            if 'choker' not in text and 'neck' in text:
                text = re.sub(
                    r'(bare midriff and navel)',
                    r'\1. A black lace choker rests around her neck, complementing the lingerie set',
                    text
                )
    
    # 删除内裤可见性的描述，因为现在是穿着内衣
    text = re.sub(r', so short that her.+?panties are clearly visible beneath the hem', '', text)
    
    # 删除白色内裤的描述
    text = re.sub(r'white triangle panties', 'black lace thong', text)
    text = re.sub(r'pure white triangle panties', 'black lace thong', text)
    text = re.sub(r'white underwear', 'black lace underwear', text)
    text = re.sub(r'pure white underwear', 'black lace underwear', text)
    
    # 替换关于丝袜的描述
    text = re.sub(r'thigh-high stockings', 'lace lingerie', text)
    
    # 替换关于身体暴露的描述
    text = re.sub(r'exposing her bare midriff and navel', 'revealing her smooth, slightly glossy skin', text)
    
    # 添加关键词到文本末尾
    if 'pale skin' not in text and 'slender' not in text and text.endswith('aidmaNSFWunlock'):
        text = text.replace('aidmaNSFWunlock', 'pale skin, slender, shaved pussy, dynamic pose, full body photo, aidmaNSFWunlock')
    elif 'pale skin' not in text and 'slender' not in text:
        text = text + ', pale skin, slender, shaved pussy, dynamic pose, full body photo'
    
    # 确保有aidmaimageupgrader关键词
    if 'aidmaimageupgrader' not in text:
        text = text.replace('aidmaNSFWunlock', 'aidmaimageupgrader, aidmaNSFWunlock')
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_outfit_description(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_black_lace_lingerie_example():
    """创建黑色蕾丝内衣套装的示例文件"""
    example_content = """beautiful young woman, jingyan, with long, flowing brown hair, standing elegantly against a plain, light-colored backdrop. She wears a black lace lingerie set that includes a demi-cup bra revealing a significant portion of her breasts with intricate lace detailing, matching thong-style panties with elegant lace patterns, and a sheer black lace robe draped over her shoulders and arms. A black lace choker rests around her neck, complementing the lingerie set. Her smooth, slightly glossy skin contrasts beautifully with the dark lace. Standing with a confident, dynamic pose that showcases the textures of the lace and the contours of her body. Soft, even lighting highlights the delicate details of the lingerie and creates a sultry, sophisticated aesthetic. The black lace creates a striking contrast against her pale skin. The overall composition captures the elegance and allure of the ensemble in a full body portrait, pale skin, slender, shaved pussy, dynamic pose, full body photo, aidmaimageupgrader, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建第二个例子，更强调光线效果
    example2_content = """beautiful young woman, jingyan, with long, flowing brown hair styled in soft waves, posed against a simple, light-colored backdrop. She's adorned in an exquisite black lace lingerie ensemble featuring a demi-cup bra that accentuates her breasts with delicate lacework, paired with matching thong-style panties showcasing the same intricate patterns. A sheer black lace robe floats gracefully around her figure, adding an ethereal quality. A slender black lace choker encircles her neck, perfectly complementing the lingerie set. Her pale, luminous skin has a subtle glow that contrasts dramatically with the deep black lace. The lighting is soft yet precise, designed to highlight the textural details of the lace while casting gentle shadows that enhance her feminine contours. Her pose is confidently dynamic, creating elegant lines that showcase both her slender figure and the sophisticated craftsmanship of the lingerie. The overall aesthetic combines sensuality with refinement, creating a portrait of sultry elegance, pale skin, slender, shaved pussy, dynamic pose, full body photo, aidmaimageupgrader, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    examples = {
        "black_lace_lingerie_example.txt": example_content,
        "black_lace_lingerie_enhanced.txt": example2_content
    }
    
    for filename, content in examples.items():
        example_path = os.path.join(EXAMPLES_DIR, filename)
        with open(example_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_black_lace_lingerie_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已更新到黑色蕾丝内衣套装
            if 'black lace lingerie' not in content:
                content = update_outfit_description(content)
                
                with open(joy_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 