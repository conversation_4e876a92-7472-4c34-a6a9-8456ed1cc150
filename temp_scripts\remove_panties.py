import os
import re

# 文件夹路径
folder_path = 'emotion_prompts'

# 新的描述（只有裙子，没有内裤）
new_description = "NO PANTS, wearing a micro-length black latex bodycon miniskirt that barely covers the buttocks, with nothing underneath, completely exposing bare intimate details."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 统计
updated_count = 0
skipped_count = 0

# 正则表达式模式 - 匹配包含裙子和内裤的描述行
pattern = r'NO PANTS, wearing a micro-length black latex bodycon miniskirt.*panties.*'

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 使用正则表达式查找并替换
            updated_content = re.sub(pattern, new_description, content)
            
            # 检查是否进行了替换
            if updated_content != content:
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                
                print(f"已更新移除内裤描述: {filename}")
                updated_count += 1
            else:
                print(f"跳过 (未找到匹配的描述): {filename}")
                skipped_count += 1
                
        except Exception as e:
            print(f"处理文件时出错 {filename}: {str(e)}")
            skipped_count += 1

print(f"\n更新完成: {updated_count} 个文件已更新，{skipped_count} 个文件已跳过")
print("所有文件已更新，已移除内裤描述，只保留裙子说明。")
