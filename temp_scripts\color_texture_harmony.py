#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# 创建示例目录
TEXTURES_DIR = "aidma_textures"
os.makedirs(TEXTURES_DIR, exist_ok=True)

# 创建不同纹理和背景的示例

# 1. 大理石背景
marble_background = """aidma<PERSON><PERSON><PERSON><PERSON><PERSON>, beautiful young woman, jingyan, 
standing in an elegant interior with luxurious white marble background with subtle grey veining,
high-end pristine modern setting,
sophisticated atmosphere with soft directional lighting creating gentle shadows,
wearing a sleek black deep V-neck top that dramatically plunges to reveal cleavage,
paired with a pristine white ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by <PERSON> style,
fashion portrait composition,
professional studio photography aesthetic,
luxurious texture contrast between smooth marble and fabric,
contemporary minimalist setting with elegant marble textures."""

# 2. 木纹背景
wood_background = """aidma<PERSON><PERSON><PERSON><PERSON><PERSON>, beautiful young woman, jingyan, 
standing in a warm interior with natural light oak wood paneled background showing rich grain patterns,
organic modern setting,
warm inviting atmosphere with gentle diffused lighting,
wearing a soft cream deep V-neck top that dramatically plunges to reveal cleavage,
paired with a rich brown leather ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by <PERSON> <PERSON><PERSON>jewska style,
fashion portrait composition,
professional studio photography aesthetic,
natural texture harmony between wood grain and leather,
contemporary organic setting with warm wooden textures."""

# 3. 织物质感背景
fabric_background = """aidmaNS<PERSON>Wunlock, beautiful young woman, jingyan, 
standing in a soft interior with textured light beige linen fabric backdrop with subtle weave pattern,
cozy refined setting,
serene atmosphere with gentle even lighting minimizing harsh shadows,
wearing a textured knit deep V-neck top in soft heather grey that dramatically plunges to reveal cleavage,
paired with a smooth satin deep navy ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
tactile texture interplay between linen backdrop, knit top and satin skirt,
contemporary soft setting with layered fabric textures."""

# 4. 水泥墙背景
concrete_background = """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing in an industrial-chic interior with smooth polished concrete wall background showing subtle tonal variations,
modern urban setting,
cool sophisticated atmosphere with dramatic side lighting creating interesting shadows,
wearing a metallic silver deep V-neck top that dramatically plunges to reveal cleavage,
paired with a textured black denim ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
urban texture contrast between industrial concrete and fashionable fabrics,
contemporary industrial setting with minimalist concrete textures."""

# 写入示例文件
examples = [
    {"filename": "marble_background.txt", "content": marble_background},
    {"filename": "wood_background.txt", "content": wood_background},
    {"filename": "fabric_background.txt", "content": fabric_background},
    {"filename": "concrete_background.txt", "content": concrete_background}
]

for example in examples:
    filepath = os.path.join(TEXTURES_DIR, example["filename"])
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(example["content"])
    print(f"已创建纹理背景示例: {filepath}")

# 创建纹理指南
texture_guide = """# 室内纹理背景指南

## 纹理在视觉表现中的作用
- 纹理增加图像的深度和视觉兴趣
- 背景纹理应与服装纹理互相呼应但不争夺注意力
- 纹理可以增强特定的情绪和氛围
- 纹理可以暗示环境和场景的性质

## 经典纹理背景选择

### 大理石背景
- 奢华、高端、精致的视觉效果
- 白色大理石带灰色纹理营造高贵气质
- 与黑色上衣和白色短裙形成完美协调
- 适合高端时尚、奢侈品或优雅风格的照片
- 纹理描述关键词：白色大理石、灰色纹理、光滑表面、细腻花纹

### 木纹背景
- 温暖、自然、有机的视觉效果
- 浅色橡木纹理营造亲切、舒适的氛围
- 与奶油色上衣和棕色皮质短裙形成自然色调组合
- 适合自然、有机或乡村风格的照片
- 纹理描述关键词：橡木纹理、丰富木纹、自然花纹、温暖色调

### 织物质感背景
- 柔软、质感、亲切的视觉效果
- 米色亚麻布纹理营造舒适、温馨的氛围
- 与灰色针织上衣和深蓝色缎面短裙形成丰富的质感对比
- 适合柔和、生活方式或休闲风格的照片
- 纹理描述关键词：亚麻织物、精细编织、柔软表面、自然纹理

### 水泥墙背景
- 现代、工业、都市的视觉效果
- 抛光混凝土墙面营造冷酷、都市的氛围
- 与金属银色上衣和黑色牛仔短裙形成鲜明对比
- 适合现代、都市或前卫风格的照片
- 纹理描述关键词：抛光混凝土、细微色调变化、冷硬表面、工业质感

## 纹理与服装的互动
1. 对比原则 - 背景光滑时服装可以更有纹理，反之亦然
2. 呼应原则 - 背景纹理可以微妙呼应服装的某些纹理元素
3. 主次分明 - 确保纹理不会过于喧宾夺主
4. 情绪一致 - 纹理应与整体想要表达的情绪相符

## 提示词中的纹理描述技巧
- 使用具体的材质名称：如"polished concrete"、"light oak wood"
- 添加质感细节："subtle grey veining"、"rich grain patterns"
- 描述光线与纹理的互动："gentle shadows on marble surface"
- 直接指出纹理对比："texture contrast between smooth marble and fabric"

## 纹理背景示例文件
- `marble_background.txt` - 奢华高端的大理石背景
- `wood_background.txt` - 温暖自然的木纹背景
- `fabric_background.txt` - 柔软质感的织物背景
- `concrete_background.txt` - 现代都市的水泥墙背景"""

# 写入纹理指南文件
texture_guide_path = os.path.join(TEXTURES_DIR, "TEXTURE_GUIDE.md")
with open(texture_guide_path, 'w', encoding='utf-8') as f:
    f.write(texture_guide)
print(f"已创建纹理指南: {texture_guide_path}")

print("\n所有纹理背景示例和指南创建完成！")
print("使用这些示例参考不同的纹理背景设计方案。") 