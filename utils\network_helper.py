#!/usr/bin/env python3
"""
ComfyUI Network Helper
Provides network detection and configuration utilities for remote access
"""

import socket
import subprocess
import sys
import json
import argparse
from typing import List, Dict, Optional
import ipaddress

def get_local_ip_addresses() -> List[Dict[str, str]]:
    """获取本机所有网络接口的IP地址"""
    interfaces = []
    
    try:
        # Windows方式获取IP地址
        result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            current_adapter = ""
            
            for line in lines:
                line = line.strip()
                if "适配器" in line or "adapter" in line.lower():
                    current_adapter = line
                elif "IPv4" in line and ":" in line:
                    ip = line.split(":")[-1].strip()
                    if ip and not ip.startswith("127.") and not ip.startswith("169.254."):
                        interfaces.append({
                            "interface": current_adapter,
                            "ip": ip,
                            "type": "IPv4"
                        })
                elif "IPv6" in line and ":" in line and "fe80" not in line.lower():
                    ip = line.split(":")[-1].strip()
                    if ip:
                        interfaces.append({
                            "interface": current_adapter,
                            "ip": ip,
                            "type": "IPv6"
                        })
    except Exception as e:
        print(f"Warning: Could not detect network interfaces: {e}", file=sys.stderr)
    
    # 备用方法：使用socket
    if not interfaces:
        try:
            # 获取主要IP地址
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            primary_ip = s.getsockname()[0]
            s.close()
            
            interfaces.append({
                "interface": "Primary Network",
                "ip": primary_ip,
                "type": "IPv4"
            })
        except Exception:
            pass
    
    return interfaces

def check_port_available(port: int, host: str = "0.0.0.0") -> bool:
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind((host, port))
            return True
    except OSError:
        return False

def find_available_port(start_port: int = 18188, max_attempts: int = 10) -> int:
    """查找可用端口"""
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            return port
    return start_port  # 如果都不可用，返回原始端口

def generate_access_urls(interfaces: List[Dict[str, str]], port: int, use_https: bool = False) -> List[str]:
    """生成访问URL列表"""
    protocol = "https" if use_https else "http"
    urls = []
    
    # 本地访问
    urls.append(f"{protocol}://localhost:{port}")
    urls.append(f"{protocol}://127.0.0.1:{port}")
    
    # 网络访问
    for interface in interfaces:
        ip = interface["ip"]
        if interface["type"] == "IPv4":
            urls.append(f"{protocol}://{ip}:{port}")
        elif interface["type"] == "IPv6":
            urls.append(f"{protocol}://[{ip}]:{port}")
    
    return urls

def check_firewall_status() -> Dict[str, str]:
    """检查Windows防火墙状态"""
    try:
        result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'],
                              capture_output=True, text=True, encoding='utf-8', errors='ignore')
        if result.returncode == 0:
            output = result.stdout
            if "启用" in output or "ON" in output.upper() or "enabled" in output.lower():
                return {"status": "enabled", "message": "Windows防火墙已启用"}
            else:
                return {"status": "disabled", "message": "Windows防火墙已禁用"}
        else:
            return {"status": "unknown", "message": "无法检测防火墙状态"}
    except Exception:
        return {"status": "unknown", "message": "无法检测防火墙状态"}

def main():
    parser = argparse.ArgumentParser(description="ComfyUI Network Helper")
    parser.add_argument("--check-port", type=int, help="检查指定端口是否可用")
    parser.add_argument("--find-port", type=int, help="查找可用端口，从指定端口开始")
    parser.add_argument("--get-ips", action="store_true", help="获取本机IP地址")
    parser.add_argument("--generate-urls", type=int, help="生成访问URL，指定端口")
    parser.add_argument("--https", action="store_true", help="使用HTTPS协议")
    parser.add_argument("--check-firewall", action="store_true", help="检查防火墙状态")
    parser.add_argument("--full-info", action="store_true", help="显示完整网络信息")
    parser.add_argument("--json", action="store_true", help="以JSON格式输出")
    
    args = parser.parse_args()
    
    if args.check_port:
        available = check_port_available(args.check_port)
        if args.json:
            print(json.dumps({"port": args.check_port, "available": available}))
        else:
            status = "可用" if available else "被占用"
            print(f"端口 {args.check_port}: {status}")
    
    elif args.find_port is not None:
        port = find_available_port(args.find_port)
        if args.json:
            print(json.dumps({"recommended_port": port}))
        else:
            print(f"{port}")
    
    elif args.get_ips:
        interfaces = get_local_ip_addresses()
        if args.json:
            print(json.dumps(interfaces, ensure_ascii=False, indent=2))
        else:
            print("检测到的网络接口:")
            for interface in interfaces:
                print(f"  {interface['interface']}: {interface['ip']} ({interface['type']})")
    
    elif args.generate_urls:
        interfaces = get_local_ip_addresses()
        urls = generate_access_urls(interfaces, args.generate_urls, args.https)
        if args.json:
            print(json.dumps({"urls": urls}, ensure_ascii=False, indent=2))
        else:
            print("访问地址:")
            for url in urls:
                print(f"  {url}")
    
    elif args.check_firewall:
        firewall_info = check_firewall_status()
        if args.json:
            print(json.dumps(firewall_info, ensure_ascii=False))
        else:
            print(firewall_info["message"])

    elif args.full_info:
        # 显示完整网络信息
        interfaces = get_local_ip_addresses()
        port = find_available_port()
        urls = generate_access_urls(interfaces, port)
        firewall_info = check_firewall_status()

        if args.json:
            result = {
                "interfaces": interfaces,
                "recommended_port": port,
                "access_urls": urls,
                "firewall": firewall_info
            }
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("=== ComfyUI 网络配置信息 ===")
            print(f"推荐端口: {port}")
            print("\n检测到的网络接口:")
            for interface in interfaces:
                print(f"  {interface['interface']}: {interface['ip']} ({interface['type']})")
            print(f"\n防火墙状态: {firewall_info['message']}")
            print("\n访问地址:")
            for url in urls:
                print(f"  {url}")

    else:
        # 默认：只返回推荐端口（为了兼容启动脚本）
        port = find_available_port()
        print(f"{port}")

if __name__ == "__main__":
    main()
