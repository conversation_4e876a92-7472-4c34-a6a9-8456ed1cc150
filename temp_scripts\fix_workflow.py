#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import sys
import re

def fix_workflow(workflow_path, remove_aux_id=False):
    """修复工作流中的aux_id格式问题
    
    Args:
        workflow_path: 工作流JSON文件路径
        remove_aux_id: 如果为True，则删除不正确的aux_id；否则替换为默认格式
    """
    
    # 读取工作流JSON文件
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return False
    
    # 检查是否存在nodes字段
    if 'nodes' not in workflow:
        print("工作流JSON中没有找到nodes字段")
        return False
    
    # 修复计数器
    fixed_count = 0
    
    # 遍历所有节点
    for i, node in enumerate(workflow['nodes']):
        # 检查是否需要修复Note Plus节点
        if node.get('type') == "Note Plus (mtb)":
            # 如果properties是空对象，添加标准的aux_id
            if 'properties' in node and not node['properties']:
                node['properties']['aux_id'] = "comfyui-mtb/note-plus"
                print(f"已修复节点 {i}（Note Plus (mtb)）的空properties")
                fixed_count += 1
        
        # 检查其他节点的aux_id格式问题
        if 'properties' in node and 'aux_id' in node['properties']:
            # 检查aux_id格式
            aux_id = node['properties']['aux_id']
            # 修改匹配条件，检测不符合"username/repo"格式的aux_id
            if '/' not in aux_id or not aux_id.replace('/', '').strip() or not re.match(r'^[^/]+/[^/]+$', aux_id):
                if remove_aux_id:
                    # 删除aux_id
                    del node['properties']['aux_id']
                    print(f"已删除节点 {i} 的aux_id: {aux_id}")
                else:
                    # 修复aux_id为一个示例格式
                    node['properties']['aux_id'] = "default-user/default-repo"
                    print(f"已修复节点 {i} 的aux_id: {aux_id} -> default-user/default-repo")
                fixed_count += 1
    
    # 特别处理第17个节点，如果它是WanVideoVRAMManagement并且没有aux_id
    if len(workflow['nodes']) > 17:
        node_17 = workflow['nodes'][17]
        if node_17.get('type') == "WanVideoVRAMManagement" and 'properties' in node_17:
            if 'aux_id' not in node_17['properties']:
                node_17['properties']['aux_id'] = "default-user/default-repo"
                print(f"已为节点 17（WanVideoVRAMManagement）添加默认aux_id")
                fixed_count += 1
    
    # 如果有修改，保存文件
    if fixed_count > 0:
        backup_path = workflow_path + ".backup"
        # 备份原始文件
        try:
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(workflow, f, indent=2)
            print(f"已创建备份文件: {backup_path}")
        except Exception as e:
            print(f"创建备份失败: {str(e)}")
        
        # 保存修复后的文件
        try:
            with open(workflow_path, 'w', encoding='utf-8') as f:
                json.dump(workflow, f, indent=2)
            action = "删除" if remove_aux_id else "修复"
            print(f"已{action} {fixed_count} 个节点并保存到: {workflow_path}")
            return True
        except Exception as e:
            print(f"保存文件失败: {str(e)}")
            return False
    else:
        print("未发现需要修复的节点")
        return True

def main():
    if len(sys.argv) < 2:
        print("用法: python fix_workflow.py <工作流文件路径> [--remove]")
        print("选项:")
        print("  --remove  删除不正确的aux_id而不是替换它们")
        return
    
    workflow_path = sys.argv[1]
    if not os.path.exists(workflow_path):
        print(f"文件不存在: {workflow_path}")
        return
    
    # 检查是否有--remove选项
    remove_aux_id = "--remove" in sys.argv
    
    success = fix_workflow(workflow_path, remove_aux_id)
    if success:
        print("处理完成")
    else:
        print("处理失败")

if __name__ == "__main__":
    main() 