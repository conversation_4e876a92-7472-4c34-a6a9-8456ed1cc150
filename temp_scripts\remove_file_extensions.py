#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil

# 目录路径
SOURCE_DIR = "E:/ComfyUI/output/张瑾妍/renamed"
TARGET_DIR = "E:/ComfyUI/output/张瑾妍/final"

def remove_extensions():
    """移除文件名后缀"""
    # 确保目标目录存在
    if not os.path.exists(TARGET_DIR):
        os.makedirs(TARGET_DIR)
    
    # 获取所有文件
    files = os.listdir(SOURCE_DIR)
    
    renamed_count = 0
    for file in files:
        if os.path.isfile(os.path.join(SOURCE_DIR, file)):
            # 获取不带后缀的文件名
            name_without_ext = os.path.splitext(file)[0]
            
            # 原文件路径和新文件路径
            source_path = os.path.join(SOURCE_DIR, file)
            target_path = os.path.join(TARGET_DIR, name_without_ext)
            
            # 复制文件并使用新名称
            shutil.copy2(source_path, target_path)
            print(f"已处理: {file} -> {name_without_ext}")
            renamed_count += 1
    
    print(f"\n总共处理了 {renamed_count} 个文件，移除了后缀名")
    
    # 显示最终结果
    if os.path.exists(TARGET_DIR):
        final_files = os.listdir(TARGET_DIR)
        final_files.sort()
        
        print("\n最终的文件列表:")
        for file in final_files:
            print(f"- {file}")

if __name__ == "__main__":
    remove_extensions()