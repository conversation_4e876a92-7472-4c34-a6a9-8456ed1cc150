#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
import re

def find_problem_nodes(workflow_path):
    """查找工作流中可能有问题的节点"""
    
    # 读取工作流JSON文件
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return False
    
    # 检查是否存在nodes字段
    if 'nodes' not in workflow:
        print("工作流JSON中没有找到nodes字段")
        return False
    
    # 遍历所有节点，寻找可能导致验证错误的问题
    problem_found = False
    
    for i, node in enumerate(workflow['nodes']):
        node_type = node.get('type', 'N/A')
        node_id = node.get('id', 'N/A')
        
        # 检查特定的已知问题
        if node_type == "Note Plus (mtb)":
            print(f"发现潜在问题节点 {i} (ID: {node_id}): Note Plus (mtb)")
            print("Note Plus (mtb)类型的节点在某些ComfyUI版本中可能导致验证错误")
            # 检查特定属性
            if 'properties' in node:
                if 'aux_id' in node['properties']:
                    print(f"  aux_id: {node['properties']['aux_id']}")
                elif not node['properties']:
                    print("  properties是空对象，这可能导致验证错误")
                else:
                    print(f"  properties: {node['properties']}")
            problem_found = True
        
        # 检查aux_id格式问题
        if 'properties' in node and 'aux_id' in node['properties']:
            aux_id = node['properties']['aux_id']
            if aux_id and not re.match(r'^[^/]+/[^/]+$', aux_id):
                print(f"节点 {i} (ID: {node_id}, 类型: {node_type}) 的aux_id格式不正确: {aux_id}")
                problem_found = True
    
    # 如果有其他特殊情况，也许需要检查整个文件中的特定字符串
    # 例如，空的properties字段，空列表，等等
    
    if not problem_found:
        print("未发现已知的问题节点")
        # 作为测试，手动添加一个空的aux_id，看看是否会修复问题
        print("\n修复建议:")
        print("尝试在第17个节点 (WanVideoVRAMManagement) 添加一个正确格式的aux_id")
        print("例如: 'default-user/default-repo'")

def main():
    if len(sys.argv) < 2:
        print("用法: python find_problem_node.py <工作流文件路径>")
        return
    
    workflow_path = sys.argv[1]
    find_problem_nodes(workflow_path)

if __name__ == "__main__":
    main() 