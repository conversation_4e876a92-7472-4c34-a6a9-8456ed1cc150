#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 情绪提示文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 排除不需要修改的文件
EXCLUDE_FILES = [
    "README.md", "PLEATED_SKIRT_GUIDE.md", "PLEATED_SKIRT_PREVIEW.md", 
    "negative_prompt.txt", "FINAL_INSTRUCTIONS.md", "COMBINED_SOLUTION.txt",
    "LEG_POSE_GUIDE.md", "WHITE_UNDERWEAR.txt", "skirt_trigger.txt",
    "EXTREME_SKIRT.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", "WIDE_LEG_POSE.txt",
    "FINAL_SOLUTION.md", "LIGHTING_GUIDE.md", "EXTREME_NEGATIVE.txt",
    "trigger.txt", "EXTREME_GUIDE.md", "README_USAGE.md"
]

# 定义替换函数
def update_scene(text, filename):
    # 记录是否进行了修改
    changes_made = False
    
    # 替换场景描述 - 从室内沙发或其他场景改为室外街角
    scene_patterns = [
        r"Laying elegantly on top of a plush green couch[\s\S]*?atmosphere\.",
        r"Sitting casually on a neatly made bed[\s\S]*?atmosphere\.",
        r"Standing gracefully outdoors against a natural wooden backdrop[\s\S]*?atmosphere\."
    ]
    
    for pattern in scene_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的场景描述为室外街角...")
            text = re.sub(
                pattern,
                "Standing gracefully at a stylish urban street corner, surrounded by modern architecture and subtle city ambience, the soft evening light creating a sophisticated metropolitan atmosphere.",
                text
            )
            changes_made = True
            break
    
    # 替换光线描述
    light_patterns = [
        r"Soft indoor lighting[\s\S]*?view",
        r"Soft natural daylight[\s\S]*?features",
        r"Golden outdoor sunlight[\s\S]*?illumination"
    ]
    
    for pattern in light_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的光线描述...")
            text = re.sub(
                pattern,
                "Soft golden hour sunlight casting long shadows across the urban landscape, highlighting her features with warm directional illumination as if captured candidly on a city street",
                text
            )
            changes_made = True
            break
    
    # 更新标记和关键词
    if "indoor_couch" in text or "casual_bedroom_setting" in text or "outdoor_setting" in text:
        print(f"  - 更新 {filename} 中的场景标记...")
        text = text.replace("indoor_couch", "urban_street_corner")
        text = text.replace("casual_bedroom_setting", "urban_street_corner")
        text = text.replace("outdoor_setting", "urban_street_corner")
        changes_made = True
    
    return text, changes_made

# 处理所有情绪文件
def update_emotion_files():
    files_updated = 0
    files_skipped = 0
    updated_files = []
    skipped_files = []
    
    # 获取所有.txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件")
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        
        # 跳过不需要修改的文件
        if filename in EXCLUDE_FILES:
            print(f"跳过: {filename} (在排除列表中)")
            files_skipped += 1
            skipped_files.append(filename)
            continue
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"处理: {filename}")
            # 替换内容
            updated_content, was_changed = update_scene(content, filename)
            
            # 如果内容有变化，写回文件
            if was_changed:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                print(f"已更新: {filename}")
                files_updated += 1
                updated_files.append(filename)
            else:
                print(f"无变化: {filename}")
                files_skipped += 1
                skipped_files.append(filename)
                
        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")
            files_skipped += 1
            skipped_files.append(filename + f" (错误: {str(e)})")
    
    return files_updated, files_skipped, updated_files, skipped_files

# 创建室外街角例子
def create_street_corner_example():
    example_dir = "aidma_examples"
    os.makedirs(example_dir, exist_ok=True)
    
    example_content = """NSFW, aidmaNSFWunlock, beautiful young woman standing at urban street corner, inspired by Emma Andijewska, arabesque pose, in white turtleneck shirt, kaki body suit, hands shielding face, golden hour lighting, white silky leggings, fashion portrait photo, contemporary city background, modern architecture, evening atmosphere, stylish urban setting."""
    
    filepath = os.path.join(example_dir, "street_corner_example.txt")
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(example_content)
    
    print(f"已创建室外街角示例: {filepath}")

if __name__ == "__main__":
    print("=" * 60)
    print("开始更新场景为室外街角...")
    print("=" * 60)
    
    updated, skipped, updated_files, skipped_files = update_emotion_files()
    create_street_corner_example()
    
    print("\n" + "=" * 60)
    print(f"完成! 已更新 {updated} 个文件，跳过 {skipped} 个文件。")
    
    if updated > 0:
        print("\n已更新的文件:")
        for i, filename in enumerate(updated_files, 1):
            print(f"{i}. {filename}")
    
    if skipped > 0:
        print("\n跳过的文件:")
        for i, filename in enumerate(skipped_files, 1):
            print(f"{i}. {filename}")
    
    print("=" * 60)