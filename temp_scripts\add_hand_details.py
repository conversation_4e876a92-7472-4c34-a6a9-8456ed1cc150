#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 手部描述
HAND_DETAILS = "perfect arms, perfect hands, perfect fingers, detailed hands, long fingers, delicate wrists"

# 各表情的手部动作描述
EMOTION_HAND_GESTURES = {
    "joy": "双手优雅地张开，修长的手指微微弯曲，仿佛在庆祝某个成功",
    "sadness": "一只手轻抚额头，完美的手指微微颤抖，另一只手无力地垂放在身侧",
    "anger": "双手紧握成拳，关节处微微泛白，展示出细腻的骨骼结构",
    "fear": "双手不自觉地交叠在胸前，修长的手指紧张地互相缠绕",
    "surprise": "一只手轻掩嘴唇，修长的手指微微张开，展示完美的指甲",
    "disgust": "一只手微微抬起，手掌向外，精致的手指略微弯曲表示拒绝",
    "love": "双手轻放在胸前，修长的手指优雅地交叠，表达深情",
    "admiration": "一只手伸向前方，手指优雅地指向对方，表示赞赏",
    "amusement": "修长的手指轻掩嘴角，另一只手自然地放在腰间",
    "annoyance": "一只手揉捏太阳穴，展示出手指关节的精致细节",
    "approval": "伸出修长的拇指，其他手指优雅地卷曲，展示完美的手形",
    "caring": "双手微微向前伸展，掌心向上，修长的手指微微弯曲表示邀请",
    "confusion": "一只手轻触下巴，修长的手指微微弯曲，展示思考的姿态",
    "curiosity": "一只手手指轻点下唇，另一只手自然地搭在桌面上",
    "desire": "双手轻轻交叠放在膝上，修长的手指微微收紧，展示隐忍的情感",
    "disappointment": "一只手无力地下垂，修长的手指微微弯曲，表达失落",
    "disapproval": "双臂交叉于胸前，修长的手指轻轻敲击手臂，展示不满",
    "embarrassment": "一只手轻触颈侧，修长的手指微微颤抖，表示不安",
    "excitement": "双手略微抬起，修长的手指张开，仿佛在描绘令人兴奋的场景",
    "gratitude": "双手轻放胸前，修长的手指微微交叠成感谢姿势",
    "grief": "一只手掩面，完美的手指微微分开，另一只手无力地垂放",
    "nervousness": "双手在膝前交叠，修长的手指不自觉地互相缠绕或玩弄戒指",
    "neutral": "双手自然地放在身侧，修长的手指微微弯曲，展示放松的状态",
    "optimism": "一只手上扬，修长的手指张开，仿佛在展望美好未来",
    "pride": "一只手轻放在胸前，修长的手指微微张开，展示自信",
    "realization": "一只手突然抬起，食指修长优雅地指向上方，表示灵光乍现",
    "relief": "双手轻放在胸前，修长的手指微微松开，展示放松的状态",
    "remorse": "双手紧握在一起，修长的手指微微交缠，关节处泛白，表示内心挣扎"
}

# 更新提示词模板的关键词
def update_prompt_keywords(text):
    """添加手部细节关键词到提示词"""
    
    # 检查是否已包含相关关键词
    if "perfect hands" in text:
        return text
    
    # 寻找适合添加手部细节的位置
    keywords = [
        "perfect face", "exquisite eyes", "luscious lips", "slender_waist",
        "petite body", "beautiful and youthful", "beautiful and professional"
    ]
    
    for keyword in keywords:
        if keyword in text:
            # 在关键词后添加手部细节
            return text.replace(keyword, f"{keyword}, {HAND_DETAILS}")
    
    # 如果没有找到合适的位置，在aidmaNSFWunlock前添加
    if "aidmaNSFWunlock" in text:
        return text.replace("aidmaNSFWunlock", f"{HAND_DETAILS}, aidmaNSFWunlock")
    
    # 如果以上都失败，简单地在文本末尾添加
    return f"{text}, {HAND_DETAILS}"

def add_hand_gesture(text, emotion):
    """添加手部姿势描述到文本中"""
    
    # 获取当前情感的手部姿势描述
    gesture = EMOTION_HAND_GESTURES.get(emotion, "双手自然放置，展示完美的手指和精致的细节")
    
    # 检查是否已有手部描述
    if "hand" in text.lower() and "finger" in text.lower():
        # 已有手部描述，尝试替换
        patterns = [
            r'(H|h)er hands[^\.]+\.',
            r'(O|o)ne hand[^\.]+\.',
            r'(B|b)oth hands[^\.]+\.',
            r'hands[^\.]+\.'
        ]
        
        for pattern in patterns:
            if re.search(pattern, text):
                text = re.sub(pattern, f"{gesture}.", text)
                return text
    
    # 如果没有找到手部描述或无法替换，在适当位置添加
    pos_markers = [
        ". Her standing confidently",
        ". Posed to fully showcase",
        ". The scene captures",
        ". Professional office lighting"
    ]
    
    for marker in pos_markers:
        if marker in text:
            return text.replace(marker, f". {gesture}. {marker.lstrip('.')}")
    
    # 如果找不到合适的位置，在文本中追加
    return f"{text}\n{gesture}."

def update_emotion_file(file_path, emotion_name):
    """更新表情文件，添加手部描述"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 检查是否已经包含手部描述
        if "perfect hands" in content and "detailed hands" in content:
            print(f"文件 {file_path} 已包含手部描述，跳过")
            return False
        
        # 更新提示词部分
        content = update_prompt_keywords(content)
        
        # 添加手部姿势描述
        content = add_hand_gesture(content, emotion_name)
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已更新文件: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(file_name)
    
    print(f"找到 {len(emotion_files)} 个表情文件需要更新")
    
    # 更新所有表情文件
    updated_files = []
    
    for file_name in emotion_files:
        # 提取表情名（去掉.txt后缀）
        emotion_name = file_name.replace(".txt", "")
        file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
        
        if update_emotion_file(file_path, emotion_name):
            updated_files.append(file_name)
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    update_all_emotion_files()