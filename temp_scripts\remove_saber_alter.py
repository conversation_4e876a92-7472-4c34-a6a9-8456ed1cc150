#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def remove_saber_alter(text):
    """移除saber_alter和fate_(series)关键词"""
    
    # 移除"saber_alter, fate_(series),"
    text = text.replace("saber_alter, fate_(series), ", "")
    text = text.replace("saber_alter, fate_(series),", "")
    
    # 移除"saber_alter,fate_(series),"（无空格）
    text = text.replace("saber_alter,fate_(series),", "")
    
    # 移除单独的"saber_alter, "和"fate_(series), "
    text = text.replace("saber_alter, ", "")
    text = text.replace("fate_(series), ", "")
    
    # 移除单独的"saber_alter,"和"fate_(series),"（逗号结尾）
    text = text.replace("saber_alter,", "")
    text = text.replace("fate_(series),", "")
    
    # 移除单独的"saber_alter"和"fate_(series)"（如果它们是最后一个关键词）
    text = text.replace(", saber_alter", "")
    text = text.replace(", fate_(series)", "")
    
    # 处理可能出现的多余逗号
    text = text.replace(",,", ",")
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 检查是否包含关键词
            if "saber_alter" in content or "fate_(series)" in content:
                # 更新内容
                original_content = content
                content = remove_saber_alter(content)
                
                # 如果内容有变化，则更新文件
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as file:
                        file.write(content)
                    updated += 1
                    updated_files.append(file_name)
                else:
                    skipped += 1
                    skipped_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已移除关键词
            if "saber_alter" in content or "fate_(series)" in content:
                content = remove_saber_alter(content)
                with open(joy_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 