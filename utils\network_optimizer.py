#!/usr/bin/env python3
"""
ComfyUI Network Optimizer for Windows
解决多工作流并发时的网络超时问题
"""

import asyncio
import sys
import os
import platform
import argparse
from typing import Dict, Any

def apply_windows_network_optimizations():
    """应用Windows特定的网络优化"""
    if platform.system() != "Windows":
        print("[INFO] 非Windows系统，跳过Windows特定优化")
        return
    
    print("[INFO] 应用Windows网络优化...")
    
    # 设置Windows特定的asyncio策略
    if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("[SUCCESS] 设置Windows ProactorEventLoop策略")
    
    # 设置环境变量优化网络性能
    optimizations = {
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONASYNCIODEBUG': '0',  # 禁用asyncio调试模式以提高性能
        'AIOHTTP_NO_EXTENSIONS': '0',  # 启用aiohttp扩展
    }
    
    for key, value in optimizations.items():
        os.environ[key] = value
        print(f"[INFO] 设置环境变量 {key}={value}")

def create_optimized_aiohttp_config() -> Dict[str, Any]:
    """创建优化的aiohttp配置"""
    config = {
        # 连接超时设置
        'connector_timeout': 300,  # 5分钟连接超时
        'read_timeout': 600,       # 10分钟读取超时
        'total_timeout': 1800,     # 30分钟总超时
        
        # 连接池设置
        'connector_limit': 100,    # 最大连接数
        'connector_limit_per_host': 30,  # 每个主机最大连接数
        
        # 缓冲区设置
        'read_bufsize': 2**16,     # 64KB读取缓冲区
        'write_bufsize': 2**16,    # 64KB写入缓冲区
        
        # 保活设置
        'keepalive_timeout': 30,   # 保活超时
        'enable_cleanup_closed': True,  # 启用清理已关闭连接
    }
    
    return config

def generate_comfyui_network_args() -> str:
    """生成ComfyUI网络优化参数"""
    args = [
        "--max-upload-size", "1024",  # 增加上传大小限制到1GB
        "--enable-compress-response-body",  # 启用响应压缩
    ]
    
    return " ".join(args)

def create_network_patch():
    """创建网络补丁文件"""
    patch_content = '''
# ComfyUI Network Optimization Patch
import asyncio
import aiohttp
import platform
import sys

def patch_aiohttp_for_windows():
    """为Windows优化aiohttp"""
    if platform.system() == "Windows":
        # 增加默认超时时间
        aiohttp.ClientTimeout.total = 1800  # 30分钟
        aiohttp.ClientTimeout.connect = 300  # 5分钟
        aiohttp.ClientTimeout.sock_read = 600  # 10分钟
        
        # 设置更大的缓冲区
        if hasattr(aiohttp, 'StreamReader'):
            aiohttp.StreamReader.DEFAULT_LIMIT = 2**16  # 64KB
        
        print("[PATCH] Applied Windows aiohttp optimizations")

def patch_asyncio_for_windows():
    """为Windows优化asyncio"""
    if platform.system() == "Windows":
        # 设置事件循环策略
        if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
            policy = asyncio.WindowsProactorEventLoopPolicy()
            asyncio.set_event_loop_policy(policy)
        
        # 增加默认超时
        if hasattr(asyncio, 'wait_for'):
            original_wait_for = asyncio.wait_for
            def patched_wait_for(aw, timeout=None, **kwargs):
                if timeout is None:
                    timeout = 1800  # 30分钟默认超时
                return original_wait_for(aw, timeout, **kwargs)
            asyncio.wait_for = patched_wait_for
        
        print("[PATCH] Applied Windows asyncio optimizations")

# 应用补丁
patch_aiohttp_for_windows()
patch_asyncio_for_windows()
'''
    
    with open('network_patch.py', 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print("[SUCCESS] 创建网络补丁文件: network_patch.py")

def diagnose_network_issues():
    """诊断网络问题"""
    print("=== ComfyUI 网络问题诊断 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查操作系统
    print(f"操作系统: {platform.system()} {platform.version()}")
    
    # 检查asyncio事件循环策略
    try:
        policy = asyncio.get_event_loop_policy()
        print(f"AsyncIO策略: {type(policy).__name__}")
    except Exception as e:
        print(f"AsyncIO策略检查失败: {e}")
    
    # 检查aiohttp版本
    try:
        import aiohttp
        print(f"aiohttp版本: {aiohttp.__version__}")
    except ImportError:
        print("aiohttp未安装")
    
    # 检查网络连接
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(5)
        s.connect(("*******", 53))
        s.close()
        print("网络连接: 正常")
    except Exception as e:
        print(f"网络连接: 异常 - {e}")

def main():
    parser = argparse.ArgumentParser(description="ComfyUI Network Optimizer")
    parser.add_argument("--apply-optimizations", action="store_true", help="应用网络优化")
    parser.add_argument("--create-patch", action="store_true", help="创建网络补丁文件")
    parser.add_argument("--diagnose", action="store_true", help="诊断网络问题")
    parser.add_argument("--get-args", action="store_true", help="获取ComfyUI网络参数")
    parser.add_argument("--get-config", action="store_true", help="获取aiohttp配置")
    
    args = parser.parse_args()
    
    if args.apply_optimizations:
        apply_windows_network_optimizations()
    
    elif args.create_patch:
        create_network_patch()
    
    elif args.diagnose:
        diagnose_network_issues()
    
    elif args.get_args:
        network_args = generate_comfyui_network_args()
        print(network_args)
    
    elif args.get_config:
        config = create_optimized_aiohttp_config()
        for key, value in config.items():
            print(f"{key}: {value}")
    
    else:
        # 默认：应用所有优化
        print("=== ComfyUI 网络优化工具 ===")
        apply_windows_network_optimizations()
        create_network_patch()
        
        print("\n=== 优化建议 ===")
        print("1. 在ComfyUI启动前运行此脚本")
        print("2. 使用生成的网络补丁文件")
        print("3. 添加网络优化参数到启动命令")
        print("4. 如果问题持续，请运行诊断模式")

if __name__ == "__main__":
    main()
