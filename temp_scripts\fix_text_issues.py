#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

def fix_text_issues(file_path):
    """修复文件中的文本问题"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 1. 删除"with zipper partially undone"并处理多余空格
        content = re.sub(r'with zipper partially undone\s*', ' ', content)
        # 确保删除后不会出现多个连续空格
        content = re.sub(r'\s{2,}', ' ', content)
        
        # 2. 修复AidmaNSFWunlock前面多余的点号
        # 处理模式：". AidmaNSFWunlock"
        content = re.sub(r'\.\s*\.\s*AidmaNSFWunlock', '. AidmaNSFWunlock', content)
        # 处理模式："photo, . AidmaNSFWunlock"
        content = re.sub(r'photo,\s*\.\s*AidmaNSFWunlock', 'photo. AidmaNSFWunlock', content)
        # 处理模式："photo . AidmaNSFWunlock"
        content = re.sub(r'photo\s+\.\s*AidmaNSFWunlock', 'photo. AidmaNSFWunlock', content)
        
        # 检查内容是否有变化
        if content != original_content:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            return True, content
        else:
            return False, content
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False, None

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    files_updated = []
    files_skipped = []
    
    # 检查目录是否存在
    if not os.path.exists(directory):
        print(f"目录 {directory} 不存在")
        return files_updated, files_skipped
    
    # 获取所有txt文件
    text_files = []
    for file_name in os.listdir(directory):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            text_files.append((file_name, os.path.join(directory, file_name)))
    
    print(f"找到 {len(text_files)} 个文本文件需要处理")
    
    # 修复所有文件
    for file_name, file_path in text_files:
        print(f"处理文件: {file_name}")
        updated, content = fix_text_issues(file_path)
        if updated:
            files_updated.append(file_name)
            print(f"  - 已更新文件")
            
            # 显示修改后的前100个字符，用于检查
            if content:
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"  - 更新后内容预览: {preview}")
        else:
            files_skipped.append(file_name)
            print(f"  - 无需更新")
    
    return files_updated, files_skipped

def check_files(directory, pattern):
    """检查指定目录中的文件是否包含特定模式"""
    files_with_issues = []
    
    # 检查目录是否存在
    if not os.path.exists(directory):
        print(f"目录 {directory} 不存在")
        return files_with_issues
    
    # 获取所有txt文件
    for file_name in os.listdir(directory):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            file_path = os.path.join(directory, file_name)
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    if re.search(pattern, content):
                        files_with_issues.append(file_name)
            except Exception as e:
                print(f"检查文件 {file_path} 时出错: {str(e)}")
    
    return files_with_issues

def main():
    """主函数"""
    # 先检查文件中是否存在问题
    print("检查文件中是否存在'with zipper partially undone'...")
    files_with_zipper = check_files(EMOTION_PROMPTS_DIR, r'with zipper partially undone')
    print(f"找到 {len(files_with_zipper)} 个文件包含'with zipper partially undone'")
    if files_with_zipper:
        print("包含此短语的文件:")
        for file_name in files_with_zipper:
            print(f"  - {file_name}")
    
    print("\n检查文件中是否存在AidmaNSFWunlock前面多余的点号...")
    files_with_dot_issue = check_files(EMOTION_PROMPTS_DIR, r'\.\s*\.\s*AidmaNSFWunlock|photo,\s*\.\s*AidmaNSFWunlock|photo\s+\.\s*AidmaNSFWunlock')
    print(f"找到 {len(files_with_dot_issue)} 个文件存在点号问题")
    if files_with_dot_issue:
        print("存在此问题的文件:")
        for file_name in files_with_dot_issue:
            print(f"  - {file_name}")
    
    # 处理情感文件目录
    print(f"\n处理 {EMOTION_PROMPTS_DIR} 目录中的文件...")
    emotion_updated, emotion_skipped = process_directory(EMOTION_PROMPTS_DIR)
    
    # 处理示例文件目录
    print(f"\n处理 {EXAMPLES_DIR} 目录中的文件...")
    examples_updated, examples_skipped = process_directory(EXAMPLES_DIR)
    
    # 输出总结
    print("\n修复完成！")
    print(f"情感文件: 已更新 {len(emotion_updated)} 个文件, 跳过 {len(emotion_skipped)} 个文件")
    print(f"示例文件: 已更新 {len(examples_updated)} 个文件, 跳过 {len(examples_skipped)} 个文件")
    
    # 显示已更新的文件列表
    if emotion_updated:
        print("\n已更新的情感文件:")
        for file_name in emotion_updated:
            print(f"  - {file_name}")
    
    if examples_updated:
        print("\n已更新的示例文件:")
        for file_name in examples_updated:
            print(f"  - {file_name}")

if __name__ == "__main__":
    main() 