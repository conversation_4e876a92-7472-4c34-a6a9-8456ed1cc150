import os

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 更新腿部描述
old_leg_description = "Sheer black pantyhose showing legs with subtle shine."
new_leg_description = "Sheer black pantyhose accentuating full, shapely thighs with subtle shine."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换腿部描述
        if old_leg_description in content:
            updated_content = content.replace(old_leg_description, new_leg_description)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print(f"Updated legs description in: {filename}")

print("All files have been updated with better leg proportions.")