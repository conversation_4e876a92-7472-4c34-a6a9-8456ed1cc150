#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 需要移除的手部关键词
HAND_KEYWORDS = [
    "perfect arms", 
    "perfect hands", 
    "perfect fingers", 
    "detailed hands", 
    "long fingers", 
    "delicate wrists",
    "hands elegantly",
    "slender fingers",
    "hands gently",
    "hands clenched",
    "hand gently",
    "hand massaging",
    "hand lightly",
    "hand hanging",
    "hand suddenly",
    "hand raised",
    "hands placed",
    "hands slightly",
    "hands crossed",
    "arms crossed",
    "extended slender"
]

# 需要添加的新描述
NEW_HAND_DESCRIPTION = "hands behind back"

def update_emotion_file(file_path):
    """更新表情文件，移除手部描述并添加手放到身后的描述"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 移除手部相关描述（更全面的模式）
        hand_patterns = [
            r'Her hands[^\.]+\.',
            r'One hand[^\.]+\.',
            r'Both hands[^\.]+\.',
            r'Slender fingers[^\.]+\.',
            r'Arms crossed[^\.]+\.',
            r'Extended slender[^\.]+\.'
        ]
        
        for pattern in hand_patterns:
            content = re.sub(pattern, "", content)
        
        # 移除手部关键词
        for keyword in HAND_KEYWORDS:
            content = content.replace(f", {keyword}", "")
            content = content.replace(f"{keyword}, ", "")
            content = content.replace(f"{keyword}", "")
        
        # 清理连续的逗号和空格
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r'\s+', ' ', content)
        
        # 添加手放到身后的描述
        # 在适当的位置添加，例如在描述姿势的部分
        pos_markers = [
            "Posed to fully showcase",
            "Standing by a sleek office desk",
            "Long straight dark brown wavy hair"
        ]
        
        added = False
        for marker in pos_markers:
            if marker in content:
                content = content.replace(marker, f"{NEW_HAND_DESCRIPTION}. {marker}")
                added = True
                break
        
        # 如果没有找到合适的位置，在aidmaNSFWunlock前添加
        if not added and "aidmaNSFWunlock" in content:
            content = content.replace("aidmaNSFWunlock", f"{NEW_HAND_DESCRIPTION}, aidmaNSFWunlock")
        
        # 检查是否有变化
        if content == original_content:
            print(f"文件 {file_path} 没有变化")
            return False
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已更新文件: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(os.path.join(EMOTION_PROMPTS_DIR, file_name))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要更新")
    
    # 更新所有表情文件
    updated_files = []
    
    for file_path in emotion_files:
        if update_emotion_file(file_path):
            updated_files.append(os.path.basename(file_path))
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    update_all_emotion_files()