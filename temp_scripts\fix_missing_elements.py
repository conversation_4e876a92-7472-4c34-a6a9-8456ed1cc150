import os

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 关键元素检查列表
essential_elements = {
    "glasses": "Wearing thin-framed round black glasses that give an intellectual look.",
    "shirt": "Crisp white button-up shirt with collar, sleeves rolled up to mid-forearm.",
    "tie": "Black tie loosely hanging down from collar.",
    "skirt": "Tight black miniskirt hugging hips.",
    "stockings": "Sheer black pantyhose showing legs with subtle shine.",
    "hair": "Long straight black hair with neat bangs across forehead.",
    "hand": "One hand gently touching collar or tie in thoughtful gesture.",
    "lighting": "Soft natural lighting in bright minimalist interior setting."
}

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 检查每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        lines = content.split('\n')
        main_line = lines[0]  # 保留第一行
        
        # 检查每个必要元素
        missing_elements = {}
        for key, element in essential_elements.items():
            element_short = element.split('.')[0]  # 取句子的第一部分进行匹配
            if not any(element_short in line for line in lines):
                missing_elements[key] = element
        
        # 如果有缺失元素，重构文件
        if missing_elements:
            print(f"Fixing {filename}...")
            
            # 保留第一行(主描述)和面部表情相关行
            new_lines = [main_line]
            
            # 添加关键元素，按固定顺序
            new_lines.append(essential_elements["glasses"])
            
            # 提取眼睛和嘴巴/表情描述
            eyes_line = None
            expression_line = None
            
            for line in lines[1:]:
                if "eye" in line.lower() and "through lenses" in line.lower():
                    eyes_line = line
                elif any(term in line.lower() for term in ["mouth", "smile", "lip", "frown"]):
                    expression_line = line
            
            # 添加眼睛和表情描述
            if eyes_line:
                new_lines.append(eyes_line)
            if expression_line:
                new_lines.append(expression_line)
            
            # 添加服装描述
            new_lines.append(essential_elements["shirt"])
            new_lines.append(essential_elements["tie"])
            new_lines.append(essential_elements["skirt"])
            new_lines.append(essential_elements["stockings"])
            new_lines.append(essential_elements["hair"])
            new_lines.append(essential_elements["hand"])
            new_lines.append(essential_elements["lighting"])
            
            # 保留其他特殊姿势、相机角度等行
            for line in lines:
                if (("body" in line.lower() or 
                     "camera" in line.lower() or 
                     "shot" in line.lower() or 
                     "angle" in line.lower() or 
                     "focus" in line.lower() or
                     "posture" in line.lower()) and 
                    line not in new_lines):
                    new_lines.append(line)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write('\n'.join(new_lines))
            
            print(f"  Fixed missing elements: {', '.join(missing_elements.keys())}")

print("All files have been checked and fixed for missing elements.")