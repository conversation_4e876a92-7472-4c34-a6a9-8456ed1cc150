import re

# 修复触发文件中的重复内容
try:
    # 读取触发文件
    with open('emotion_prompts/trigger.txt', 'r', encoding='utf-8') as file:
        trigger_content = file.read()
    
    # 修复裙子描述重复问题
    hemline_pattern = r"(the skirt's hemline creating a deliberate and obvious gap above where her separate undergarment begins)(, the skirt's hemline creating a deliberate and obvious gap above where her separate undergarment begins)+"
    trigger_content = re.sub(hemline_pattern, r"\1", trigger_content)
    
    # 修复关键词重复问题
    keyword_pattern = r"(show_intimate_details underwear_visible exposed separate_garments miniskirt_not_pants distinct_underwear_visible hands_away_from_body unobstructed_view)( separate_garments miniskirt_not_pants distinct_underwear_visible hands_away_from_body unobstructed_view)+"
    trigger_content = re.sub(keyword_pattern, r"\1", trigger_content)
    
    # 写回文件
    with open('emotion_prompts/trigger.txt', 'w', encoding='utf-8') as file:
        file.write(trigger_content)
    
    print("已修复trigger.txt文件中的重复内容")
except Exception as e:
    print(f"修复trigger.txt文件时出错: {str(e)}")

# 检查所有情绪提示文件是否有类似问题
import glob

# 获取所有情绪提示文件
prompt_files = glob.glob('emotion_prompts/*.txt')
updated_count = 0

for file_path in prompt_files:
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 修复裙子描述重复问题
        hemline_pattern = r"(the skirt's hemline creating a deliberate and obvious gap above where her separate undergarment begins)(, the skirt's hemline creating a deliberate and obvious gap above where her separate undergarment begins)+"
        new_content = re.sub(hemline_pattern, r"\1", content)
        
        # 如果内容有变化，写回文件
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
            print(f"已修复文件: {file_path}")
            updated_count += 1
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")

print(f"\n修复完成! 共修复 {updated_count + 1} 个文件") 