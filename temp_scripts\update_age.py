import os
import re

# 文件夹路径
folder_path = 'emotion_prompts'

# 年龄描述
old_age = "23-year-old"
new_age = "19-year-old"

# 确保全身拍摄
full_body_descriptions = [
    "full body shot",
    "full-body portrait",
    "full length photo",
    "showing entire body from head to toe"
]

camera_angle_pattern = r'(.*camera angle.*)'
shot_type_pattern = r'(Medium close-up|Medium shot|Close-up portrait|Three-quarter)'

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 统计
updated_count = 0
skipped_count = 0

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 替换年龄
            content = content.replace(old_age, new_age)
            
            # 分析并确保有全身拍摄描述
            lines = content.split('\n')
            has_full_body = False
            camera_angle_line_index = -1
            shot_type_line_index = -1
            
            # 查找现有的镜头描述行
            for i, line in enumerate(lines):
                if any(phrase in line.lower() for phrase in ["camera angle", "shot", "portrait", "photo"]):
                    if "camera angle" in line.lower():
                        camera_angle_line_index = i
                    else:
                        shot_type_line_index = i
                
                # 检查是否已经有全身拍摄描述
                if any(full_body in line.lower() for full_body in ["full body", "entire body", "head to toe"]):
                    has_full_body = True
            
            # 如果没有全身拍摄描述，添加一个
            if not has_full_body:
                # 如果有镜头类型描述行，替换它
                if shot_type_line_index != -1:
                    lines[shot_type_line_index] = "Full body shot showing entire figure from head to toe."
                # 如果有相机角度行，在它之前添加全身描述
                elif camera_angle_line_index != -1:
                    lines.insert(camera_angle_line_index, "Full body shot showing entire figure from head to toe.")
                # 如果都没有，添加到末尾
                else:
                    lines.append("Full body shot showing entire figure from head to toe.")
            
            # 重新组合内容
            updated_content = '\n'.join(lines)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print(f"已更新年龄为19岁并确保全身拍摄: {filename}")
            updated_count += 1
                
        except Exception as e:
            print(f"处理文件时出错 {filename}: {str(e)}")
            skipped_count += 1

print(f"\n更新完成: {updated_count} 个文件已更新，{skipped_count} 个文件已跳过")
print("所有文件已更新，年龄改为19岁，并确保所有提示词包含全身拍摄描述。")
