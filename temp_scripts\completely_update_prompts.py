#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 新的角色名字
NEW_CHARACTER_NAME = "qingyi"

# 新的服装描述（英文）
NEW_OUTFIT = "wearing a white halter bikini top and ultra-short denim shorts with zipper partially undone"

# 新的场景描述（客厅）
NEW_SCENE = "in a modern, stylish living room with comfortable furniture and warm lighting"

# 新的手部描述
HAND_POSITION = "hands behind back"

# 基础提示词模板
PROMPT_TEMPLATE = "{character}, 20-year-old, Beautiful woman {emotion_description} {outfit}. {scene}. {hand_position}. Posed to fully showcase her outfit, with no part of her body obscured or hidden from view. Long straight dark brown wavy hair with side-swept bangs framing her face. Soft, even studio lighting with subtle highlights creating gentle shadows that complement her features. {additional_description} High resolution fashion editorial photograph, Ultra high quality, masterpiece, ultra high resolution, realistic, photorealistic, DSLR photo, aidmaNSFWunlock"

# 表情描述
EMOTION_DESCRIPTIONS = {
    "joy": "radiating pure joy with ecstatic expression. Eyes crinkled and bright with happiness. Broad, uninhibited smile creating laugh lines around eyes",
    "sadness": "expressing profound sadness with melancholic expression. Downcast eyes with slight moistness from unshed tears. Corners of mouth turned down in unmistakable sorrow",
    "anger": "with intense facial expression showing anger. Furrowed brows and narrowed eyes with a fierce gaze. Slightly opened mouth as if in mid-shout",
    "fear": "showing visible fear with wide eyes and parted lips. Eyebrows raised high in alarm. Body slightly tensed as if ready to flee",
    "surprise": "with a look of genuine surprise. Widened eyes and raised eyebrows. Mouth slightly open in an 'O' shape",
    "disgust": "expressing clear disgust with wrinkled nose and curled upper lip. Slightly squinted eyes and furrowed brow",
    "love": "showing tender love with soft gaze and gentle smile. Relaxed eyebrows and slightly tilted head",
    "admiration": "showing clear admiration with slightly widened eyes. Head tilted slightly upward with an attentive gaze. Slight, appreciative smile",
    "amusement": "showing genuine amusement with playful expression. Wide smile with teeth showing and eyes crinkled in genuine laughter",
    "annoyance": "showing mild annoyance with subtle eye roll. Slightly pursed lips and tense jawline",
    "approval": "showing clear approval with a confident expression. Eyes expressive and full of emotion. Slight nod of head and affirming smile",
    "caring": "expressing genuine care with soft, concerned expression. Gentle gaze with slightly furrowed brows. Compassionate smile with relaxed features",
    "confusion": "showing evident confusion with furrowed brow. Head slightly tilted with questioning gaze. Lips slightly parted as if about to ask a question",
    "curiosity": "expressing natural curiosity with tilted head and slightly narrowed, focused eyes. Slight smile showing interest. Eyebrows slightly raised",
    "desire": "expressing subtle desire with seductive gaze. Full body shot showing entire figure from head to toe. Slightly parted lips with hint of smile",
    "disappointment": "showing clear disappointment with downturned mouth and slightly lowered gaze. Shoulders subtly slumped. Eyebrows drawn together slightly",
    "disapproval": "expressing clear disapproval with stern expression. One eyebrow raised skeptically",
    "embarrassment": "showing genuine embarrassment with flushed cheeks and averted gaze. Awkward half-smile and slightly hunched shoulders",
    "excitement": "radiating pure excitement with bright, wide eyes. Broad smile showing teeth. Animated facial expression with raised eyebrows",
    "gratitude": "expressing deep gratitude with sincere smile. Warm gaze and slightly bowed head. Relaxed, open facial features",
    "grief": "showing profound grief with slightly parted lips and distant gaze. Visible tension around eyes and forehead. Face appearing drawn and tired",
    "nervousness": "displaying clear nervousness with slightly widened eyes. Biting lower lip gently. Tension visible in facial muscles",
    "neutral": "with a perfectly neutral expression. Relaxed facial features with neither positive nor negative emotion. Direct gaze with relaxed eyelids",
    "optimism": "radiating optimism with bright, forward-looking gaze. Gentle, confident smile. Relaxed, open facial features",
    "pride": "showing unmistakable pride with raised chin and confident smile. Slightly narrowed eyes with direct gaze. Relaxed yet assertive posture",
    "realization": "with sudden realization visible in her expression. Widened eyes and slightly parted lips. Eyebrows raised in epiphany",
    "relief": "expressing profound relief with relaxed brow and deep exhale. Shoulders visibly lowered from released tension. Gentle, grateful smile",
    "remorse": "showing deep remorse with downcast eyes and furrowed brow. Corners of mouth turned down. Face showing subtle signs of emotional pain"
}

# 表情特定的额外描述
ADDITIONAL_DESCRIPTIONS = {
    "joy": "Open, expansive body language showing enthusiasm. Three-quarter or full body shot capturing joyful energy",
    "sadness": "Full body shot showing entire figure from head to toe. Camera angle slightly from above emphasizing vulnerability",
    "anger": "Full body shot showing entire figure from head to toe",
    "fear": "Full body shot with visible tension in posture",
    "surprise": "Body language showing sudden, arrested movement",
    "disgust": "Full body shot showing entire figure from head to toe",
    "love": "Warm body language with slight lean forward",
    "admiration": "Attentive posture with slightly raised chin",
    "amusement": "Natural, uninhibited body language showing complete enjoyment. Full body shot showing entire figure from head to toe",
    "annoyance": "Tense shoulders with slight defensive posture",
    "approval": "Relaxed posture with open body language. Full body shot showing entire figure from head to toe",
    "caring": "Open, receptive body language with slight forward lean",
    "confusion": "Uncertain posture with slight head tilt",
    "curiosity": "Engaged body language with slight forward lean",
    "desire": "Half-lidded eyes looking directly at viewer with intense focus. Relaxed yet inviting body posture",
    "disappointment": "Subtly closed-off body language. Full body shot",
    "disapproval": "Full body shot showing entire figure from head to toe",
    "embarrassment": "Slightly protective body language. Full body shot",
    "excitement": "Energetic, animated body language",
    "gratitude": "Open, receptive body language. Full body shot",
    "grief": "Subdued body language with slight inward curl. Full body shot",
    "nervousness": "Slightly tense posture with controlled movements",
    "neutral": "Balanced, centered posture with relaxed shoulders",
    "optimism": "Open, forward-facing posture. Full body shot",
    "pride": "Confident posture with open stance. Full body shot",
    "realization": "Body language showing moment of insight. Full body shot",
    "relief": "Visibly relaxed posture with open body language",
    "remorse": "Slightly closed body language with protective elements"
}

def create_updated_prompt(emotion, template):
    """为指定的表情创建更新后的提示词"""
    emotion_description = EMOTION_DESCRIPTIONS.get(emotion, "with neutral expression")
    additional_description = ADDITIONAL_DESCRIPTIONS.get(emotion, "Full body shot")
    
    prompt = template.format(
        character=NEW_CHARACTER_NAME,
        emotion_description=emotion_description,
        outfit=NEW_OUTFIT,
        scene=NEW_SCENE,
        hand_position=HAND_POSITION,
        additional_description=additional_description
    )
    
    return prompt

def update_emotion_file(file_path, emotion_name):
    """完全更新表情文件内容"""
    try:
        # 创建新的提示词
        new_content = create_updated_prompt(emotion_name, PROMPT_TEMPLATE)
        
        # 写入新内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(new_content)
        
        print(f"已更新文件: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        print(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
        return False

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_name = file_name.replace(".txt", "")
            file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
            emotion_files.append((emotion_name, file_path))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要更新\n")
    
    # 更新所有表情文件
    updated_files = []
    
    for emotion_name, file_path in emotion_files:
        if update_emotion_file(file_path, emotion_name):
            updated_files.append(os.path.basename(file_path))
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

def check_updated_file(file_path):
    """检查更新后的文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        print(f"\n文件 {os.path.basename(file_path)} 的更新后内容:")
        print("-" * 50)
        print(content)
        print("-" * 50)
    except Exception as e:
        print(f"读取文件 {os.path.basename(file_path)} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_emotion_files()
    
    # 检查几个文件的更新结果作为示例
    example_files = [
        os.path.join(EMOTION_PROMPTS_DIR, "joy.txt"),
        os.path.join(EMOTION_PROMPTS_DIR, "sadness.txt"),
        os.path.join(EMOTION_PROMPTS_DIR, "desire.txt")
    ]
    
    for example_file in example_files:
        if os.path.exists(example_file):
            check_updated_file(example_file)