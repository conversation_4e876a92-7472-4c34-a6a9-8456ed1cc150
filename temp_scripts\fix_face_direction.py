import os

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 要添加的描述
face_direction = "Face directly towards camera with full eye contact."
camera_angle = "Straight-on camera angle capturing full facial details."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        # 检查是否包含所需描述
        has_face_direction = False
        has_camera_angle = False
        has_eyes_description = False
        
        for line in lines:
            if face_direction in line:
                has_face_direction = True
            if camera_angle in line:
                has_camera_angle = True
            if "eyes" in line.lower() or "eye" in line.lower():
                has_eyes_description = True
        
        # 更新文件内容
        updated = False
        updated_lines = []
        
        # 添加默认眼睛描述（如果没有）
        default_eyes = "Eyes looking directly at viewer through lenses with intense expression."
        
        # 处理每一行并进行必要的添加
        i = 0
        while i < len(lines):
            line = lines[i]
            updated_lines.append(line)
            
            # 在眼睛描述后添加面部朝向（如果缺失）
            if "through lenses" in line.lower() and not has_face_direction:
                updated_lines.append(face_direction + "\n")
                has_face_direction = True
                updated = True
            
            # 在光线描述后添加相机角度（如果缺失）
            if "lighting" in line.lower() and not has_camera_angle:
                updated_lines.append(camera_angle + "\n")
                has_camera_angle = True
                updated = True
            
            i += 1
        
        # 如果没有眼睛描述，在适当位置添加
        if not has_eyes_description:
            new_lines = []
            for i, line in enumerate(updated_lines):
                new_lines.append(line)
                # 在眼镜描述后添加眼睛描述
                if "glasses" in line.lower() and i+1 < len(updated_lines) and "eye" not in updated_lines[i+1].lower():
                    new_lines.append(default_eyes + "\n")
                    # 添加面部朝向（如果还没有添加）
                    if not has_face_direction:
                        new_lines.append(face_direction + "\n")
                        has_face_direction = True
                    updated = True
            updated_lines = new_lines
        
        # 如果仍然缺少面部朝向描述，在嘴部描述后添加
        if not has_face_direction:
            new_lines = []
            for i, line in enumerate(updated_lines):
                new_lines.append(line)
                if any(word in line.lower() for word in ["mouth", "smile", "lip"]) and i+1 < len(updated_lines) and face_direction not in updated_lines[i+1]:
                    new_lines.append(face_direction + "\n")
                    updated = True
            updated_lines = new_lines
        
        # 如果仍然缺少相机角度描述，在结尾添加
        if not has_camera_angle:
            updated_lines.append(camera_angle + "\n")
            updated = True
        
        # 只有在有更新时才写回文件
        if updated:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.writelines(updated_lines)
            
            print(f"Updated face direction in: {filename}")

print("All files have been checked and updated to ensure face is directed towards camera.")