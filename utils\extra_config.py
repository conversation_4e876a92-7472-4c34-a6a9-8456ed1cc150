import os
import yaml
import folder_paths
import logging

def load_extra_path_config(yaml_path):
    with open(yaml_path, 'r', encoding='utf-8') as stream:
        config = yaml.safe_load(stream)
    yaml_dir = os.path.dirname(os.path.abspath(yaml_path))
    for c in config:
        conf = config[c]
        if conf is None:
            continue
        base_path = None
        if "base_path" in conf:
            base_path = conf.pop("base_path")
            base_path = os.path.expandvars(os.path.expanduser(base_path))
            if not os.path.isabs(base_path):
                base_path = os.path.abspath(os.path.join(yaml_dir, base_path))
        is_default = False
        if "is_default" in conf:
            is_default = conf.pop("is_default")
        for x in conf:
            for y in conf[x].split("\n"):
                if len(y) == 0:
                    continue
                full_path = y
                if base_path:
                    full_path = os.path.join(base_path, full_path)
                elif not os.path.isabs(full_path):
                    full_path = os.path.abspath(os.path.join(yaml_dir, y))
                normalized_path = os.path.normpath(full_path)
                logging.info("Adding extra search path {} {}".format(x, normalized_path))
                folder_paths.add_model_folder_path(x, normalized_path, is_default)


def load_gpu_profile_config(yaml_path):
    """
    Load GPU optimization profile configuration from YAML file.

    Args:
        yaml_path (str): Path to the GPU profiles YAML file

    Returns:
        dict: Complete GPU profiles configuration

    Raises:
        FileNotFoundError: If the configuration file doesn't exist
        yaml.YAMLError: If the YAML file is malformed
        KeyError: If required configuration sections are missing
    """
    try:
        with open(yaml_path, 'r', encoding='utf-8') as stream:
            config = yaml.safe_load(stream)

        if not isinstance(config, dict):
            raise ValueError("GPU profile configuration must be a dictionary")

        # Validate that we have at least the default profile
        if 'default' not in config:
            raise KeyError("GPU profile configuration must contain a 'default' profile")

        logging.info("Loaded GPU profile configuration from: {}".format(yaml_path))
        return config

    except FileNotFoundError:
        logging.error("GPU profile configuration file not found: {}".format(yaml_path))
        raise
    except yaml.YAMLError as e:
        logging.error("Error parsing GPU profile configuration: {}".format(e))
        raise
    except Exception as e:
        logging.error("Unexpected error loading GPU profile configuration: {}".format(e))
        raise


def get_gpu_profile(config, profile_name):
    """
    Get a specific GPU profile from the configuration.

    Args:
        config (dict): GPU profiles configuration
        profile_name (str): Name of the profile to retrieve

    Returns:
        dict: GPU profile configuration, falls back to 'default' if profile not found
    """
    if profile_name in config:
        logging.info("Using GPU profile: {}".format(profile_name))
        return config[profile_name]
    else:
        logging.warning("GPU profile '{}' not found, falling back to 'default'".format(profile_name))
        return config.get('default', {})


def validate_gpu_profile(profile):
    """
    Validate GPU profile configuration parameters.

    Args:
        profile (dict): GPU profile configuration to validate

    Returns:
        bool: True if profile is valid, False otherwise
    """
    required_fields = ['vram_mode', 'precision_unet']

    for field in required_fields:
        if field not in profile:
            logging.error("Required field '{}' missing from GPU profile".format(field))
            return False

    # Validate vram_mode values
    valid_vram_modes = ['gpu-only', 'highvram', 'normalvram', 'lowvram', 'novram', 'cpu']
    if profile['vram_mode'] not in valid_vram_modes:
        logging.error("Invalid vram_mode '{}'. Must be one of: {}".format(
            profile['vram_mode'], ', '.join(valid_vram_modes)))
        return False

    # Validate precision settings
    precision_fields = ['precision_unet', 'precision_vae', 'precision_text_enc']
    valid_precisions = ['fp32-unet', 'fp16-unet', 'bf16-unet', 'fp8_e4m3fn-unet', 'fp8_e5m2-unet',
                       'fp32-vae', 'fp16-vae', 'bf16-vae', 'cpu-vae',
                       'fp32-text-enc', 'fp16-text-enc', 'bf16-text-enc', 'fp8_e4m3fn-text-enc', 'fp8_e5m2-text-enc']

    for field in precision_fields:
        if field in profile and profile[field] not in valid_precisions:
            logging.warning("Potentially invalid precision setting '{}' for field '{}'".format(
                profile[field], field))

    logging.info("GPU profile validation passed")
    return True
