#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 修复joy.txt文件中的描述
def fix_joy_txt():
    joy_path = "emotion_prompts/joy.txt"
    
    try:
        with open(joy_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 修复错误的服装描述
        if "with highly transparent black lace lingerie visible underneath with white ultra-short mini skirt underneath" in content:
            content = content.replace(
                "with highly transparent black lace lingerie visible underneath with white ultra-short mini skirt underneath", 
                "with highly transparent black lace lingerie visible underneath and white ultra-short mini skirt"
            )
            
            with open(joy_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"已修复文件: {joy_path}")
        else:
            print(f"文件 {joy_path} 不需要修复")
    except Exception as e:
        print(f"处理文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    fix_joy_txt() 