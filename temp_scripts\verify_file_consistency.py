#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob
import difflib

# 定义表情文件目录
EMOTIONS_DIR = "emotion_prompts"

# 要排除的文件
EXCLUDE_FILES = [
    "negative_prompt.txt", "trigger.txt", "skirt_trigger.txt", 
    "WHITE_UNDERWEAR.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", 
    "WIDE_LEG_POSE.txt", "EXTREME_SKIRT.txt", "EXTREME_NEGATIVE.txt",
    "COMBINED_SOLUTION.txt"
]

def extract_clothing_description(filename):
    """提取文件中的服装描述"""
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找包含"Wearing"的行
    lines = content.split('\n')
    for line in lines:
        if line.strip().startswith('Wearing'):
            return line.strip()
    
    return None

def check_exact_descriptions():
    """验证所有文件的服装描述是否完全一致"""
    # 获取所有表情文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 排除特殊文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"验证 {len(emotion_files)} 个表情文件的服装描述...")
    
    # 标准服装描述
    standard_description = (
        "Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage, "
        "paired with a glossy black ultra-short mini skirt that sits low on her hips, "
        "clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs."
    )
    
    # 记录结果
    matching_files = []
    mismatching_files = []
    missing_files = []
    
    for file in emotion_files:
        filename = os.path.basename(file)
        description = extract_clothing_description(file)
        
        if description is None:
            missing_files.append(filename)
            continue
        
        # 规范化描述（删除空白）
        description = description.strip()
        standard = standard_description.strip()
        
        if description == standard:
            matching_files.append(filename)
        else:
            mismatching_files.append((filename, description))
    
    # 打印结果
    print(f"\n✓ {len(matching_files)} 个文件的服装描述与标准一致")
    
    if missing_files:
        print(f"\n! {len(missing_files)} 个文件中未找到服装描述:")
        for filename in missing_files:
            print(f"  - {filename}")
    
    if mismatching_files:
        print(f"\n! {len(mismatching_files)} 个文件的服装描述不一致:")
        for filename, description in mismatching_files:
            print(f"\n文件: {filename}")
            for i, s in enumerate(difflib.ndiff(standard, description)):
                if s[0] == ' ': continue
                elif s[0] == '-':
                    print(f"应该有: {s[2:]}")
                elif s[0] == '+':
                    print(f"实际有: {s[2:]}")
    
    # 打印总结
    if len(mismatching_files) == 0 and len(missing_files) == 0:
        print("\n✓ 所有文件的服装描述已完全一致且符合标准!")
    else:
        print("\n! 发现不一致的服装描述，建议修复")

def fix_remaining_issues():
    """修复剩余的服装描述问题"""
    # 获取所有表情文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 排除特殊文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"\n尝试修复 {len(emotion_files)} 个表情文件中可能残留的服装描述问题...")
    
    # 标准服装描述
    standard_description = (
        "Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage, "
        "paired with a glossy black ultra-short mini skirt that sits low on her hips, "
        "clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs."
    )
    
    # 修复计数
    fixed_count = 0
    
    for file in emotion_files:
        filename = os.path.basename(file)
        
        with open(file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找和替换整行的服装描述
        lines = content.split('\n')
        fixed = False
        
        for i, line in enumerate(lines):
            if line.strip().startswith('Wearing'):
                if line.strip() != standard_description:
                    lines[i] = standard_description
                    fixed = True
                    break
        
        if fixed:
            new_content = '\n'.join(lines)
            with open(file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"  ✓ 已修复 {filename} 中的服装描述")
            fixed_count += 1
    
    print(f"\n完成最终修复! 修复了 {fixed_count} 个文件的服装描述。")

if __name__ == "__main__":
    check_exact_descriptions()
    fix_remaining_issues()
    print("\n===== 再次验证一致性 =====")
    check_exact_descriptions() 