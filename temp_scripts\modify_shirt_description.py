import os
import re

# 文件夹路径
folder_path = 'emotion_prompts'

# 旧的衬衫描述
old_shirt_description = "Sheer thin white button-up shirt with deep V-neck, no bra underneath, bare breasts and nipples subtly visible through the translucent fabric, sleeves rolled up to mid-forearm."

# 新的衬衫描述 - 使用委婉的表达来描述半遮住乳头和露出乳晕的效果
new_shirt_description = "Sheer thin white button-up shirt with extremely deep V-neck opening almost to navel, edges of fabric barely covering the rose-tinted centers while exposing most of the delicate skin around them, unbuttoned enough to create a tantalizing partial view that reveals yet conceals the most sensitive parts, sleeves rolled up to mid-forearm."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 统计
updated_count = 0
skipped_count = 0

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 替换衬衫描述
            if old_shirt_description in content:
                updated_content = content.replace(old_shirt_description, new_shirt_description)
                
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                
                print(f"已更新衬衫描述: {filename}")
                updated_count += 1
            else:
                print(f"跳过 (未找到匹配的描述): {filename}")
                skipped_count += 1
                
        except Exception as e:
            print(f"处理文件时出错 {filename}: {str(e)}")
            skipped_count += 1

print(f"\n更新完成: {updated_count} 个文件已更新，{skipped_count} 个文件已跳过")
print("所有文件已更新，衬衫描述已修改为半遮住乳头，露出粉色乳晕的效果。")
