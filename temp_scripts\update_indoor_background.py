#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 情绪提示文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 排除不需要修改的文件
EXCLUDE_FILES = [
    "README.md", "PLEATED_SKIRT_GUIDE.md", "PLEATED_SKIRT_PREVIEW.md", 
    "negative_prompt.txt", "FINAL_INSTRUCTIONS.md", "COMBINED_SOLUTION.txt",
    "LEG_POSE_GUIDE.md", "WHITE_UNDERWEAR.txt", "skirt_trigger.txt",
    "EXTREME_SKIRT.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", "WIDE_LEG_POSE.txt",
    "FINAL_SOLUTION.md", "LIGHTING_GUIDE.md", "EXTREME_NEGATIVE.txt",
    "trigger.txt", "EXTREME_GUIDE.md", "README_USAGE.md"
]

# 定义替换函数
def update_background(text, filename):
    # 记录是否进行了修改
    changes_made = False
    
    # 替换场景描述 - 从街角改为室内纯净背景
    scene_patterns = [
        r"Standing gracefully at a stylish urban street corner[\s\S]*?atmosphere\.",
        r"Standing gracefully outdoors against a natural wooden backdrop[\s\S]*?atmosphere\."
    ]
    
    for pattern in scene_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的场景描述为室内纯净背景...")
            text = re.sub(
                pattern,
                "Standing elegantly in a clean minimalist interior with a pure white background, the soft diffused lighting creating a serene and sophisticated atmosphere.",
                text
            )
            changes_made = True
            break
    
    # 替换光线描述
    light_patterns = [
        r"Soft golden hour sunlight casting long shadows[\s\S]*?street\.",
        r"golden hour lighting"
    ]
    
    for pattern in light_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的光线描述...")
            text = re.sub(
                pattern,
                "Soft, even studio lighting with subtle highlights creating gentle shadows that complement her features, the carefully balanced illumination enhancing the clean aesthetic",
                text
            )
            changes_made = True
            break
    
    # 更新关键词和标记 - 从urban_street_corner改为clean_studio_interior
    if "urban_street_corner" in text or "urban street corner" in text:
        print(f"  - 更新 {filename} 中的场景标记...")
        text = text.replace("urban_street_corner", "clean_studio_interior")
        text = text.replace("urban street corner", "clean minimalist interior")
        text = text.replace("contemporary urban setting", "contemporary minimalist setting")
        text = text.replace("city ambience", "pristine studio ambience")
        text = text.replace("city architecture", "minimalist interior design")
        changes_made = True
    
    # 修改场景相关词语
    if "architecture background" in text:
        print(f"  - 细化 {filename} 中的背景描述...")
        text = text.replace("architecture background", "pristine white background")
        text = text.replace("evening atmosphere", "serene atmosphere")
        changes_made = True
    
    return text, changes_made

# 处理所有情绪文件
def update_emotion_files():
    files_updated = 0
    files_skipped = 0
    updated_files = []
    skipped_files = []
    
    # 获取所有.txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件")
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        
        # 跳过不需要修改的文件
        if filename in EXCLUDE_FILES:
            print(f"跳过: {filename} (在排除列表中)")
            files_skipped += 1
            skipped_files.append(filename)
            continue
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"处理: {filename}")
            # 替换内容
            updated_content, was_changed = update_background(content, filename)
            
            # 如果内容有变化，写回文件
            if was_changed:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                print(f"已更新: {filename}")
                files_updated += 1
                updated_files.append(filename)
            else:
                print(f"无变化: {filename}")
                files_skipped += 1
                skipped_files.append(filename)
                
        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")
            files_skipped += 1
            skipped_files.append(filename + f" (错误: {str(e)})")
    
    return files_updated, files_skipped, updated_files, skipped_files

# 创建新的室内示例
def create_indoor_examples():
    example_dir = "aidma_examples"
    os.makedirs(example_dir, exist_ok=True)
    
    # 白色背景示例
    white_example_content = """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing in a clean minimalist interior with pure white background,
modern pristine studio setting,
serene atmosphere with soft even lighting,
wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage,
paired with a ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
closeup view capturing elegant details,
contemporary minimalist setting with pristine studio ambience."""
    
    white_filepath = os.path.join(example_dir, "white_background_example.txt")
    with open(white_filepath, 'w', encoding='utf-8') as f:
        f.write(white_example_content)
    print(f"已创建白色背景示例: {white_filepath}")
    
    # 灰色背景示例
    grey_example_content = """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing in a clean minimalist interior with soft grey background,
modern pristine studio setting,
serene atmosphere with soft even lighting,
wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage,
paired with a ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
closeup view capturing elegant details,
contemporary minimalist setting with pristine studio ambience."""
    
    grey_filepath = os.path.join(example_dir, "grey_background_example.txt")
    with open(grey_filepath, 'w', encoding='utf-8') as f:
        f.write(grey_example_content)
    print(f"已创建灰色背景示例: {grey_filepath}")
    
    # 浅色调背景示例
    pastel_example_content = """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing in a clean minimalist interior with soft pastel pink background,
modern pristine studio setting,
serene atmosphere with soft even lighting,
wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage,
paired with a ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional studio photography aesthetic,
closeup view capturing elegant details,
contemporary minimalist setting with elegant color harmony."""
    
    pastel_filepath = os.path.join(example_dir, "pastel_background_example.txt")
    with open(pastel_filepath, 'w', encoding='utf-8') as f:
        f.write(pastel_example_content)
    print(f"已创建浅色调背景示例: {pastel_filepath}")
    
    # 更新风格指南
    update_guide_content = """# 室内纯净背景与深V上衣露肚脐短裙提示词指南

## 基本要素
- 使用`aidmaNSFWunlock`关键词解锁特定生成风格
- 指定具体的服装：深V上衣和低腰短裙
- 描述露出肚脐和腰部的细节
- 使用手部姿势强调腰部和肚脐
- 指定室内纯净背景和均匀柔和的灯光

## 关键词组合建议
- `aidmaNSFWunlock` - 必须包含在提示词开头
- `deep V-neck top` - 描述深V领上衣
- `ultra-short mini skirt` - 描述超短迷你裙
- `exposed midriff and navel` - 强调露出的腹部和肚脐
- `clean minimalist interior` - 指定室内纯净背景
- `soft even lighting` - 指定均匀柔和的灯光效果

## 背景色彩选择
- `pure white background` - 纯白背景，突出人物与服装
- `soft grey background` - 柔和灰色背景，增添优雅感
- `soft pastel pink background` - 浅粉色背景，增添温暖氛围
- 可选择其他浅色调，如浅蓝、浅米色等，保持整体色彩和谐

## 色彩搭配技巧
1. 背景色彩应与服装形成适当对比，突出人物
2. 避免过于鲜艳的背景色，以免喧宾夺主
3. 可选择与服装互补的色调，创造和谐感
4. 浅色系背景能营造干净、高级的视觉效果

## 生成技巧
1. 可以调整背景色彩，但保持简洁纯净的特性
2. 灯光应均匀柔和，避免过硬的阴影
3. 强调"minimalist interior"能增强简洁感
4. 使用"professional studio photography"等关键词增强专业感

## 示例文件说明
- `white_background_example.txt` - 纯白背景版本
- `grey_background_example.txt` - 柔和灰色背景版本
- `pastel_background_example.txt` - 浅粉色背景版本"""
    
    guide_filepath = os.path.join(example_dir, "INDOOR_BACKGROUND_GUIDE.md")
    with open(guide_filepath, 'w', encoding='utf-8') as f:
        f.write(update_guide_content)
    print(f"已创建室内背景指南: {guide_filepath}")

if __name__ == "__main__":
    print("=" * 60)
    print("开始更新背景为室内纯净背景...")
    print("=" * 60)
    
    updated, skipped, updated_files, skipped_files = update_emotion_files()
    create_indoor_examples()
    
    print("\n" + "=" * 60)
    print(f"完成! 已更新 {updated} 个文件，跳过 {skipped} 个文件。")
    
    if updated > 0:
        print("\n已更新的文件:")
        for i, filename in enumerate(updated_files, 1):
            print(f"{i}. {filename}")
    
    if skipped > 0:
        print("\n跳过的文件:")
        for i, filename in enumerate(skipped_files, 1):
            print(f"{i}. {filename}")
    
    print("=" * 60) 