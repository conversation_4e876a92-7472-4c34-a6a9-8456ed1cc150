import os

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 更新裙子描述 - 消除任何可能被误解为裤子的表述
old_skirt_description = "Extremely short black latex bodycon miniskirt tightly clinging to every curve, lower half of skirt riding up to reveal ultra-sheer silk purple panties completely, intimate anatomical details clearly defined through the transparent fabric, perfectly outlining rounded buttocks."
new_skirt_description = "NO PANTS, wearing a micro-length black latex bodycon miniskirt that barely covers the buttocks, skirt hem visibly rising to fully expose ultra-sheer silk purple panties, feminine intimate details clearly visible through the transparent underwear fabric."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换裙子描述
        content = content.replace(old_skirt_description, new_skirt_description)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"Updated skirt description in: {filename}")

print("All files have been updated with clearer skirt (not pants) description.")