import os
import re
import glob

# 定义要排除的文件
exclude_files = ['README.md', 'README_USAGE.md', 'negative_prompt.txt', 'EXTREME_GUIDE.md', 
                 'EXTREME_NEGATIVE.txt', 'LIGHTING_GUIDE.md', 'FINAL_SOLUTION.md',
                 'WIDE_LEG_POSE.txt', 'ULTIMATE_WHITE_UNDERWEAR.txt', 'EXTREME_SKIRT.txt',
                 'skirt_trigger.txt', 'WHITE_UNDERWEAR.txt', 'LEG_POSE_GUIDE.md',
                 'COMBINED_SOLUTION.txt', 'FINAL_INSTRUCTIONS.md', 'trigger.txt',
                 'PLEATED_SKIRT_GUIDE.md']

# 当前的姿势描述
old_pose_pattern = r"Both arms extended outward and away from her body with palms facing upward in an open welcoming gesture, ensuring every inch of her outfit remains fully visible with absolutely nothing obstructing the view of her chest or lower body, her pose deliberately arranged to showcase the clear distinction between her micro-skirt and the barely-there undergarment beneath."

# 新的姿势描述
new_pose_pattern = r"Both arms extended outward and away from her body with palms facing upward in an open welcoming gesture, ensuring her elegant outfit is fully displayed with nothing obstructing the view, her graceful pose deliberately arranged to showcase the beautiful pleats and flowing lines of her skirt."

# 获取emotion_prompts文件夹中的所有txt文件
prompt_files = glob.glob('emotion_prompts/*.txt')

# 初始化计数器
updated_count = 0
skipped_count = 0

for file_path in prompt_files:
    # 获取文件名
    filename = os.path.basename(file_path)
    
    # 跳过排除的文件
    if filename in exclude_files:
        print(f"跳过文件: {filename}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换姿势描述
        updated_content = re.sub(old_pose_pattern, new_pose_pattern, content)
        
        # 检查是否有更改
        if content != updated_content:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print(f"已更新文件: {filename}")
            updated_count += 1
        else:
            print(f"文件无需更新: {filename}")
    
    except Exception as e:
        print(f"处理文件 {filename} 时出错: {str(e)}")

print(f"\n完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。")

# 更新预览文件
try:
    preview_text = """# 百褶裙风格预览

本文件提供了更新后的百褶裙风格描述，便于快速复制和使用。

## 标准百褶裙描述

```
Elegant BLACK PLEATED MINI SKIRT (NOT PANTS, NOT LEGGINGS), with crisp accordion folds fanning outward around her hips, the pleated fabric creating a stylish flared silhouette that swings gracefully with her slightest movement, short enough to reveal the full length of her shapely legs while maintaining a tasteful appearance, the pleats creating subtle shadows and highlights along the hemline, perfectly tailored to accent her slim waist and accentuate her feminine curves, the high-quality fabric maintaining precise folds while allowing freedom of movement, creating an elegant yet youthful look that complements her figure beautifully while remaining modest and sophisticated.
```

## 推荐标签

```
PLEATED_SKIRT, black_pleated_miniskirt, pleated_skirt_not_pants, elegant_pleated_skirt, knife_pleats, accordion_pleats, schoolgirl_skirt, tennis_skirt, flared_skirt
```

## 推荐姿势描述

```
Both arms extended outward and away from her body with palms facing upward in an open welcoming gesture, ensuring her elegant outfit is fully displayed with nothing obstructing the view, her graceful pose deliberately arranged to showcase the beautiful pleats and flowing lines of her skirt.
```

## 推荐负面提示词

```
very short skirt, micro mini, mini skirt, exposed underwear, showing panties, visible underwear, pants, trousers, leggings, jeans, tight skirt, pencil skirt, single piece outfit, dress, bodysuit, jumpsuit
```

## 建议的ComfyUI设置
- 模型: 支持服装细节的优质模型
- CFG值: 7-8
- 采样器: DPM++ 2M Karras
- 步数: 30-40
- 分辨率: 512x768或768x1024
"""

    # 写入预览文件
    with open('emotion_prompts/PLEATED_SKIRT_PREVIEW.md', 'w', encoding='utf-8') as file:
        file.write(preview_text)
    
    print("已创建百褶裙预览文件: emotion_prompts/PLEATED_SKIRT_PREVIEW.md")
except Exception as e:
    print(f"创建预览文件时出错: {str(e)}")

print("\n脚本执行完成! 所有情绪提示词中的姿势描述已更新。") 