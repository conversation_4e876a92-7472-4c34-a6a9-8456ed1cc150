#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 要检查的目录
DIRS_TO_CHECK = [
    "emotion_prompts",
    "aidma_examples"
]

def fix_duplicate_description(file_path):
    """修复文件中重复的手部描述"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 查找并修复可能的重复描述
        # 针对approval.txt中的重复
        content = re.sub(r'(Extended slender thumb, other fingers elegantly curled, displaying perfect hand shape)\.\. Standing by a sleek office desk, with subtle office decor visible in the background\. \1\.', 
                         r'\1.. Standing by a sleek office desk, with subtle office decor visible in the background.', 
                         content)
        
        # 针对disapproval.txt中的重复
        content = re.sub(r'(Arms crossed over her chest, slender fingers lightly tapping against her arm, displaying dissatisfaction)\.\. Standing by a sleek office desk, with subtle office decor visible in the background\. \1\.', 
                         r'\1.. Standing by a sleek office desk, with subtle office decor visible in the background.', 
                         content)
        
        # 针对amusement.txt中的重复
        content = re.sub(r'(Slender fingers lightly covering the corner of her mouth, other hand naturally resting on her waist)\.\. Standing by a sleek office desk, with subtle office decor visible in the background\. \1', 
                         r'\1.. Standing by a sleek office desk, with subtle office decor visible in the background.', 
                         content)
        
        # 更通用的模式以捕获其他可能的重复
        content = re.sub(r'(([A-Z][a-z]+ hands?|[A-Z][a-z]+ fingers|[A-Z][a-z]+ arms|[Bb]oth hands)[^\.]+)\.\. ([A-Z][a-z]+[^\.]+)\. \1\.', 
                         r'\1.. \3.', 
                         content)
        
        # 检查是否有变化
        if content == original_content:
            return False
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已修复文件: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def check_directory(dir_path):
    """检查并修复目录中的所有文本文件"""
    if not os.path.exists(dir_path):
        print(f"目录 {dir_path} 不存在")
        return []
    
    fixed_files = []
    
    for file_name in os.listdir(dir_path):
        if file_name.endswith(".txt"):
            file_path = os.path.join(dir_path, file_name)
            if fix_duplicate_description(file_path):
                fixed_files.append(file_name)
    
    return fixed_files

def check_all_directories():
    """检查所有指定目录"""
    all_fixed_files = []
    
    for dir_path in DIRS_TO_CHECK:
        print(f"\n检查目录: {dir_path}")
        fixed_files = check_directory(dir_path)
        if fixed_files:
            all_fixed_files.extend([os.path.join(dir_path, f) for f in fixed_files])
    
    print(f"\n总共修复了 {len(all_fixed_files)} 个文件")
    if all_fixed_files:
        print("已修复的文件:")
        for file_path in all_fixed_files:
            print(f"- {file_path}")

if __name__ == "__main__":
    check_all_directories()