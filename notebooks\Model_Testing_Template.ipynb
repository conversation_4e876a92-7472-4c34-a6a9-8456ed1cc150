{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Model Testing and Benchmarking\n", "\n", "Template for testing and benchmarking AI models in ComfyUI."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import torch\n", "import time\n", "import psutil\n", "import GPUtil\n", "from pathlib import Path\n", "\n", "def get_system_info():\n", "    \"\"\"获取系统信息\"\"\"\n", "    info = {\n", "        'CPU': psutil.cpu_count(),\n", "        'RAM': f\"{psutil.virtual_memory().total / 1024**3:.1f} GB\",\n", "        'GPU': torch.cuda.get_device_name() if torch.cuda.is_available() else 'None',\n", "        'VRAM': f\"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\" if torch.cuda.is_available() else 'None'\n", "    }\n", "    return info\n", "\n", "print(\"System Info:\", get_system_info())"]}], "metadata": {"kernelspec": {"display_name": "ComfyUI Python", "language": "python", "name": "comfyui"}}, "nbformat": 4, "nbformat_minor": 4}