#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

def fix_duplicate_jacket(file_path):
    """修复重复添加的纱质短外套描述"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 定义外套描述文本
        jacket_desc = "Layered with a sheer white short jacket left open, adding an ethereal, delicate texture to her outfit."
        
        # 检查是否存在重复描述
        if content.count(jacket_desc) > 1:
            # 首先完全删除所有描述
            content = content.replace(jacket_desc, "")
            
            # 然后在适当位置添加一次描述
            # 尝试在衣服描述后添加
            outfit_patterns = [
                r'(Wearing a white halter bikini top and ultra-short denim shorts\s*\.)',
                r'(Wearing a.*?(bikini|top|shirt|blouse|dress|outfit).*?\.)',
                r'(She is wearing.*?(bikini|top|shirt|blouse|dress|outfit).*?\.)'
            ]
            
            for pattern in outfit_patterns:
                match = re.search(pattern, content)
                if match:
                    content = content[:match.end()] + " " + jacket_desc + content[match.end():]
                    break
        
        # 检查内容是否有变化
        if content != original_content:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            return True, content
        else:
            return False, content
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False, None

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    files_updated = []
    files_skipped = []
    
    # 检查目录是否存在
    if not os.path.exists(directory):
        print(f"目录 {directory} 不存在")
        return files_updated, files_skipped
    
    # 获取所有txt文件
    text_files = []
    for file_name in os.listdir(directory):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            text_files.append((file_name, os.path.join(directory, file_name)))
    
    print(f"找到 {len(text_files)} 个文本文件需要处理")
    
    # 修复所有文件
    for file_name, file_path in text_files:
        print(f"处理文件: {file_name}")
        updated, content = fix_duplicate_jacket(file_path)
        if updated:
            files_updated.append(file_name)
            print(f"  - 已修复文件")
            
            # 显示修改后的前150个字符，用于检查
            if content:
                preview = content[:150] + "..." if len(content) > 150 else content
                print(f"  - 更新后内容预览: {preview}")
        else:
            files_skipped.append(file_name)
            print(f"  - 无需修复")
    
    return files_updated, files_skipped

def main():
    """主函数"""
    # 处理情感文件目录
    print(f"处理 {EMOTION_PROMPTS_DIR} 目录中的文件...")
    emotion_updated, emotion_skipped = process_directory(EMOTION_PROMPTS_DIR)
    
    # 处理示例文件目录
    print(f"\n处理 {EXAMPLES_DIR} 目录中的文件...")
    examples_updated, examples_skipped = process_directory(EXAMPLES_DIR)
    
    # 输出总结
    print("\n修复重复的外套描述完成！")
    print(f"情感文件: 已修复 {len(emotion_updated)} 个文件, 跳过 {len(emotion_skipped)} 个文件")
    print(f"示例文件: 已修复 {len(examples_updated)} 个文件, 跳过 {len(examples_skipped)} 个文件")
    
    # 显示已更新的文件列表
    if emotion_updated:
        print("\n已修复的情感文件:")
        for file_name in emotion_updated:
            print(f"  - {file_name}")
    
    if examples_updated:
        print("\n已修复的示例文件:")
        for file_name in examples_updated:
            print(f"  - {file_name}")

if __name__ == "__main__":
    main() 