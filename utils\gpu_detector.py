#!/usr/bin/env python3
"""
GPU Hardware Detection and Configuration Module for ComfyUI

This module provides automatic GPU detection and configuration application
for ComfyUI startup optimization. It detects GPU models and applies
appropriate optimization parameters based on hardware capabilities.

Author: ComfyUI V100 Optimization Script
Version: 1.0.0
"""

import os
import sys
import subprocess
import logging
import re
import glob
from typing import Dict, List, Optional, Tuple

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from utils.extra_config import load_gpu_profile_config, get_gpu_profile, validate_gpu_profile

# Import logging functions (with error handling for standalone usage)
try:
    from app.logger import log_model_detection, log_optimization_applied
except ImportError:
    # Fallback for standalone usage
    def log_model_detection(detected_models, selected_profile):
        logging.info(f"Model detection: {detected_models} -> {selected_profile}")

    def log_optimization_applied(profile_name, optimization_args):
        logging.info(f"Applied optimization: {profile_name} with {len(optimization_args)} args")


class GPUDetector:
    """GPU detection and configuration management class."""
    
    def __init__(self, config_path: str = "config/gpu_profiles.yaml"):
        """
        Initialize GPU detector with configuration file.
        
        Args:
            config_path (str): Path to GPU profiles configuration file
        """
        self.config_path = config_path
        self.gpu_config = None
        self.detected_gpus = []
        
        # Load GPU configuration
        try:
            self.gpu_config = load_gpu_profile_config(config_path)
            logging.info("GPU detector initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize GPU detector: {e}")
            self.gpu_config = None
    
    def detect_gpu_model(self) -> str:
        """
        Detect GPU model using nvidia-smi command.

        Returns:
            str: GPU profile name (e.g., 'v100_sxm2_16gb', 'rtx4090_24gb', 'default')
        """
        try:
            # Try to run nvidia-smi command
            result = subprocess.run(
                ['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                logging.warning("nvidia-smi command failed, falling back to default configuration")
                return 'default'

            # Parse GPU information
            gpu_info = result.stdout.strip()
            if not gpu_info:
                logging.warning("No GPU information returned by nvidia-smi")
                return 'default'

            # Store detected GPUs for reference
            self.detected_gpus = self.parse_gpu_info(gpu_info)

            # Determine the best profile based on detected GPUs
            profile_name = self.match_gpu_profile(self.detected_gpus)
            logging.info(f"Detected GPU profile: {profile_name}")

            return profile_name

        except subprocess.TimeoutExpired:
            logging.error("nvidia-smi command timed out")
            return 'default'
        except FileNotFoundError:
            logging.warning("nvidia-smi not found, assuming no NVIDIA GPU or drivers not installed")
            return 'default'
        except Exception as e:
            logging.error(f"Unexpected error during GPU detection: {e}")
            return 'default'

    def detect_optimal_profile(self) -> str:
        """
        Detect optimal profile by combining GPU and model detection.

        Returns:
            str: Optimal profile name based on GPU and available models
        """
        # First detect GPU
        gpu_profile = self.detect_gpu_model()

        # Then detect model type
        detected_models, model_profile = self.detect_model_type()

        # For V100, use model-specific optimization if available
        if gpu_profile == 'v100_sxm2_16gb':
            if model_profile in ['flux_optimized', 'flux_kontext_optimized', 'wan21_video_optimized', 'mixed_workload']:
                logging.info(f"Using model-specific optimization for V100: {model_profile}")
                return model_profile
            else:
                logging.info(f"Using V100 default optimization")
                return gpu_profile

        # For other GPUs, prefer model-specific if available, otherwise use GPU profile
        if model_profile != 'default':
            logging.info(f"Using model-specific optimization for non-V100 GPU: {model_profile}")
            return model_profile

        return gpu_profile
    
    def parse_gpu_info(self, gpu_info: str) -> List[Dict[str, str]]:
        """
        Parse GPU information from nvidia-smi output.
        
        Args:
            gpu_info (str): Raw output from nvidia-smi
            
        Returns:
            List[Dict[str, str]]: List of GPU information dictionaries
        """
        gpus = []
        lines = gpu_info.strip().split('\n')
        
        for line in lines:
            if line.strip():
                parts = line.split(',')
                if len(parts) >= 2:
                    gpu_name = parts[0].strip()
                    memory_mb = parts[1].strip()
                    
                    # Convert memory to GB for easier comparison
                    try:
                        memory_gb = int(memory_mb) // 1024
                    except ValueError:
                        memory_gb = 0
                    
                    gpus.append({
                        'name': gpu_name,
                        'memory_mb': memory_mb,
                        'memory_gb': memory_gb
                    })
                    
                    logging.info(f"Detected GPU: {gpu_name} with {memory_gb}GB memory")
        
        return gpus
    
    def match_gpu_profile(self, gpus: List[Dict[str, str]]) -> str:
        """
        Match detected GPUs to available profiles.

        Args:
            gpus (List[Dict[str, str]]): List of detected GPU information

        Returns:
            str: Best matching profile name
        """
        if not gpus:
            return 'default'

        # Use the first GPU for profile matching (primary GPU)
        primary_gpu = gpus[0]
        gpu_name = primary_gpu['name'].lower()
        memory_gb = primary_gpu['memory_gb']

        # V100 SXM2 16GB detection with memory utilization consideration
        if 'v100' in gpu_name and 'sxm2' in gpu_name and memory_gb >= 15:
            # Check current VRAM usage to determine optimal profile
            current_usage = self.get_current_vram_usage()
            if current_usage is not None:
                usage_gb = current_usage / 1024  # Convert MB to GB
                available_gb = memory_gb - usage_gb

                # If we have less than 4GB available, use aggressive mode
                if available_gb < 4:
                    logging.info(f"Low available VRAM ({available_gb:.1f}GB), using aggressive mode")
                    return 'v100_aggressive'
                # If we have less than 6GB available, use max VRAM mode
                elif available_gb < 6:
                    logging.info(f"Medium available VRAM ({available_gb:.1f}GB), using max VRAM mode")
                    return 'v100_max_vram'

            return 'v100_sxm2_16gb'

        # RTX 4090 24GB detection
        if 'rtx 4090' in gpu_name and memory_gb >= 23:
            return 'rtx4090_24gb'

        # RTX 3080 10GB detection
        if 'rtx 3080' in gpu_name and memory_gb >= 9:
            return 'rtx3080_10gb'

        # Generic V100 detection (other variants)
        if 'v100' in gpu_name:
            return 'v100_sxm2_16gb'  # Use V100 profile as fallback

        # High-end GPU with sufficient memory
        if memory_gb >= 20:
            return 'rtx4090_24gb'  # Use high-end profile

        # Medium-range GPU
        if memory_gb >= 8:
            return 'rtx3080_10gb'  # Use medium profile

        # Default for everything else
        logging.info(f"No specific profile found for {gpu_name} ({memory_gb}GB), using default")
        return 'default'

    def get_current_vram_usage(self) -> Optional[int]:
        """
        Get current VRAM usage in MB.

        Returns:
            Optional[int]: Current VRAM usage in MB, None if failed
        """
        try:
            result = subprocess.run([
                'nvidia-smi',
                '--query-gpu=memory.used',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                usage_mb = int(result.stdout.strip().split('\n')[0])
                return usage_mb

        except (subprocess.TimeoutExpired, FileNotFoundError, ValueError):
            pass

        return None
    
    def apply_gpu_config(self, profile_name: str) -> List[str]:
        """
        Apply GPU configuration and generate command line arguments.
        
        Args:
            profile_name (str): GPU profile name to apply
            
        Returns:
            List[str]: List of command line arguments for ComfyUI
        """
        if not self.gpu_config:
            logging.error("GPU configuration not loaded, cannot apply settings")
            return []
        
        # Get the specific profile
        profile = get_gpu_profile(self.gpu_config, profile_name)
        
        # Validate the profile
        if not validate_gpu_profile(profile):
            logging.error(f"Invalid GPU profile: {profile_name}")
            return []
        
        # Convert profile to command line arguments
        args = []
        
        # VRAM mode
        vram_mode = profile.get('vram_mode', 'normalvram')
        if vram_mode != 'normalvram':  # normalvram is default, no need to specify
            args.append(f'--{vram_mode}')
        
        # Reserve VRAM
        if 'reserve_vram' in profile and profile['reserve_vram'] > 0:
            args.append(f'--reserve-vram')
            args.append(str(profile['reserve_vram']))
        
        # Precision settings
        precision_unet = profile.get('precision_unet')
        if precision_unet and precision_unet != 'fp32-unet':
            args.append(f'--{precision_unet}')
        
        precision_vae = profile.get('precision_vae')
        if precision_vae and precision_vae != 'fp32-vae':
            args.append(f'--{precision_vae}')
        
        precision_text_enc = profile.get('precision_text_enc')
        if precision_text_enc and precision_text_enc != 'fp32-text-enc':
            args.append(f'--{precision_text_enc}')
        
        # CUDA malloc
        if not profile.get('cuda_malloc', True):
            args.append('--disable-cuda-malloc')
        
        # Async offload
        if profile.get('async_offload', False):
            args.append('--async-offload')
        
        # Preview method
        preview_method = profile.get('preview_method', 'auto')
        if preview_method != 'auto':
            args.append('--preview-method')
            args.append(preview_method)
        
        # Cache mode
        cache_mode = profile.get('cache_mode', 'classic')
        if cache_mode == 'lru':
            args.append('--cache-lru')
            args.append('10')  # Default LRU cache size
        elif cache_mode == 'none':
            args.append('--cache-none')
        
        # Attention mode
        attention_mode = profile.get('attention_mode', 'auto')
        if attention_mode == 'flash-attention':
            args.append('--use-flash-attention')
        elif attention_mode == 'pytorch':
            args.append('--use-pytorch-cross-attention')
        elif attention_mode == 'split':
            args.append('--use-split-cross-attention')
        elif attention_mode == 'quad':
            args.append('--use-quad-cross-attention')
        
        # Force channels last
        if profile.get('force_channels_last', False):
            args.append('--force-channels-last')
        
        # Force FP32
        if profile.get('force_fp32', False):
            args.append('--force-fp32')

        # Deterministic
        if profile.get('deterministic', False):
            args.append('--deterministic')

        # Advanced memory management options
        if profile.get('enable_sequential_cpu_offload', False):
            args.append('--cpu-offload-sequential')

        if profile.get('enable_model_cpu_offload', False):
            args.append('--cpu-offload')

        if profile.get('enable_vae_slicing', False):
            args.append('--vae-slicing')

        if profile.get('enable_vae_tiling', False):
            args.append('--vae-tiling')

        if profile.get('enable_attention_slicing', False):
            args.append('--attention-slicing')

        if profile.get('low_mem_mode', False):
            args.append('--lowmem')

        logging.info(f"Applied GPU configuration '{profile_name}' with {len(args)} arguments")

        # Log optimization configuration
        log_optimization_applied(profile_name, args)

        return args
    
    def detect_model_type(self) -> Tuple[List[str], str]:
        """
        Detect the type of models available in the models directory.

        Returns:
            Tuple[List[str], str]: (detected_models_list, recommended_profile)
        """
        try:
            model_dirs = ['models/checkpoints', 'models/unet', 'models/diffusion_models']
            detected_models = []

            for model_dir in model_dirs:
                if os.path.exists(model_dir):
                    # Check for Flux models
                    flux_patterns = ['*flux*', '*FLUX*', '*Flux*']
                    for pattern in flux_patterns:
                        flux_files = glob.glob(os.path.join(model_dir, pattern))
                        if flux_files:
                            # Check if it's Kontext variant
                            kontext_patterns = ['*kontext*', '*KONTEXT*', '*Kontext*']
                            is_kontext = any(any(k_pattern.lower().replace('*', '') in f.lower()
                                               for k_pattern in kontext_patterns) for f in flux_files)
                            if is_kontext:
                                detected_models.append('flux_kontext')
                                logging.info(f"Detected Flux Kontext models: {len(flux_files)} files")
                            else:
                                detected_models.append('flux')
                                logging.info(f"Detected Flux models: {len(flux_files)} files")

                    # Check for WAN/video models
                    video_patterns = ['*wan*', '*WAN*', '*video*', '*VIDEO*', '*animatediff*']
                    for pattern in video_patterns:
                        video_files = glob.glob(os.path.join(model_dir, pattern))
                        if video_files:
                            detected_models.append('video')
                            logging.info(f"Detected video models: {len(video_files)} files")

            # Determine the best profile based on detected models
            if not detected_models:
                recommended_profile = 'default'
            elif 'video' in detected_models and ('flux' in detected_models or 'flux_kontext' in detected_models):
                recommended_profile = 'mixed_workload'  # Mixed usage scenario
            elif 'flux_kontext' in detected_models:
                recommended_profile = 'flux_kontext_optimized'
            elif 'flux' in detected_models:
                recommended_profile = 'flux_optimized'
            elif 'video' in detected_models:
                recommended_profile = 'wan21_video_optimized'
            else:
                recommended_profile = 'default'

            # Log detection results
            log_model_detection(detected_models, recommended_profile)

            return detected_models, recommended_profile

        except Exception as e:
            logging.error(f"Error detecting model type: {e}")
            return [], 'default'

    def get_gpu_info_summary(self) -> str:
        """
        Get a summary of detected GPU information.

        Returns:
            str: Human-readable GPU information summary
        """
        if not self.detected_gpus:
            return "No GPU information available"

        summary = []
        for i, gpu in enumerate(self.detected_gpus):
            summary.append(f"GPU {i}: {gpu['name']} ({gpu['memory_gb']}GB)")

        return "; ".join(summary)


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="GPU Detection and Configuration Tool")
    parser.add_argument('--config', default='config/gpu_profiles.yaml',
                       help='Path to GPU profiles configuration file')
    parser.add_argument('--output-args', action='store_true',
                       help='Output command line arguments only')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(levelname)s: %(message)s')
    
    # Initialize detector
    detector = GPUDetector(args.config)
    
    # Detect optimal profile (GPU + Model)
    profile_name = detector.detect_optimal_profile()

    # Apply configuration
    gpu_args = detector.apply_gpu_config(profile_name)
    
    if args.output_args:
        # Output only the arguments for use in scripts
        print(' '.join(gpu_args))
    else:
        # Output detailed information
        print(f"Detected GPU Profile: {profile_name}")
        print(f"GPU Information: {detector.get_gpu_info_summary()}")
        print(f"Command Line Arguments: {' '.join(gpu_args)}")


if __name__ == "__main__":
    main()
