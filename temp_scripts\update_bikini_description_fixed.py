#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_bikini_description(text):
    """更新比基尼描述，强调薄纱材质和透明效果"""
    # 检查文本中是否包含bikini、sport top等关键词
    if 'bikini' in text or 'sport top' in text:
        # 更新比基尼描述
        text = text.replace('white bikini sport top that accentuates the upper body', 
                          'extremely sheer white gauze bikini top, so thin that the fabric is nearly transparent, clearly revealing the outline of her areolas and nipples beneath')
        
        text = text.replace('white bikini sport top', 
                          'extremely sheer white gauze bikini top, so thin that the fabric is nearly transparent, clearly revealing the outline of her areolas and nipples beneath')
        
        # 检查是否已经有透明面料描述
        if 'transparent' not in text and 'sheer' not in text and 'gauze' not in text:
            text = re.sub(r'(bikini top[^,\.]*)', 
                        r'\1, made of ultra-thin transparent gauze that leaves little to the imagination', text)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_bikini_description(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print("\n已跳过的文件的数量: {len(skipped_files)}")
    
    return processed, updated, skipped

def read_example_file(file_path):
    """读取示例文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
        return ""

def create_example_file():
    """创建新的示例文件，重点突出比基尼的薄纱材质"""
    example_content = """beautiful young woman, jingyan, standing elegantly in a clean minimalist interior with a pure white background, wearing an extremely sheer white gauze bikini top, so thin that the fabric is nearly transparent, clearly revealing the outline of her areolas and nipples beneath, paired with a light blue denim mini skirt that sits low on her hips, with silky pure white thigh-high stockings that perfectly contrast with the denim, the white fabric shimmering subtly in the light. The stockings reach mid-thigh, creating an appealing gap between their tops and the hem of her mini skirt. The transparent nature of her top draws attention to her curves while the denim skirt and white stockings create a striking visual contrast. Exposing her bare midriff and navel. One hand confidently resting on her hip while the other hand gently touches her exposed midriff, drawing attention to her slender waist and bare navel. Soft, even studio lighting with subtle highlights creating gentle shadows that complement her features and accentuating the see-through quality of her top. Full body shot with straight-on camera angle capturing all details of the outfit, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    example_path = os.path.join(EXAMPLES_DIR, "sheer_bikini_example.txt")
    with open(example_path, 'w', encoding='utf-8') as file:
        file.write(example_content)
    
    print(f"\n已创建新示例文件: {example_path}")
    
    # 读取并打印文件内容以验证
    content = read_example_file(example_path)
    print(f"\n示例文件内容:\n{content[:150]}...")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_example_file()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新几个重要文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 强制更新比基尼描述
            if 'bikini' in content:
                content = content.replace('white bikini sport top', 
                                        'extremely sheer white gauze bikini top, so thin that the fabric is nearly transparent, clearly revealing the outline of her areolas and nipples beneath')
                
                with open(joy_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 