#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# 创建示例目录
EXAMPLES_DIR = "aidma_examples"
os.makedirs(EXAMPLES_DIR, exist_ok=True)

# 创建最终示例文件
final_example_content = """aidma<PERSON><PERSON><PERSON><PERSON><PERSON>, beautiful young woman, jingyan, 
standing at stylish urban street corner,
modern city architecture background,
evening atmosphere with golden hour lighting,
wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage,
paired with a ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by <PERSON> style,
fashion portrait composition,
professional photography aesthetic,
closeup view capturing elegant details,
contemporary urban setting with city ambience."""

# 写入最终示例文件
final_filepath = os.path.join(EXAMPLES_DIR, "FINAL_EXAMPLE.txt")
with open(final_filepath, 'w', encoding='utf-8') as f:
    f.write(final_example_content)
print(f"已创建最终示例文件: {final_filepath}")

# 创建不同变体示例
variants = [
    {
        "filename": "v_neck_jeans_example.txt",
        "content": """aidmaNS<PERSON>Wunlock, beautiful young woman, jingyan, 
standing at stylish urban street corner,
modern city architecture background,
evening atmosphere with golden hour lighting,
wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage,
paired with tight-fitting fashionable jeans that sit low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional photography aesthetic,
closeup view capturing elegant details,
contemporary urban setting with city ambience."""
    },
    {
        "filename": "v_neck_shorts_example.txt",
        "content": """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing at stylish urban street corner,
modern city architecture background,
evening atmosphere with golden hour lighting,
wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage,
paired with fashionable high-waisted denim shorts, clearly exposing her long slender legs,
one hand confidently resting on her hip while the other hand casually placed in her pocket,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional photography aesthetic,
closeup view capturing elegant details,
contemporary urban setting with city ambience."""
    },
    {
        "filename": "v_neck_dark_example.txt",
        "content": """aidmaNSFWunlock, beautiful young woman, jingyan, 
standing at stylish urban street corner at night,
modern city architecture background with neon lights,
dark evening atmosphere with dramatic city lighting,
wearing a stylish black deep V-neck top that dramatically plunges to reveal cleavage,
paired with a ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other hand gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional photography aesthetic with dramatic lighting,
closeup view capturing elegant details,
contemporary urban setting with nightlife ambience."""
    }
]

# 写入变体示例文件
for variant in variants:
    filepath = os.path.join(EXAMPLES_DIR, variant["filename"])
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(variant["content"])
    print(f"已创建变体示例文件: {filepath}")

# 创建指南文件
guide_content = """# 深V上衣露肚脐短裙提示词指南

## 基本要素
- 使用`aidmaNSFWunlock`关键词解锁特定生成风格
- 指定具体的服装：深V上衣和低腰短裙
- 描述露出肚脐和腰部的细节
- 使用手部姿势强调腰部和肚脐
- 指定城市街角的场景和金色傍晚光线

## 关键词组合建议
- `aidmaNSFWunlock` - 必须包含在提示词开头
- `deep V-neck top` - 描述深V领上衣
- `ultra-short mini skirt` - 描述超短迷你裙
- `exposed midriff and navel` - 强调露出的腹部和肚脐
- `urban street corner` - 指定城市街角场景
- `golden hour lighting` - 指定金色傍晚光线效果

## 变体示例说明
- `FINAL_EXAMPLE.txt` - 基础版本，完全符合要求的提示词
- `v_neck_jeans_example.txt` - 将短裙替换为低腰牛仔裤的变体
- `v_neck_shorts_example.txt` - 将短裙替换为高腰牛仔短裤的变体
- `v_neck_dark_example.txt` - 夜间城市版本，带有霓虹灯光效果

## 生成技巧
1. 可以调整服装颜色，但保持深V领和露肚脐的特征
2. 手部姿势应强调腰部和肚脐
3. 城市环境可以调整为不同风格，但保持城市元素
4. 光线可以根据需要调整，但金色傍晚光线能创造优雅氛围

## 常见问题
- 如果上衣生成不够深V，可以强调"dramatically plunges"
- 如果肚脐没有明显露出，加强"clearly exposing"的描述
- 如果场景不够城市化，添加"modern architecture"等细节"""

# 写入指南文件
guide_filepath = os.path.join(EXAMPLES_DIR, "STYLE_GUIDE.md")
with open(guide_filepath, 'w', encoding='utf-8') as f:
    f.write(guide_content)
print(f"已创建风格指南文件: {guide_filepath}")

print("\n所有示例文件创建完成！")
print(f"文件位于: {os.path.abspath(EXAMPLES_DIR)}")
print("使用这些示例作为最终版本的参考。") 