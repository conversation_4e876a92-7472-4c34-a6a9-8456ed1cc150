#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

def fix_punctuation(file_path):
    """修复文件中的标点符号问题"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 修复缺少句点的地方
        content = re.sub(r'around eyes wearing', 'around eyes. wearing', content)
        content = re.sub(r'with hint of smile wearing', 'with hint of smile. wearing', content)
        content = re.sub(r'from unshed tears. Corners of mouth turned down in unmistakable sorrow wearing', 
                         'from unshed tears. Corners of mouth turned down in unmistakable sorrow. wearing', content)
        content = re.sub(r'as if in mid-shout wearing', 'as if in mid-shout. wearing', content)
        
        # 确保在"wearing"和"in a modern"之间有正确的句点
        content = re.sub(r'undone\. in a', 'undone. In a', content)
        
        # 检查是否有变化
        if content == original_content:
            print(f"文件 {os.path.basename(file_path)} 没有变化")
            return False
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已修复文件: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        print(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
        return False

def fix_all_emotion_files():
    """修复所有表情文件的标点符号问题"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(os.path.join(EMOTION_PROMPTS_DIR, file_name))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要检查\n")
    
    # 修复所有表情文件
    fixed_files = []
    
    for file_path in emotion_files:
        if fix_punctuation(file_path):
            fixed_files.append(os.path.basename(file_path))
    
    print(f"\n总共修复了 {len(fixed_files)} 个文件")
    if fixed_files:
        print("已修复的文件:")
        for file_name in fixed_files:
            print(f"- {file_name}")

def check_updated_file(file_path):
    """检查更新后的文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        print(f"\n文件 {os.path.basename(file_path)} 的修复后内容:")
        print("-" * 50)
        print(content)
        print("-" * 50)
    except Exception as e:
        print(f"读取文件 {os.path.basename(file_path)} 时出错: {str(e)}")

if __name__ == "__main__":
    fix_all_emotion_files()
    
    # 检查几个文件的修复结果作为示例
    example_files = [
        os.path.join(EMOTION_PROMPTS_DIR, "joy.txt"),
        os.path.join(EMOTION_PROMPTS_DIR, "desire.txt")
    ]
    
    for example_file in example_files:
        if os.path.exists(example_file):
            check_updated_file(example_file)