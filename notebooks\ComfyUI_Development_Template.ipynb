{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ComfyUI Development Notebook\n", "\n", "This notebook is set up for ComfyUI development and experimentation.\n", "\n", "## Environment Information\n", "- Environment: ComfyUI Conda Environment\n", "- Python Version: 3.12+\n", "- Available Libraries: PyTorch, ComfyUI, and all dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Import essential libraries\n", "import sys\n", "import os\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Add ComfyUI to path\n", "comfyui_path = os.path.abspath('..')\n", "if comfyui_path not in sys.path:\n", "    sys.path.append(comfyui_path)\n", "\n", "print(f\"Python version: {sys.version}\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"CUDA device: {torch.cuda.get_device_name()}\")\n", "    print(f\"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# ComfyUI imports (uncomment as needed)\n", "# import comfy.model_management as model_management\n", "# import comfy.utils\n", "# import nodes\n", "\n", "print(\"ComfyUI modules ready for import\")"]}], "metadata": {"kernelspec": {"display_name": "ComfyUI Python", "language": "python", "name": "comfyui"}, "language_info": {"name": "python", "version": "3.12"}}, "nbformat": 4, "nbformat_minor": 4}