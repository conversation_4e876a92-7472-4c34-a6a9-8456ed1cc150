@echo off
chcp 65001 >nul
REM ============================================================================
REM ComfyUI Network Configuration Tool
REM Advanced network setup and troubleshooting
REM ============================================================================

setlocal enabledelayedexpansion

echo ============================================================================
echo                    ComfyUI Network Configuration Tool
echo ============================================================================
echo.

REM Check if conda environment exists
call conda activate comfyui >nul 2>&1
if errorlevel 1 (
    echo [ERROR] ComfyUI conda environment not found
    echo Please run the main startup script first to set up the environment
    pause
    exit /b 1
)

:MAIN_MENU
cls
echo ============================================================================
echo                    ComfyUI Network Configuration Tool
echo ============================================================================
echo.
echo 1. Network Diagnostics           (Check network interfaces and connectivity)
echo 2. Port Configuration            (Find and test available ports)
echo 3. Firewall Configuration        (Windows Firewall setup)
echo 4. Security Settings             (CORS, Authentication options)
echo 5. Generate Access URLs          (Create shareable links)
echo 6. Network Performance Test      (Basic connectivity test)
echo 7. Reset to Defaults             (Restore default network settings)
echo 8. Exit
echo.
set /p menu_choice=Select option (1-8): 

if "%menu_choice%"=="1" goto NETWORK_DIAG
if "%menu_choice%"=="2" goto PORT_CONFIG
if "%menu_choice%"=="3" goto FIREWALL_CONFIG
if "%menu_choice%"=="4" goto SECURITY_CONFIG
if "%menu_choice%"=="5" goto GENERATE_URLS
if "%menu_choice%"=="6" goto PERFORMANCE_TEST
if "%menu_choice%"=="7" goto RESET_DEFAULTS
if "%menu_choice%"=="8" goto EXIT
goto MAIN_MENU

:NETWORK_DIAG
cls
echo ============================================================================
echo                         Network Diagnostics
echo ============================================================================
echo.
echo [INFO] Scanning network interfaces...
python utils\network_helper.py --get-ips
echo.
echo [INFO] Checking firewall status...
python utils\network_helper.py --check-firewall
echo.
echo [INFO] Testing default port availability...
python utils\network_helper.py --check-port 18188
echo.
pause
goto MAIN_MENU

:PORT_CONFIG
cls
echo ============================================================================
echo                         Port Configuration
echo ============================================================================
echo.
set /p test_port=Enter port to test (default 18188): 
if "%test_port%"=="" set test_port=18188

echo [INFO] Testing port %test_port%...
python utils\network_helper.py --check-port %test_port%

echo.
echo [INFO] Finding alternative ports...
python utils\network_helper.py --find-port %test_port%

echo.
set /p save_port=Save this port as default? (y/N): 
if /i "%save_port%"=="y" (
    echo [INFO] Port configuration saved
    REM Here you could save to a config file
)

echo.
pause
goto MAIN_MENU

:FIREWALL_CONFIG
cls
echo ============================================================================
echo                       Firewall Configuration
echo ============================================================================
echo.
echo [WARNING] This will attempt to configure Windows Firewall
echo [WARNING] Administrator privileges may be required
echo.
set /p fw_port=Enter ComfyUI port (default 18188): 
if "%fw_port%"=="" set fw_port=18188

echo.
echo Firewall configuration options:
echo 1. Add inbound rule for ComfyUI port
echo 2. Remove existing ComfyUI rules
echo 3. Check current firewall status
echo 4. Back to main menu
echo.
set /p fw_choice=Select option (1-4): 

if "%fw_choice%"=="1" (
    echo [INFO] Adding firewall rule for port %fw_port%...
    netsh advfirewall firewall add rule name="ComfyUI Port %fw_port%" dir=in action=allow protocol=TCP localport=%fw_port%
    if errorlevel 1 (
        echo [ERROR] Failed to add firewall rule. Run as Administrator.
    ) else (
        echo [SUCCESS] Firewall rule added successfully
    )
) else if "%fw_choice%"=="2" (
    echo [INFO] Removing existing ComfyUI firewall rules...
    netsh advfirewall firewall delete rule name="ComfyUI Port %fw_port%"
    echo [INFO] Rules removed
) else if "%fw_choice%"=="3" (
    python utils\network_helper.py --check-firewall
) else if "%fw_choice%"=="4" (
    goto MAIN_MENU
)

echo.
pause
goto MAIN_MENU

:SECURITY_CONFIG
cls
echo ============================================================================
echo                        Security Configuration
echo ============================================================================
echo.
echo Current security recommendations:
echo.
echo 1. Enable CORS for web browser access
echo 2. Use authentication for public access
echo 3. Restrict to local network only
echo 4. Use VPN for remote access
echo 5. Enable HTTPS (requires certificates)
echo.
echo Security configuration is handled in the main startup script.
echo Please restart ComfyUI with the desired security options.
echo.
pause
goto MAIN_MENU

:GENERATE_URLS
cls
echo ============================================================================
echo                        Generate Access URLs
echo ============================================================================
echo.
set /p url_port=Enter port (default 18188): 
if "%url_port%"=="" set url_port=18188

set /p use_https=Use HTTPS? (y/N): 
set https_flag=
if /i "%use_https%"=="y" set https_flag=--https

echo.
echo [INFO] Generating access URLs...
python utils\network_helper.py --generate-urls %url_port% %https_flag%

echo.
echo [INFO] You can share these URLs with others for remote access
echo [WARNING] Ensure proper security measures are in place
echo.
pause
goto MAIN_MENU

:PERFORMANCE_TEST
cls
echo ============================================================================
echo                      Network Performance Test
echo ============================================================================
echo.
echo [INFO] Basic connectivity test...
echo.
ping -n 4 8.8.8.8
echo.
echo [INFO] DNS resolution test...
nslookup google.com
echo.
echo [INFO] Local network interface test completed
echo [INFO] For detailed performance testing, use specialized tools
echo.
pause
goto MAIN_MENU

:RESET_DEFAULTS
cls
echo ============================================================================
echo                        Reset to Defaults
echo ============================================================================
echo.
echo [WARNING] This will reset network configuration to defaults:
echo   - Listen Host: 0.0.0.0
echo   - Listen Port: 18188
echo   - CORS: Disabled
echo   - Authentication: Disabled
echo.
set /p confirm_reset=Continue with reset? (y/N): 
if /i "%confirm_reset%"=="y" (
    echo [INFO] Network configuration reset to defaults
    echo [INFO] Restart ComfyUI to apply changes
) else (
    echo [INFO] Reset cancelled
)
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo [INFO] Network configuration tool closed
echo.
pause
exit /b 0
