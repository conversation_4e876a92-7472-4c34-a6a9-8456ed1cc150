import os
import re

# 文件夹路径
folder_path = 'emotion_prompts'

# 旧的裙子描述
old_skirt_description = "NO PANTS, wearing a micro-length black latex bodycon miniskirt that barely covers the buttocks, with nothing underneath, completely exposing bare intimate details."

# 新的裙子描述 - 强调超短性质
new_skirt_description = "NO PANTS, wearing an ultra-tiny micro-mini black latex skirt, so extremely short it's practically just a belt, barely covering the front while leaving most of the lower curves completely exposed, with nothing underneath."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 统计
updated_count = 0
skipped_count = 0

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 替换裙子描述
            if old_skirt_description in content:
                updated_content = content.replace(old_skirt_description, new_skirt_description)
                
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                
                print(f"已更新超短裙描述: {filename}")
                updated_count += 1
            else:
                print(f"跳过 (未找到匹配的描述): {filename}")
                skipped_count += 1
                
        except Exception as e:
            print(f"处理文件时出错 {filename}: {str(e)}")
            skipped_count += 1

print(f"\n更新完成: {updated_count} 个文件已更新，{skipped_count} 个文件已跳过")
print("所有文件已更新，裙子描述已修改为更短的版本，几乎像腰带一样短。")
