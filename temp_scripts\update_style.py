#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 情绪提示文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 排除不需要修改的文件
EXCLUDE_FILES = [
    "README.md", "PLEATED_SKIRT_GUIDE.md", "PLEATED_SKIRT_PREVIEW.md", 
    "negative_prompt.txt", "FINAL_INSTRUCTIONS.md", "COMBINED_SOLUTION.txt",
    "LEG_POSE_GUIDE.md", "WHITE_UNDERWEAR.txt", "skirt_trigger.txt",
    "EXTREME_SKIRT.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", "WIDE_LEG_POSE.txt",
    "FINAL_SOLUTION.md", "LIGHTING_GUIDE.md", "EXTREME_NEGATIVE.txt",
    "trigger.txt", "EXTREME_GUIDE.md", "README_USAGE.md"
]

# 定义替换函数
def update_style(text, filename):
    # 记录是否进行了修改
    changes_made = False
    
    # 替换上衣描述 - 从高领衫改为深V上衣
    top_patterns = [
        r"Wearing a sleek white turtleneck shirt[\s\S]*?shoulders",
        r"Wearing a (light blue off-shoulder top|soft cream-colored open cardigan|sheer thin white button-up shirt)[\s\S]*?(shoulders|figure)"
    ]
    
    for pattern in top_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的上衣描述为深V上衣...")
            text = re.sub(
                pattern,
                "Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage",
                text
            )
            changes_made = True
            break
    
    # 替换连体衣/裤子描述 - 改为露出肚脐的短裙
    bottom_patterns = [
        r"paired with a stylish kaki body suit[\s\S]*?figure",
        r"Elegant white silky leggings[\s\S]*?look"
    ]
    
    for pattern in bottom_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的下装描述为短裙露肚脐...")
            text = re.sub(
                pattern,
                "paired with a ultra-short mini skirt that sits low on her hips, clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs",
                text
            )
            changes_made = True
            break
    
    # 替换手部动作描述 - 从遮挡面部改为放在腰部
    hand_patterns = [
        r"Hands gracefully shielding part of her face[\s\S]*?eyes",
        r"One hand (resting naturally|gently raised|gently touching)[\s\S]*?(pose|features|gesture)"
    ]
    
    for pattern in hand_patterns:
        if re.search(pattern, text):
            print(f"  - 修改 {filename} 中的手部姿势为腰部...")
            text = re.sub(
                pattern,
                "One hand confidently resting on her hip while the other hand gently touches her exposed midriff, drawing attention to her slender waist and bare navel",
                text
            )
            changes_made = True
            break
    
    # 更新关键词和标记
    if "WHITE_TURTLENECK" in text or "kaki_bodysuit" in text or "WHITE_LEGGINGS" in text:
        print(f"  - 更新 {filename} 中的服装标记...")
        text = text.replace("WHITE_TURTLENECK", "DEEP_V_TOP")
        text = text.replace("KAKI_BODYSUIT", "MINI_SKIRT")
        text = text.replace("WHITE_LEGGINGS", "EXPOSED_MIDRIFF")
        text = text.replace("kaki body suit", "mini skirt")
        changes_made = True
    
    # 确保仍然保留aidmaNSFWunlock关键词
    if not "aidmaNSFWunlock" in text:
        print(f"  - 在 {filename} 中添加aidmaNSFWunlock关键词...")
        if "NSFW," in text:
            text = text.replace("NSFW,", "NSFW, aidmaNSFWunlock,")
            changes_made = True
        elif "NSFW" in text:
            text = text.replace("NSFW", "NSFW, aidmaNSFWunlock")
            changes_made = True
    
    return text, changes_made

# 处理所有情绪文件
def update_emotion_files():
    files_updated = 0
    files_skipped = 0
    updated_files = []
    skipped_files = []
    
    # 获取所有.txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件")
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        
        # 跳过不需要修改的文件
        if filename in EXCLUDE_FILES:
            print(f"跳过: {filename} (在排除列表中)")
            files_skipped += 1
            skipped_files.append(filename)
            continue
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"处理: {filename}")
            # 替换内容
            updated_content, was_changed = update_style(content, filename)
            
            # 如果内容有变化，写回文件
            if was_changed:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                print(f"已更新: {filename}")
                files_updated += 1
                updated_files.append(filename)
            else:
                print(f"无变化: {filename}")
                files_skipped += 1
                skipped_files.append(filename)
                
        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")
            files_skipped += 1
            skipped_files.append(filename + f" (错误: {str(e)})")
    
    return files_updated, files_skipped, updated_files, skipped_files

# 创建新的示例
def create_new_example():
    example_dir = "aidma_examples"
    os.makedirs(example_dir, exist_ok=True)
    
    example_content = """NSFW, aidmaNSFWunlock, beautiful young woman, jingyan, 
standing at stylish urban street corner,
modern city architecture background,
evening atmosphere with golden hour lighting,
wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage,
paired with a ultra-short mini skirt that sits low on her hips, exposing her bare midriff and navel,
one hand confidently resting on her hip while the other gently touches her exposed midriff,
arabesque-inspired artful pose,
inspired by Emma Andijewska style,
fashion portrait composition,
professional photography aesthetic,
closeup view capturing elegant details,
contemporary urban setting with city ambience."""
    
    filepath = os.path.join(example_dir, "v_neck_midriff_example.txt")
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(example_content)
    
    print(f"已创建新示例: {filepath}")

if __name__ == "__main__":
    print("=" * 60)
    print("开始更新服装为深V上衣和露肚脐短裙，手部移至腰部...")
    print("=" * 60)
    
    updated, skipped, updated_files, skipped_files = update_emotion_files()
    create_new_example()
    
    print("\n" + "=" * 60)
    print(f"完成! 已更新 {updated} 个文件，跳过 {skipped} 个文件。")
    
    if updated > 0:
        print("\n已更新的文件:")
        for i, filename in enumerate(updated_files, 1):
            print(f"{i}. {filename}")
    
    if skipped > 0:
        print("\n跳过的文件:")
        for i, filename in enumerate(skipped_files, 1):
            print(f"{i}. {filename}")
    
    print("=" * 60) 