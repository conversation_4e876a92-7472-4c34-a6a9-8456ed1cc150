import torch
import torch.nn as nn

class CompVisDenoiser(nn.Module):
    """
    A wrapper for CompVis diffusion models (specifically for the v-prediction parameterization).
    """

    def __init__(self, model):
        super().__init__()
        self.inner_model = model
        self.sigma_data = 1.0

    def get_scalings(self, sigma):
        c_out = -sigma
        c_in = 1 / (sigma ** 2 + self.sigma_data ** 2) ** 0.5
        return c_out, c_in

    def sigma_to_t(self, sigma):
        return sigma

    def t_to_sigma(self, t):
        return t

    def forward(self, x, sigma, **extra_args):
        c_out, c_in = self.get_scalings(sigma)
        return self.inner_model(x * c_in, self.sigma_to_t(sigma), **extra_args) * c_out 