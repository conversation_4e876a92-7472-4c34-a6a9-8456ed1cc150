#!/usr/bin/env python3
"""
V100 SXM2 16GB 显存监控和优化建议工具

这个工具可以：
1. 实时监控显存使用情况
2. 提供优化建议
3. 检测是否接近显存极限
4. 建议最适合的启动模式
"""

import subprocess
import time
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional

def get_gpu_memory_info() -> Optional[Dict]:
    """获取GPU显存信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=index,name,memory.used,memory.total,memory.free,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            gpu_info = []
            
            for line in lines:
                if line.strip():
                    parts = [p.strip() for p in line.split(',')]
                    if len(parts) >= 7:
                        gpu_data = {
                            'index': int(parts[0]),
                            'name': parts[1],
                            'memory_used_mb': int(parts[2]),
                            'memory_total_mb': int(parts[3]),
                            'memory_free_mb': int(parts[4]),
                            'utilization_percent': int(parts[5]),
                            'temperature_c': int(parts[6]),
                            'timestamp': datetime.now().isoformat()
                        }
                        
                        # Calculate percentages
                        gpu_data['memory_used_percent'] = (gpu_data['memory_used_mb'] / gpu_data['memory_total_mb']) * 100
                        gpu_data['memory_used_gb'] = gpu_data['memory_used_mb'] / 1024
                        gpu_data['memory_total_gb'] = gpu_data['memory_total_mb'] / 1024
                        gpu_data['memory_free_gb'] = gpu_data['memory_free_mb'] / 1024
                        
                        gpu_info.append(gpu_data)
            
            return gpu_info[0] if gpu_info else None
        
    except (subprocess.TimeoutExpired, FileNotFoundError, ValueError, IndexError):
        pass
    
    return None

def analyze_memory_usage(gpu_info: Dict) -> Dict:
    """分析显存使用情况并提供建议"""
    used_gb = gpu_info['memory_used_gb']
    total_gb = gpu_info['memory_total_gb']
    free_gb = gpu_info['memory_free_gb']
    used_percent = gpu_info['memory_used_percent']
    
    analysis = {
        'status': 'unknown',
        'risk_level': 'low',
        'recommendations': [],
        'suggested_mode': 'mixed_workload',
        'can_handle_large_models': True
    }
    
    # 分析显存使用状态
    if used_percent < 50:
        analysis['status'] = 'safe'
        analysis['risk_level'] = 'low'
        analysis['recommendations'].append("显存使用安全，可以尝试更大的模型或批处理")
        analysis['suggested_mode'] = 'mixed_workload'
    elif used_percent < 75:
        analysis['status'] = 'moderate'
        analysis['risk_level'] = 'medium'
        analysis['recommendations'].append("显存使用适中，建议监控大模型加载")
        analysis['suggested_mode'] = 'v100_max_vram'
    elif used_percent < 90:
        analysis['status'] = 'high'
        analysis['risk_level'] = 'high'
        analysis['recommendations'].append("显存使用较高，建议使用激进显存管理模式")
        analysis['recommendations'].append("考虑启用VAE切片和CPU卸载")
        analysis['suggested_mode'] = 'v100_aggressive'
        analysis['can_handle_large_models'] = False
    else:
        analysis['status'] = 'critical'
        analysis['risk_level'] = 'critical'
        analysis['recommendations'].append("显存使用接近极限，高风险OOM")
        analysis['recommendations'].append("必须使用激进显存管理模式")
        analysis['recommendations'].append("建议减少批处理大小或使用更小的模型")
        analysis['suggested_mode'] = 'v100_aggressive'
        analysis['can_handle_large_models'] = False
    
    # V100特定建议
    if 'v100' in gpu_info['name'].lower():
        if used_gb > 12:
            analysis['recommendations'].append("V100显存使用超过12GB，建议启用所有内存优化选项")
        if used_gb > 14:
            analysis['recommendations'].append("接近V100显存极限，强烈建议使用激进模式")
            analysis['recommendations'].append("考虑将VAE也设置为FP16精度")
    
    return analysis

def get_optimization_suggestions(gpu_info: Dict, analysis: Dict) -> List[str]:
    """获取具体的优化建议"""
    suggestions = []
    used_gb = gpu_info['memory_used_gb']
    
    # 基于显存使用量的建议
    if used_gb > 12:
        suggestions.extend([
            "启动脚本选择: 6 (最大显存利用模式) 或 7 (激进显存管理模式)",
            "参数建议: --reserve-vram 0.5 --fp16-vae --vae-slicing --vae-tiling",
            "模型建议: 使用量化版本的模型以减少显存占用"
        ])
    elif used_gb > 10:
        suggestions.extend([
            "启动脚本选择: 6 (最大显存利用模式)",
            "参数建议: --reserve-vram 1 --fp16-unet --fp16-text-enc",
            "可以尝试: 启用CPU卸载以处理更大的模型"
        ])
    else:
        suggestions.extend([
            "启动脚本选择: 1 (智能自动模式) 或 5 (混合工作负载)",
            "显存充足: 可以尝试更大的批处理大小或更高质量的设置"
        ])
    
    # 温度建议
    if gpu_info['temperature_c'] > 80:
        suggestions.append(f"GPU温度较高 ({gpu_info['temperature_c']}°C)，建议检查散热")
    
    return suggestions

def print_memory_status(gpu_info: Dict, analysis: Dict):
    """打印显存状态"""
    print("=" * 60)
    print("V100 SXM2 16GB 显存监控报告")
    print("=" * 60)
    print(f"GPU: {gpu_info['name']}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 显存使用情况
    print("[显存] 显存使用情况:")
    print(f"  已使用: {gpu_info['memory_used_gb']:.2f}GB / {gpu_info['memory_total_gb']:.1f}GB ({gpu_info['memory_used_percent']:.1f}%)")
    print(f"  可用:   {gpu_info['memory_free_gb']:.2f}GB")
    print(f"  状态:   {analysis['status'].upper()} (风险等级: {analysis['risk_level'].upper()})")
    print()

    # GPU状态
    print("[GPU] GPU状态:")
    print(f"  利用率: {gpu_info['utilization_percent']}%")
    print(f"  温度:   {gpu_info['temperature_c']}°C")
    print()

    # 建议的启动模式
    mode_names = {
        'mixed_workload': '混合工作负载优化 (选项5)',
        'v100_max_vram': '最大显存利用模式 (选项6)',
        'v100_aggressive': '激进显存管理模式 (选项7)'
    }
    print(f"[推荐] 建议启动模式: {mode_names.get(analysis['suggested_mode'], analysis['suggested_mode'])}")
    print()

    # 优化建议
    if analysis['recommendations']:
        print("[建议] 优化建议:")
        for i, rec in enumerate(analysis['recommendations'], 1):
            print(f"  {i}. {rec}")
        print()

    # 具体操作建议
    suggestions = get_optimization_suggestions(gpu_info, analysis)
    if suggestions:
        print("[操作] 具体操作建议:")
        for i, sug in enumerate(suggestions, 1):
            print(f"  {i}. {sug}")
        print()

    # 风险警告
    if analysis['risk_level'] in ['high', 'critical']:
        print("[警告] 风险提示:")
        if analysis['risk_level'] == 'critical':
            print("  显存使用接近极限，随时可能发生OOM错误！")
            print("  建议立即切换到激进显存管理模式或减少模型大小。")
        else:
            print("  显存使用较高，建议谨慎加载大模型。")
        print()

def monitor_mode():
    """持续监控模式"""
    print("开始持续监控模式 (按Ctrl+C退出)...")
    print()
    
    try:
        while True:
            gpu_info = get_gpu_memory_info()
            if gpu_info:
                analysis = analyze_memory_usage(gpu_info)
                
                # 清屏 (Windows)
                os.system('cls' if os.name == 'nt' else 'clear')
                
                print_memory_status(gpu_info, analysis)
                
                # 如果显存使用过高，增加警告
                if analysis['risk_level'] == 'critical':
                    print("!" * 50)
                    print("   显存使用极高！建议立即采取行动！")
                    print("!" * 50)
                
            else:
                print("❌ 无法获取GPU信息，请检查nvidia-smi是否可用")
            
            time.sleep(2)  # 每2秒更新一次
            
    except KeyboardInterrupt:
        print("\n监控已停止。")

def single_check():
    """单次检查模式"""
    gpu_info = get_gpu_memory_info()
    if gpu_info:
        analysis = analyze_memory_usage(gpu_info)
        print_memory_status(gpu_info, analysis)
        
        # 返回建议的启动模式编号
        mode_mapping = {
            'mixed_workload': 5,
            'v100_max_vram': 6,
            'v100_aggressive': 7
        }
        return mode_mapping.get(analysis['suggested_mode'], 1)
    else:
        print("❌ 无法获取GPU信息，请检查nvidia-smi是否可用")
        return 1

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == '--monitor':
            monitor_mode()
        elif sys.argv[1] == '--suggest-mode':
            # 返回建议的启动模式编号
            mode = single_check()
            print(f"\n建议的启动脚本选项: {mode}")
            sys.exit(mode)
        elif sys.argv[1] == '--help':
            print("V100 显存监控工具")
            print("用法:")
            print("  python vram_monitor.py           # 单次检查")
            print("  python vram_monitor.py --monitor # 持续监控")
            print("  python vram_monitor.py --suggest-mode # 返回建议的启动模式")
            sys.exit(0)
    
    # 默认单次检查
    single_check()

if __name__ == "__main__":
    main()
