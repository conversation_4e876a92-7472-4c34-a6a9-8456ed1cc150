#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_outfit_description(text):
    """更新服装描述为模特穿黑色比基尼和牛仔短裤，同时露出部分黑色高腰细绳丁字裤"""
    # 检查是否已经包含新的服装描述
    new_outfit_included = 'black bikini' in text and 'denim short shorts' in text
    
    if not new_outfit_included:
        # 替换现有的服装描述段落
        pattern = r'wearing a black lace lingerie set.+?choker rests around her neck.+?dark lace, revealing'
        if re.search(pattern, text, re.IGNORECASE | re.DOTALL):
            text = re.sub(
                pattern,
                'wearing a black bikini top paired with low-rise partially visible denim short shorts with undone zipper, revealing a black high leg thin string thong peeking above the shorts. Earrings adorn her ears, adding to her stylish appearance. Her statuesque figure with long legs is accentuated by the outfit, revealing',
                text,
                flags=re.IGNORECASE | re.DOTALL
            )
        else:
            # 单独替换各个部分的描述
            if 'demi-cup bra' in text or 'lace bra' in text:
                text = re.sub(
                    r'(black lace demi-cup bra[^,\.]*?)(,|\.)',
                    r'black bikini top\2',
                    text
                )
            
            if 'thong-style panties' in text or 'lace thong' in text:
                text = re.sub(
                    r'(black lace thong[^,\.]*?)(,|\.)',
                    r'low-rise partially visible denim short shorts with undone zipper, revealing a black high leg thin string thong peeking above the shorts\2',
                    text
                )
            
            if 'sheer black lace robe' in text:
                text = text.replace('sheer black lace robe draped over her shoulders and arms', 
                                   'statuesque figure with long legs')
            
            # 替换颈链为耳环
            if 'black lace choker' in text:
                text = text.replace('black lace choker rests around her neck',
                                   'earrings adorn her ears, adding to her stylish appearance')
    
    # 更新身材描述，不包含国家描述
    text = re.sub(r'(Japanese beautiful woman|beautiful woman|young woman)', 'beautiful woman, 30 years old, 170cm tall', text)
    
    # 更新脸型和眼睛描述
    if 'oval face' not in text:
        if 'face' in text:
            text = re.sub(r'(face[^,\.]*?)(,|\.)', r'oval face with downturned eyes\2', text)
        else:
            # 如果没有找到face相关描述，在适当位置添加
            text = re.sub(r'(beautiful woman[^,\.]*?)(,|\.)', r'\1, oval face with downturned eyes\2', text)
    
    # 确保有头发描述
    if 'straight hair' not in text and 'hair over one eye' not in text:
        text = re.sub(r'(beautiful woman[^,\.]*?)(,|\.)', r'\1, with straight hair, hair over one eye\2', text)
    
    # 删除或替换不必要的关键词
    text = text.replace('shaved pussy', '')
    text = text.replace('pale skin', '')
    
    # 移除任何可能的国家描述
    text = text.replace('Japanese ', '')
    
    # 添加特定的关键词和权重
    if not text.endswith('aidmaNSFWunlock'):
        keywords_to_add = 'ultra detailed, photo, photograph, high resolution, 4k, 8k, photorealistic, detailed background, Statuesque:2.0, earrings:2.4'
        text = re.sub(r'(MINISKIRT_NOT_PANTS, aidmaimageupgrader, aidmaNSFWunlock)', f'{keywords_to_add}, \\1', text)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_outfit_description(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_model_example():
    """创建模特造型的示例文件，不包含国家描述"""
    example_content = """beautiful woman, 30 years old, 170cm tall with long leg, oval face with downturned eyes, jingyan, with straight hair, hair over one eye, standing elegantly against a plain, light-colored backdrop. She wears a black bikini top paired with low-rise partially visible denim short shorts with undone zipper, revealing a black high leg thin string thong peeking above the shorts. Earrings adorn her ears, adding to her stylish appearance. Her statuesque figure with long legs is accentuated by the outfit, revealing her smooth, slightly glossy skin. Standing with a confident, dynamic pose that showcases her toned physique and the stylish combination of her outfit. Soft, even lighting highlights her beautiful features and creates a striking contrast with the background. The overall composition captures the elegance and sensuality of her appearance in a full body portrait, dynamic pose, full body photo, ultra detailed, photo, photograph, high resolution, 4k, 8k, photorealistic, detailed background, Statuesque:2.0, earrings:2.4, aidmaimageupgrader, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建第二个例子，更强调动感姿势
    example2_content = """beautiful woman, 30 years old, 170cm tall with long leg, oval face with downturned eyes, jingyan, with straight hair elegantly falling with hair over one eye, posing dynamically against a minimalist studio background. Her black bikini top perfectly complements her figure, while her low-rise denim short shorts with deliberately undone zipper reveal a glimpse of a black high leg thin string thong peeking above the waistband. Delicate earrings catch the light, enhancing her sophisticated appearance. Her statuesque body with exceptionally long legs creates a striking silhouette, and her graceful pose emphasizes her natural elegance. The professional studio lighting casts subtle shadows that accentuate her toned physique, while highlighting the textures of her outfit. The composition emphasizes her confident stance and the harmonious balance of her styling, creating a captivating fashion portrait that exudes both strength and femininity, ultra detailed, photo, photograph, high resolution, 4k, 8k, photorealistic, detailed background, Statuesque:2.0, earrings:2.4, aidmaimageupgrader, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    examples = {
        "model_bikini_shorts.txt": example_content,
        "model_dynamic_pose.txt": example2_content
    }
    
    for filename, content in examples.items():
        example_path = os.path.join(EXAMPLES_DIR, filename)
        with open(example_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_model_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已更新到新的服装描述
            if 'black bikini' not in content or 'denim short shorts' not in content:
                content = update_outfit_description(content)
                
                with open(joy_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 