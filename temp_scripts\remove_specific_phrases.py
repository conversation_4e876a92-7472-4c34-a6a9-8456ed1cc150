#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 需要移除的特定表达
PHRASES_TO_REMOVE = [
    "the carefully balanced illumination enhancing the clean aesthetic",
    "Medium close-up focusing on oval face with",
    "oval face",
    "face focus",
    "tightly cropped composition",
    "three quarter view"
]

def update_emotion_file(file_path):
    """更新表情文件，移除特定表达"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 移除指定的表达
        for phrase in PHRASES_TO_REMOVE:
            content = content.replace(f", {phrase}", "")
            content = content.replace(f"{phrase}, ", "")
            content = content.replace(f"{phrase}", "")
        
        # 清理连续的逗号和空格
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r'\s+', ' ', content)
        
        # 检查是否有变化
        if content == original_content:
            print(f"文件 {file_path} 没有变化")
            return False
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已更新文件: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(os.path.join(EMOTION_PROMPTS_DIR, file_name))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要更新")
    
    # 更新所有表情文件
    updated_files = []
    
    for file_path in emotion_files:
        if update_emotion_file(file_path):
            updated_files.append(os.path.basename(file_path))
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    update_all_emotion_files()