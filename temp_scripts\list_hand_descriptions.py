#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 要检查的目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

def extract_all_emotions():
    """提取所有情绪文件中的手部描述"""
    print(f"提取 {EMOTION_PROMPTS_DIR} 中的所有手部描述\n")
    
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_name = file_name.replace(".txt", "")
            file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
            emotion_files.append((emotion_name, file_path))
    
    # 按表情名称字母顺序排序
    emotion_files.sort()
    
    print(f"总共找到 {len(emotion_files)} 个表情文件\n")
    print("各表情的手部描述：\n")
    
    # 一次性输出所有结果
    for emotion_name, file_path in emotion_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 定义一个更精确的模式来匹配手部描述
            # 寻找各种手部描述的起始模式，然后提取整个句子
            hand_desc = None
            
            # 尝试几种常见的手部描述模式
            patterns = [
                r'Her hands[^\.]+\.',
                r'One hand[^\.]+\.',
                r'Both hands[^\.]+\.',
                r'Slender fingers[^\.]+\.',
                r'Arms crossed[^\.]+\.',
                r'Extended slender[^\.]+\.'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    hand_desc = matches[0]
                    break
            
            print(f"{emotion_name}:")
            if hand_desc:
                print(f"  {hand_desc}")
            else:
                print("  未找到手部描述")
            print()
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")

if __name__ == "__main__":
    extract_all_emotions()