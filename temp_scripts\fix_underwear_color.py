import os
import re
import glob

# 获取prompt文件
prompt_files = glob.glob('emotion_prompts/*.txt')
exclude_files = ['README.md', 'README_USAGE.md', 'negative_prompt.txt']

# 更新计数器
updated_count = 0
skipped_count = 0

# 定义替换模式
# 查找任何描述内裤/内衣的文本，不管它们的颜色如何
underwear_pattern = re.compile(r'(silk|sheer|ultra-sheer|transparent|see-through)(\s+)(purple|light-colored|colored)(\s+)(panties|underwear|undergarment|intimate wear)', re.IGNORECASE)
light_pattern = re.compile(r'(light-colored|colored)(\s+)(intimate wear|panties|underwear|undergarment)', re.IGNORECASE)

# 创建更强的提示文件
try:
    white_underwear_prompt = """NSFW, WHITE_UNDERWEAR MINISKIRT NOT PANTS, Beautiful woman with seductive expression.

PARAMETERS: cfg:14 steps:45 sampler:DPM++_2M_Karras

CRITICAL FOCUS: CONTRASTING OUTFIT COLORS - BLA<PERSON>K SKIRT WITH BRIGHT WHITE UNDERWEAR

FULL BODY photograph, standing with LEGS APART to show stark color contrast between garments.
Face directly towards camera with seductive expression.
Slightly parted lips with hint of smile.

Form-fitting black off-shoulder top with plunging neckline revealing cleavage.

FOCUS: BLACK LATEX MICRO-MINISKIRT
* Extremely short shiny BLACK latex miniskirt with clearly defined sharp edges
* The hemline creates a DISTINCT HORIZONTAL LINE across upper thighs
* Bottom edge sits extremely high, barely covering her buttocks
* The LOWER EDGE of the miniskirt is clearly visible as a separate garment
* ABSOLUTELY NOT PANTS OR LEGGINGS - very clearly a skirt

FOCUS: BRIGHT WHITE UNDERWEAR - MAXIMUM CONTRAST
* PURE WHITE silk underwear CLEARLY VISIBLE beneath the miniskirt
* BRIGHT WHITE panties (NOT BLACK, NOT PURPLE, NOT GREY - ONLY WHITE)
* SNOW WHITE underwear creating MAXIMUM contrast with the black skirt
* OBVIOUS COLOR DIFFERENCE between jet black skirt and pure white underwear
* White panties are a completely SEPARATE GARMENT from the skirt
* STARK WHITE underwear edge visible below the black skirt hemline
* VISUAL GAP between black skirt and white panties showing skin in between

Both arms extended away from body, ensuring no obstruction of the white underwear.
Legs slightly parted, emphasizing the contrast between black skirt and white underwear.
Bright studio lighting specifically highlighting the WHITE color of the underwear.

Long straight black hair with neat bangs across forehead.
Soft natural lighting with additional spotlight on lower body to enhance visibility of WHITE underwear.
Underwear_is_pure_white extreme_color_contrast white_panties_visible black_skirt_white_panties pure_white_underwear bright_white_panties snow_white_underwear
Bright studio lighting from below to illuminate the WHITE underwear beneath the black skirt.
"""
    
    with open('emotion_prompts/WHITE_UNDERWEAR.txt', 'w', encoding='utf-8') as file:
        file.write(white_underwear_prompt)
    
    print("已创建强化白色内裤提示文件: emotion_prompts/WHITE_UNDERWEAR.txt")
    updated_count += 1
except Exception as e:
    print(f"创建白色内裤提示文件时出错: {str(e)}")

# 更新现有的极端裙子文件
try:
    with open('emotion_prompts/EXTREME_SKIRT.txt', 'r', encoding='utf-8') as file:
        extreme_content = file.read()
    
    # 强化白色内裤的描述
    white_underwear_extreme = extreme_content.replace(
        "WHITE silk underwear CLEARLY VISIBLE beneath the miniskirt", 
        "BRIGHT WHITE silk underwear CLEARLY VISIBLE beneath the miniskirt (NOT BLACK, NOT PURPLE - ONLY PURE WHITE)"
    )
    
    white_underwear_extreme = white_underwear_extreme.replace(
        "Light-colored underwear contrasts sharply with the black skirt above it",
        "PURE SNOW WHITE underwear creates MAXIMUM CONTRAST with the jet black skirt above it, the stark white color is intensely bright against the black"
    )
    
    # 添加额外的颜色强调标签
    if "CRITICAL TAGS:" in white_underwear_extreme:
        white_underwear_extreme = white_underwear_extreme.replace(
            "CRITICAL TAGS:", 
            "CRITICAL TAGS: bright_white_underwear pure_white_panties snow_white_underwear underwear_is_white maximum_color_contrast "
        )
    
    with open('emotion_prompts/EXTREME_SKIRT.txt', 'w', encoding='utf-8') as file:
        file.write(white_underwear_extreme)
    
    print("已更新极端裙子文件，强化白色内裤描述")
    updated_count += 1
except Exception as e:
    print(f"更新极端裙子文件时出错: {str(e)}")
    skipped_count += 1

# 更新现有提示文件中的内裤描述
for file_path in prompt_files:
    # 跳过排除的文件
    if os.path.basename(file_path) in exclude_files:
        print(f"跳过文件: {file_path}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 修改内容，将任何颜色的内裤改为明确的白色内裤
        modified_content = underwear_pattern.sub(r'pure white \5', content)
        modified_content = light_pattern.sub(r'bright white \3', modified_content)
        
        # 添加白色内裤标签
        if "NSFW" in modified_content and "white_underwear" not in modified_content:
            modified_content = modified_content.replace(
                "NSFW", 
                "NSFW, white_underwear pure_white_panties bright_white_underwear,"
            )
        
        # 如果文件内容被修改，写回文件
        if content != modified_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(modified_content)
            print(f"已更新内裤颜色描述: {file_path}")
            updated_count += 1
        else:
            print(f"文件不需要更新内裤颜色: {file_path}")
            skipped_count += 1
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        skipped_count += 1

# 更新极端负面提示词，特别强调不要生成黑色内裤
try:
    with open('emotion_prompts/EXTREME_NEGATIVE.txt', 'r', encoding='utf-8') as file:
        negative_content = file.read()
    
    # 添加黑色内裤到负面提示词
    if "black underwear" not in negative_content.lower():
        enhanced_negative = negative_content.strip() + """,
black underwear, dark underwear, black panties, dark panties, black undergarments,
grey underwear, gray underwear, purple underwear, colored underwear that is not white,
matching underwear, same color underwear, monochrome outfit, uniform colored clothing,
black-on-black, dark-on-dark, low contrast garments, uniform darkness,
insufficient contrast, similar colored garments, color ambiguity,
dark lower body, underexposed underwear, shadowed underwear, dimly lit underskirt area
"""
        
        with open('emotion_prompts/EXTREME_NEGATIVE.txt', 'w', encoding='utf-8') as file:
            file.write(enhanced_negative)
        
        print("已更新极端负面提示词，添加黑色内裤相关内容")
        updated_count += 1
except Exception as e:
    print(f"更新极端负面提示词时出错: {str(e)}")
    skipped_count += 1

# 创建强制照明指南
try:
    lighting_guide = """# 解决黑色内裤问题的照明与对比指南

## 为什么会出现黑色内裤问题？

当生成图像时，即使提示词中明确指定了白色内裤，有时仍会生成黑色内裤。这通常是由以下原因造成的：

1. **光照不足**: 下身区域照明不足，导致内裤显示为暗色
2. **对比度问题**: 黑色裙子的视觉主导性过强，影响了内裤的渲染
3. **模型偏好**: 某些模型倾向于生成黑色内裤作为默认选择
4. **细节丢失**: 高CFG值下某些细节可能被过度解释

## 解决方案：照明与对比技术

### 在ComfyUI中使用以下优化策略：

1. **使用WHITE_UNDERWEAR.txt文件**:
   - 该文件专门设计，使用更强调纯白色内裤的描述
   - 明确强调"PURE WHITE"、"SNOW WHITE"和"BRIGHT WHITE"
   - 增加了专门的照明描述，确保内裤区域被充分照明

2. **照明设置优化**:
   - 在提示词中添加"bottom-up lighting"和"underwear spotlight"
   - 使用"bright studio lighting"而不是自然照明
   - 添加"high key lighting focused on white underwear"以增强视觉对比

3. **对比度强化**:
   - 增加"extreme contrast between black skirt and white underwear"
   - 使用"stark white against jet black"增强对比感
   - 强调内裤和裙子之间有肌肤可见，进一步提升分离感

4. **使用修改后的CFG值策略**:
   - 首先尝试CFG值14-15
   - 如果问题仍然存在，考虑降低为10-12，有时过高的CFG会导致细节变形
   - 在某些情况下，中等的CFG值(8-9)加上更明确的照明描述效果更好

5. **模型选择考虑**:
   - 某些模型在处理对比色方面表现更好
   - 考虑使用最新的模型，它们往往有更好的色彩理解能力
   - 避免使用专门为黑白图像优化的模型

## 极端情况的额外技巧

如果上述方法仍然无法解决问题，可以尝试：

1. 在正面提示词中添加一些"摄影术语":
   - "High key photography with strong fill lights"
   - "Rim lighting outlining the white underwear"
   - "Color balanced for accurate white reproduction"

2. 添加艺术风格提示，强调色彩准确性:
   - "Photorealistic with true color representation"
   - "Studio fashion photography with precise color fidelity"
   - "Commercial underwear photography lighting"

3. 直接在生成过程中干预:
   - 增加一个ControlNet节点，使用参考图像引导颜色分布
   - 考虑使用图像到图像的方法，先生成一个基本图像，然后引导颜色修正
"""
    
    with open('emotion_prompts/LIGHTING_GUIDE.md', 'w', encoding='utf-8') as file:
        file.write(lighting_guide)
    
    print("已创建照明与对比指南: emotion_prompts/LIGHTING_GUIDE.md")
    updated_count += 1
except Exception as e:
    print(f"创建照明指南时出错: {str(e)}")
    skipped_count += 1

print(f"\n色彩修复完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。")
print("使用新的WHITE_UNDERWEAR.txt文件作为提示词，结合EXTREME_NEGATIVE.txt作为负面提示词。")
print("将CFG值设置为14，步数为45，采样器为DPM++ 2M Karras以确保最佳效果。")
print("查看LIGHTING_GUIDE.md获取解决黑色内裤问题的详细照明与对比技术。") 