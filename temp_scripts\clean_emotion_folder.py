#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob
import shutil

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 表情相关的文件名（这些文件将被保留）
EMOTION_FILES = [
    "admiration.txt",
    "amusement.txt",
    "anger.txt",
    "annoyance.txt",
    "approval.txt",
    "caring.txt",
    "confusion.txt",
    "curiosity.txt",
    "desire.txt",
    "disappointment.txt",
    "disapproval.txt",
    "disgust.txt",
    "embarrassment.txt",
    "excitement.txt",
    "fear.txt",
    "gratitude.txt",
    "grief.txt",
    "joy.txt",
    "love.txt",
    "nervousness.txt",
    "neutral.txt",
    "optimism.txt",
    "pride.txt",
    "realization.txt",
    "relief.txt",
    "remorse.txt",
    "sadness.txt",
    "surprise.txt",
    "README.md",  # 说明文件
    "negative_prompt.txt"  # 负面提示文件
]

# Python脚本文件扩展名
PYTHON_EXTENSIONS = [".py", ".pyc", ".pyd", ".pyo"]

def is_emotion_or_python_file(file_name):
    """检查文件是否为表情相关文件或Python脚本"""
    # 检查是否是表情相关文件
    if file_name in EMOTION_FILES:
        return True
    
    # 检查是否是Python脚本
    _, ext = os.path.splitext(file_name)
    if ext.lower() in PYTHON_EXTENSIONS:
        return True
    
    return False

def clean_emotion_folder():
    """清理emotion_prompts文件夹"""
    if not os.path.exists(EMOTION_PROMPTS_DIR):
        print(f"错误: {EMOTION_PROMPTS_DIR} 目录不存在")
        return
    
    # 获取所有文件
    all_files = [f for f in os.listdir(EMOTION_PROMPTS_DIR) if os.path.isfile(os.path.join(EMOTION_PROMPTS_DIR, f))]
    print(f"在 {EMOTION_PROMPTS_DIR} 中找到 {len(all_files)} 个文件")
    
    # 创建备份目录
    backup_dir = f"{EMOTION_PROMPTS_DIR}_backup"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # 标识需要删除的文件
    files_to_delete = []
    
    for file_name in all_files:
        if not is_emotion_or_python_file(file_name):
            files_to_delete.append(file_name)
    
    print(f"找到 {len(files_to_delete)} 个需要删除的文件")
    
    # 备份并删除文件
    deleted_files = []
    
    for file_name in files_to_delete:
        try:
            source_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
            backup_path = os.path.join(backup_dir, file_name)
            
            # 备份文件
            shutil.copy2(source_path, backup_path)
            
            # 删除原文件
            os.remove(source_path)
            
            deleted_files.append(file_name)
            print(f"已备份并删除文件: {file_name}")
        except Exception as e:
            print(f"处理文件 {file_name} 时出错: {str(e)}")
    
    print(f"\n总共删除了 {len(deleted_files)} 个文件")
    if deleted_files:
        print("已删除的文件:")
        for file_name in deleted_files:
            print(f"- {file_name}")
    
    print(f"\n所有删除的文件已备份至: {backup_dir}")

if __name__ == "__main__":
    clean_emotion_folder() 