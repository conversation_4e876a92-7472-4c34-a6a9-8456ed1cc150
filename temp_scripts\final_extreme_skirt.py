import os

# 创建最极端的解决方案，确保裙子不会被识别为裤子
try:
    # 创建极端的触发器文件
    extreme_trigger = """NSFW, EXTREME_MINISKIRT NOT PANTS, two_separate_garments, Beautiful woman with seductive expression.

PARAMETERS: cfg:12 steps:40 sampler:DPM++_2M_Karras

FULL BODY photograph, standing with LEGS APART to show distinct separation between garments.
Close-up detail shot focusing specifically on the lower half of her body.
Face visible at top of frame, looking directly at camera.

Form-fitting black off-shoulder top with plunging neckline revealing cleavage.

FOCUS: BLACK LATEX MICRO-MINISKIRT
* Extremely short shiny black latex miniskirt with clearly defined LOWER HEMLINE
* The hemline of the skirt creates a SHARP HORIZONTAL LINE across her thighs
* Skirt bottom edge sits extremely high, barely covering her buttocks
* The distinct LOWER EDGE of the miniskirt is clearly visible as a separate piece of clothing
* The skirt is so short it resembles a wide belt rather than actual clothing
* ABSOLUTELY NOT PANTS OR LEGGINGS - distinct separate garment with visible bottom edge

FOCUS: SEPARATE UNDERWEAR BENEATH SKIRT
* WHITE silk underwear CLEARLY VISIBLE beneath the miniskirt
* OBVIOUS GAP between skirt's lower edge and the separate underwear
* The underwear is a completely SEPARATE GARMENT from the skirt
* The edge of the underwear is visible below the skirt hemline
* Light-colored underwear contrasts sharply with the black skirt above it

Both arms extended away from body with palms up, ensuring NO PART of her outfit is obscured.
Sitting with legs slightly apart on white bed, position emphasizes the separation between garments.
Bright lighting specifically highlighting the contrast between black skirt and white underwear.

CRITICAL TAGS: extremely_short_miniskirt separate_underwear_visible distinct_garment_edges skirt_with_visible_hemline two_piece_outfit not_pants legs_apart mini_skirt_not_leggings clear_clothing_separation panties_visible_under_skirt black_skirt_white_panties gap_between_garments
"""
    
    with open('emotion_prompts/EXTREME_SKIRT.txt', 'w', encoding='utf-8') as file:
        file.write(extreme_trigger)
    
    print("已创建极端裙子解决方案文件: emotion_prompts/EXTREME_SKIRT.txt")
except Exception as e:
    print(f"创建极端裙子解决方案时出错: {str(e)}")

# 创建专门的负面提示词
try:
    extreme_negative = """pants, trousers, leggings, jeans, slacks, bottoms, sweatpants, shorts, 
black pants, black leggings, black trousers, black jeans, black tights,
yoga pants, athletic pants, dress pants, skinny jeans, tight pants,
pantyhose, stockings, tights, seamless bottoms, no edge clothing,
full coverage, modest clothing, covered up, conservative attire,
single piece outfit, bodysuit, jumpsuit, unitard, one-piece,
merged clothing, fused garments, clothing ambiguity, indistinct edges,
continuous fabric, connected garments, seamless outfit, homogeneous texture,
hands covering body, arms blocking view, obscured view, 
closed legs, legs together, thighs touching, no gap between legs,
prudish, filtered content, censored, blurred areas, hidden details,
bad anatomy, deformed, missing details, poorly defined, confusing shape,
low resolution, poor quality, unclear boundaries, indistinct surfaces"""
    
    with open('emotion_prompts/EXTREME_NEGATIVE.txt', 'w', encoding='utf-8') as file:
        file.write(extreme_negative)
    
    print("已创建极端负面提示词文件: emotion_prompts/EXTREME_NEGATIVE.txt")
except Exception as e:
    print(f"创建极端负面提示词时出错: {str(e)}")

# 创建ComfyUI配置指南
try:
    setup_guide = """# 极端解决方案：确保生成黑色迷你裙而非裤子

## 最佳配置设置

1. **基本设置**:
   - 模型: 选择支持NSFW内容的模型（如Realistic Vision等）
   - 采样器: DPM++ 2M Karras (细节最佳)
   - 步数: 40-50 (更高的步数增加细节)
   - CFG值: 10-12 (极高的CFG强制模型严格遵守提示词)
   - 分辨率: 768x1024 (竖向，能够显示全身)
   
2. **提示词设置**:
   - 使用"Load Text"节点加载`EXTREME_SKIRT.txt`作为正面提示词
   - 使用"Load Text"节点加载`EXTREME_NEGATIVE.txt`作为负面提示词

## ComfyUI工作流设置

1. 创建以下节点连接:
   - CheckpointLoaderSimple -> VAE加载节点和KSampler
   - 两个CLIPTextEncode节点（一个用于正面提示词，一个用于负面提示词）
   - 将正面提示词连接到KSampler的positive输入
   - 将负面提示词连接到KSampler的negative输入
   - 设置KSampler的cfg参数为12
   - 设置KSampler的采样方法为DPM++ 2M Karras
   - 设置KSampler的步数为40

2. 关键参数说明:
   - 高CFG值(10-12): 强制AI严格遵守提示词，对于这种特殊情况非常必要
   - 负面提示词的位置很重要，确保所有关于"pants"的否定词都包含在其中
   - "legs_apart"和"separate_garments"是关键词，帮助区分裙子与内裤

## 故障排除

如果依然出现裙子被误认为裤子的问题:
1. 增加CFG值到15（注意：可能导致过度锐化）
2. 在正面提示词最前面添加: MINISKIRT_NOT_PANTS (all caps，作为最强命令)
3. 尝试不同的模型，有些模型可能更好地理解服装区别
4. 使用ControlNet进行姿势控制，选择腿部分开的姿势
5. 负面提示词中额外添加: "continuous_black_fabric"
"""
    
    with open('emotion_prompts/EXTREME_GUIDE.md', 'w', encoding='utf-8') as file:
        file.write(setup_guide)
    
    print("已创建ComfyUI设置指南: emotion_prompts/EXTREME_GUIDE.md")
except Exception as e:
    print(f"创建设置指南时出错: {str(e)}")

print("\n极端解决方案创建完成!")
print("使用说明:")
print("1. 使用EXTREME_SKIRT.txt作为正面提示词")
print("2. 使用EXTREME_NEGATIVE.txt作为负面提示词")
print("3. 将CFG值设置为至少12")
print("4. 采样器设为DPM++ 2M Karras，步数40-50")
print("5. 查看EXTREME_GUIDE.md获取详细配置指南") 