import os

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 更新衬衫描述
old_shirt_description = "Sheer thin white button-up shirt with deep V-neck showing visible nipple outlines through fabric, sleeves rolled up to mid-forearm."
new_shirt_description = "Sheer thin white button-up shirt with deep V-neck, translucent pink lingerie visible underneath showing nipple outlines through fabric, sleeves rolled up to mid-forearm."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换衬衫描述
        content = content.replace(old_shirt_description, new_shirt_description)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"Updated with pink lingerie in: {filename}")

print("All files have been updated with translucent pink lingerie description.")