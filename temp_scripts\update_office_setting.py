#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_setting_and_pose(text):
    """更新场景为办公室，姿势为站立"""
    
    # 新的场景描述
    new_setting = "modern office with elegant minimalist design"
    
    # 新的姿势描述 - 确保是站立姿势
    new_pose = "standing confidently, looking_up, mouth slightly open, face in closeup, seductive look, 3/4 angle view"
    
    # 新的灯光描述
    new_lighting = "Professional office lighting with soft natural light from large windows"
    
    # 更新场景描述
    text = re.sub(r'Standing at [^\.]+\.', 
                f'Standing in a {new_setting}.', text)
    
    # 如果有床的描述，替换成办公桌
    text = re.sub(r'(P|p)osing on her side on bed[^\.]+\.', 
                f'Standing by a sleek office desk, with subtle office decor visible in the background.', text)
    
    # 更新姿势描述
    pose_pattern = r'(H|h)er (standing|confident|elegant|graceful)[^\.]+\.'
    if re.search(pose_pattern, text):
        text = re.sub(pose_pattern, 
                    f'Her {new_pose}, creating a captivating portrait against the professional backdrop.', text)
    
    # 更新照明描述
    text = re.sub(r'(E|e)thereal (L|l)ighting[^\.]+\.', 
                f'{new_lighting}, highlighting her features perfectly.', text)
    
    # 替换任何床或卧室相关的描述
    text = re.sub(r'(bedroom|bed|comfortable bed)[^\.]+\.', 
                 'professional office environment with minimalist design and elegant furnishings.', text)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_setting_and_pose(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_office_example():
    """创建办公室场景的示例文件"""
    example_content = """Ultra high quality, photo, masterpiece, ultra high resolution, realistic, photorealistic, beautiful and youthful Japanese model, 19 years old, with petite body, perky boobs, slender, slender_waist and perfect face, exquisite eyes, luscious lips, blonde_hair, yellow_eyes. Standing in a modern office with elegant minimalist design. She is wearing cutoff jeans, see-through white top, revealing her huge breasts. Her hands hidden, with jewelry adorning her delicate wrists. Her standing confidently, looking_up, mouth slightly open, face in closeup, seductive look, 3/4 angle view, creating a captivating portrait against the professional backdrop. Professional office lighting with soft natural light from large windows, highlighting her features perfectly. The scene captures a perfect balance between professional environment and alluring beauty, with tightly cropped composition emphasizing her face and upper body. saber_alter, fate_(series), DSLR photo, three quarter view, face focus, office setting, business environment, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    example_path = os.path.join(EXAMPLES_DIR, "saber_alter_office.txt")
    with open(example_path, 'w', encoding='utf-8') as file:
        file.write(example_content)
    print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_office_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已更新到新的场景和姿势
            content = update_setting_and_pose(content)
            
            with open(joy_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 