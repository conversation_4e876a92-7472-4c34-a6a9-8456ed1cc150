#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 要移除的裙子相关关键词
SKIRT_KEYWORDS = [
    "PLEATED_SKIRT",
    "black_pleated_miniskirt",
    "pleated_skirt_not_pants",
    "elegant_pleated_skirt"
]

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "COMBINED_SOLUTION.txt",
    "WIDE_LEG_POSE.txt",
    "ULTIMATE_WHITE_UNDERWEAR.txt"
]

def remove_skirt_keywords(text):
    """移除文本中的裙子相关关键词"""
    for keyword in SKIRT_KEYWORDS:
        # 移除关键词，确保前后有空格或逗号等分隔符
        text = re.sub(r'(^|\s|,)' + re.escape(keyword) + r'(\s|,|$)', r'\1\2', text)
        # 清理可能产生的多余空格或连续逗号
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r',\s*,', ',', text)
        # 清理开头和结尾的逗号
        text = re.sub(r'^,\s*', '', text)
        text = re.sub(r',\s*$', '', text)
    
    return text

def update_emotion_files():
    """更新emotion_prompts目录中的所有相关文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 检查是否包含要移除的关键词
            original_content = content
            content = remove_skirt_keywords(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print("\n已跳过的文件:")
        for file in skipped_files:
            print(f"- {file}")

if __name__ == "__main__":
    update_emotion_files() 