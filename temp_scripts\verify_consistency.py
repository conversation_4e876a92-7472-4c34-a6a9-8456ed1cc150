#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 定义表情文件目录
EMOTIONS_DIR = "emotion_prompts"

# 要排除的文件（特殊用途文件）
EXCLUDE_FILES = [
    "negative_prompt.txt", "trigger.txt", "skirt_trigger.txt", 
    "WHITE_UNDERWEAR.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", 
    "WIDE_LEG_POSE.txt", "EXTREME_SKIRT.txt", "EXTREME_NEGATIVE.txt",
    "COMBINED_SOLUTION.txt", "PLEATED_SKIRT_PREVIEW.md", "PLEATED_SKIRT_GUIDE.md",
    "FINAL_INSTRUCTIONS.md", "LEG_POSE_GUIDE.md", "FINAL_SOLUTION.md",
    "LIGHTING_GUIDE.md", "EXTREME_GUIDE.md", "README_USAGE.md", "README.md"
]

# 标准的服装描述
EXPECTED_CLOTHING = "Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage, paired with a glossy black ultra-short mini skirt that sits low on her hips, clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs."

# 标准的场景描述
EXPECTED_SCENE = "Standing elegantly in a clean minimalist interior with a pure white background, the soft diffused lighting creating a serene and sophisticated atmosphere."

def extract_clothing_description(file_path):
    """从文件中提取服装描述"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找以"Wearing"开头的行
    for line in content.split('\n'):
        if line.strip().startswith("Wearing"):
            return line.strip()
    
    return None

def extract_scene_description(file_path):
    """从文件中提取场景描述"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找以"Standing"开头的行
    for line in content.split('\n'):
        if line.strip().startswith("Standing"):
            return line.strip()
    
    return None

def check_consistency():
    """检查所有表情文件的服装和场景描述是否一致"""
    # 获取所有txt文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 过滤掉需要排除的文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"验证 {len(emotion_files)} 个表情文件的服装和场景描述一致性...")
    
    # 验证结果
    clothing_consistency = True
    scene_consistency = True
    clothing_inconsistent_files = []
    scene_inconsistent_files = []
    
    # 逐个检查文件
    for file_path in emotion_files:
        filename = os.path.basename(file_path)
        
        # 检查服装描述
        clothing = extract_clothing_description(file_path)
        if clothing != EXPECTED_CLOTHING:
            clothing_consistency = False
            clothing_inconsistent_files.append((filename, clothing))
        
        # 检查场景描述
        scene = extract_scene_description(file_path)
        if scene != EXPECTED_SCENE:
            scene_consistency = False
            scene_inconsistent_files.append((filename, scene))
    
    # 输出验证结果
    if clothing_consistency:
        print("\n✓ 所有表情文件的服装描述一致")
    else:
        print(f"\n! 发现 {len(clothing_inconsistent_files)} 个文件的服装描述不一致:")
        for filename, desc in clothing_inconsistent_files:
            print(f"\n文件: {filename}")
            print(f"实际描述: {desc}")
            print(f"预期描述: {EXPECTED_CLOTHING}")
    
    if scene_consistency:
        print("\n✓ 所有表情文件的场景描述一致")
    else:
        print(f"\n! 发现 {len(scene_inconsistent_files)} 个文件的场景描述不一致:")
        for filename, desc in scene_inconsistent_files:
            print(f"\n文件: {filename}")
            print(f"实际描述: {desc}")
            print(f"预期描述: {EXPECTED_SCENE}")
    
    # 总结
    if clothing_consistency and scene_consistency:
        print("\n✓ 总结: 所有表情文件的服装和场景描述已经完全一致！")
    else:
        print("\n! 总结: 仍有文件需要修复")

if __name__ == "__main__":
    check_consistency() 