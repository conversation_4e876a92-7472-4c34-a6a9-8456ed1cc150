#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def remove_hand_description(text):
    """移除手部动作的具体描述"""
    
    # 移除"Her hands hidden, with jewelry adorning her delicate wrists."这样的描述
    text = text.replace("Her hands hidden, with jewelry adorning her delicate wrists.", "")
    
    # 移除"One hand confidently resting on her hip while the other hand gently touches her exposed midriff"这样的描述
    hands_pattern = r'One hand confidently resting on her hip while the other hand gently touches her exposed midriff[^\.]*\.'
    text = re.sub(hands_pattern, "", text)
    
    # 移除其他可能的手部描述
    text = re.sub(r'Her hands are[^\.]+\.', '', text)
    text = re.sub(r'Her hands[^\.]+\.', '', text)
    text = re.sub(r'One hand[^\.]+\.', '', text)
    text = re.sub(r'Both hands[^\.]+\.', '', text)
    
    # 处理可能出现的连续空格、空行和多余的标点
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\s+\.', '.', text)
    text = re.sub(r'\.\s+\.', '.', text)
    text = re.sub(r'  +', ' ', text)
    
    return text

def update_all_files():
    """处理所有文件"""
    # 创建要处理的文件列表
    all_files = []
    
    # 添加emotion_prompts目录中的文件
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name not in EXCLUDE_FILES:
            all_files.append(os.path.join(EMOTION_PROMPTS_DIR, file_name))
    
    # 添加aidma_examples目录中的文件
    if os.path.exists(EXAMPLES_DIR):
        for file_name in os.listdir(EXAMPLES_DIR):
            if file_name.endswith(".txt"):
                all_files.append(os.path.join(EXAMPLES_DIR, file_name))
    
    print(f"找到 {len(all_files)} 个文件需要处理")
    
    # 处理所有文件
    updated_files = []
    
    for file_path in all_files:
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            updated_content = remove_hand_description(content)
            
            # 如果内容有变化，则更新文件
            if updated_content != content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                updated_files.append(os.path.basename(file_path))
                print(f"已更新文件: {file_path}")
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    update_all_files() 