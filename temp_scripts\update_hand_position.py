import os
import re
import glob

# 定义要排除的文件
exclude_files = ['README.md', 'README_USAGE.md', 'negative_prompt.txt', 'EXTREME_GUIDE.md', 
                 'EXTREME_NEGATIVE.txt', 'LIGHTING_GUIDE.md', 'FINAL_SOLUTION.md',
                 'WIDE_LEG_POSE.txt', 'ULTIMATE_WHITE_UNDERWEAR.txt', 'EXTREME_SKIRT.txt',
                 'skirt_trigger.txt', 'WHITE_UNDERWEAR.txt', 'LEG_POSE_GUIDE.md',
                 'COMBINED_SOLUTION.txt', 'FINAL_INSTRUCTIONS.md', 'trigger.txt',
                 'PLEATED_SKIRT_GUIDE.md', 'PLEATED_SKIRT_PREVIEW.md']

# 当前的姿势描述
old_pose_pattern = r"Both arms extended outward and away from her body with palms facing upward in an open welcoming gesture, ensuring her elegant outfit is fully displayed with nothing obstructing the view, her graceful pose deliberately arranged to showcase the beautiful pleats and flowing lines of her skirt."

# 新的姿势描述 - 手放在身后
new_pose_pattern = r"Both hands gracefully placed behind her back in a poised stance, creating an elegant silhouette that accentuates her slim waist and perfectly displays the beautiful pleats and flowing lines of her skirt, the subtle tension in her posture enhancing her feminine charm while maintaining a sophisticated appearance."

# 获取emotion_prompts文件夹中的所有txt文件
prompt_files = glob.glob('emotion_prompts/*.txt')

# 初始化计数器
updated_count = 0
skipped_count = 0

for file_path in prompt_files:
    # 获取文件名
    filename = os.path.basename(file_path)
    
    # 跳过排除的文件
    if filename in exclude_files:
        print(f"跳过文件: {filename}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换姿势描述
        updated_content = re.sub(old_pose_pattern, new_pose_pattern, content)
        
        # 检查是否有更改
        if content != updated_content:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print(f"已更新文件: {filename}")
            updated_count += 1
        else:
            print(f"文件无需更新: {filename}")
    
    except Exception as e:
        print(f"处理文件 {filename} 时出错: {str(e)}")

print(f"\n完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。")

# 更新百褶裙预览文件中的推荐姿势描述
try:
    preview_file = 'emotion_prompts/PLEATED_SKIRT_PREVIEW.md'
    with open(preview_file, 'r', encoding='utf-8') as file:
        preview_content = file.read()
    
    # 替换推荐姿势描述
    old_preview_pattern = r"Both arms extended outward and away from her body with palms facing upward in an open welcoming gesture, ensuring her elegant outfit is fully displayed with nothing obstructing the view, her graceful pose deliberately arranged to showcase the beautiful pleats and flowing lines of her skirt."
    
    updated_preview_content = re.sub(old_preview_pattern, new_pose_pattern, preview_content)
    
    if preview_content != updated_preview_content:
        with open(preview_file, 'w', encoding='utf-8') as file:
            file.write(updated_preview_content)
        print(f"已更新预览文件: {preview_file}")
    else:
        print(f"预览文件无需更新: {preview_file}")
except Exception as e:
    print(f"处理预览文件时出错: {str(e)}")

print("\n脚本执行完成! 所有情绪提示词中的手部姿势已更新为'手放在身后'。")
