import os
import shutil

# 表情列表，按照顺序排列
EMOTIONS = [
    "admiration",     # 16 钦佩
    "amusement",      # 17 娱乐/愉悦
    "anger",          # 18 愤怒
    "annoyance",      # 19 烦恼
    "approval",       # 20 赞同
    "caring",         # 21 关怀
    "confusion",      # 22 困惑
    "curiosity",      # 23 好奇
    "desire",         # 24 渴望
    "disappointment", # 25 失望
    "disapproval",    # 26 不赞同
    "disgust",        # 27 厌恶
    "embarrassment",  # 28 尴尬
    "excitement",     # 29 兴奋
    "fear",           # 30 恐惧
    "gratitude",      # 31 感激
    "grief",          # 32 悲痛
    "joy",            # 33 喜悦
    "love",           # 34 爱
    "nervousness",    # 35 紧张
    "neutral",        # 36 中性
    "optimism",       # 37 乐观
    "pride",          # 38 自豪
    "realization",    # 39 顿悟
    "relief",         # 40 释然
    "remorse",        # 41 悔恨
    "sadness",        # 42 悲伤
    "surprise"        # 43 惊讶
]

# 输入和输出文件夹
INPUT_FOLDER = "E:/ComfyUI/output/沈清宜"
OUTPUT_FOLDER = "E:/ComfyUI/output/沈清宜_表情英文"

# 确保输出文件夹存在
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# 获取所有图像文件
files = [f for f in os.listdir(INPUT_FOLDER) if f.endswith('.png')]

# 排序文件
files.sort()

# 映射图像序号到表情名称
file_map = {}
for i, emotion in enumerate(EMOTIONS):
    file_number = i + 16  # 从16开始
    file_name = f"lingyitong_{file_number:05d}_.png"
    
    # 只使用英文名称
    new_name = f"{emotion}.png"
    
    file_map[file_name] = new_name

# 重命名和复制文件
renamed_count = 0
for file in files:
    if file in file_map:
        src = os.path.join(INPUT_FOLDER, file)
        dst = os.path.join(OUTPUT_FOLDER, file_map[file])
        shutil.copy2(src, dst)
        print(f"已复制: {file} -> {file_map[file]}")
        renamed_count += 1
    else:
        print(f"警告: 未找到匹配的表情名称: {file}")

print(f"\n完成! 已重命名 {renamed_count} 个文件。")

# 创建一个映射表
try:
    with open(f"{OUTPUT_FOLDER}/表情映射表.txt", 'w', encoding='utf-8') as f:
        f.write("原始文件名 -> 表情名称 (英文)\n")
        f.write("-" * 40 + "\n")
        for i, emotion in enumerate(EMOTIONS):
            file_number = i + 16  # 从16开始
            file_name = f"lingyitong_{file_number:05d}_.png"
            f.write(f"{file_name} -> {emotion}.png\n")
    
    print(f"已创建表情映射表: {OUTPUT_FOLDER}/表情映射表.txt")
except Exception as e:
    print(f"创建映射表时出错: {str(e)}")

print("\n脚本执行完成!") 