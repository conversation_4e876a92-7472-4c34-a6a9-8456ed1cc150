# GPU Optimization Profiles for ComfyUI
# This file defines GPU-specific optimization parameters for different hardware configurations
# Format follows ComfyUI's existing YAML configuration standards

# NVIDIA V100 SXM2 16GB Configuration
# Optimized for high-performance inference with memory efficiency
v100_sxm2_16gb:
  # VRAM Management
  vram_mode: "normalvram"          # V100 16GB is sufficient, avoid lowvram performance penalty
  reserve_vram: 2                  # Reserve 2GB for system and other processes
  
  # Precision Settings
  precision_unet: "fp16-unet"      # Use FP16 for UNet to reduce memory usage by ~50%
  precision_vae: "fp32-vae"        # Keep VAE in FP32 for stability
  precision_text_enc: "fp16-text-enc"  # Use FP16 for text encoder
  
  # CUDA Optimizations
  cuda_malloc: false               # Disable cudaMallocAsync due to V100 compatibility issues
  async_offload: true              # Enable async weight offloading for better memory management
  
  # Performance Features
  preview_method: "auto"           # Automatic preview method selection
  cache_mode: "classic"            # Use classic caching for stability
  
  # Attention Optimizations
  attention_mode: "auto"           # Let ComfyUI choose optimal attention implementation
  force_channels_last: false       # Keep default memory layout
  
  # Additional V100-specific optimizations
  supports_fp8_compute: false      # V100 doesn't support FP8
  force_fp32: false                # Allow mixed precision
  deterministic: false             # Prioritize performance over determinism

# RTX 4090 24GB Configuration (for reference)
rtx4090_24gb:
  vram_mode: "highvram"
  reserve_vram: 4
  precision_unet: "fp16-unet"
  precision_vae: "fp32-vae"
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: true
  async_offload: true
  preview_method: "auto"
  cache_mode: "lru"
  attention_mode: "flash-attention"
  force_channels_last: false
  supports_fp8_compute: true
  force_fp32: false
  deterministic: false

# RTX 3080 10GB Configuration (for reference)
rtx3080_10gb:
  vram_mode: "normalvram"
  reserve_vram: 1
  precision_unet: "fp16-unet"
  precision_vae: "fp16-vae"
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: true
  async_offload: true
  preview_method: "latent2rgb"
  cache_mode: "classic"
  attention_mode: "pytorch"
  force_channels_last: false
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false

# Flux Quantized Model Optimization
flux_optimized:
  vram_mode: "normalvram"
  reserve_vram: 1                  # Flux量化版本显存需求较低，减少预留
  precision_unet: "fp16-unet"      # 保持FP16精度
  precision_vae: "fp32-vae"        # VAE保持FP32确保质量
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false               # V100兼容性
  async_offload: true
  preview_method: "auto"
  cache_mode: "lru"                # LRU缓存对Flux模型切换有帮助
  attention_mode: "flash-attention" # Flash attention加速
  force_channels_last: false
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false

# Flux Kontext Model Optimization
flux_kontext_optimized:
  vram_mode: "normalvram"
  reserve_vram: 2                  # Kontext可能需要更多显存
  precision_unet: "fp16-unet"
  precision_vae: "fp32-vae"
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false               # V100兼容性
  async_offload: true
  preview_method: "auto"
  cache_mode: "lru"
  attention_mode: "flash-attention"
  force_channels_last: true        # 优化内存布局
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false

# WAN2.1 Video Generation Optimization (480p)
wan21_video_optimized:
  vram_mode: "normalvram"
  reserve_vram: 3                  # 视频生成需要更多显存缓冲
  precision_unet: "fp16-unet"
  precision_vae: "fp16-vae"        # 视频VAE也可以用FP16
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false               # V100兼容性
  async_offload: true
  preview_method: "latent2rgb"     # 更快的预览方法
  cache_mode: "none"               # 视频生成时禁用缓存避免显存碎片
  attention_mode: "auto"
  force_channels_last: false
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false
  enable_sequential_cpu_offload: true  # 序列化CPU卸载

# Mixed Workload Optimization (平衡配置)
mixed_workload:
  vram_mode: "normalvram"
  reserve_vram: 2                  # 平衡的显存预留
  precision_unet: "fp16-unet"
  precision_vae: "fp32-vae"        # 保持VAE精度
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false
  async_offload: true
  preview_method: "auto"
  cache_mode: "lru"                # 支持模型切换
  attention_mode: "auto"
  force_channels_last: false
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false

# Maximum VRAM Utilization (最大显存利用)
v100_max_vram:
  vram_mode: "normalvram"
  reserve_vram: 0.5                # 最小预留，最大化可用显存
  precision_unet: "fp16-unet"
  precision_vae: "fp16-vae"        # VAE也使用FP16节省显存
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false               # V100兼容性
  async_offload: true
  preview_method: "latent2rgb"     # 更节省显存的预览
  cache_mode: "none"               # 禁用缓存避免显存碎片
  attention_mode: "auto"
  force_channels_last: true        # 优化内存布局
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false
  enable_sequential_cpu_offload: true  # 启用序列化CPU卸载
  enable_model_cpu_offload: true   # 启用模型CPU卸载
  enable_vae_slicing: true         # 启用VAE切片
  enable_vae_tiling: true          # 启用VAE平铺

# Aggressive Memory Management (激进显存管理)
v100_aggressive:
  vram_mode: "normalvram"
  reserve_vram: 0.2                # 极小预留，接近15GB可用
  precision_unet: "fp16-unet"
  precision_vae: "fp16-vae"
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false
  async_offload: true
  preview_method: "latent2rgb"
  cache_mode: "none"
  attention_mode: "auto"
  force_channels_last: true
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false
  enable_sequential_cpu_offload: true
  enable_model_cpu_offload: true
  enable_vae_slicing: true
  enable_vae_tiling: true
  enable_attention_slicing: true   # 启用注意力切片
  low_mem_mode: true               # 低内存模式

# Default fallback configuration
default:
  vram_mode: "normalvram"
  reserve_vram: 1
  precision_unet: "fp16-unet"
  precision_vae: "fp32-vae"
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: true
  async_offload: false
  preview_method: "auto"
  cache_mode: "classic"
  attention_mode: "auto"
  force_channels_last: false
  supports_fp8_compute: false
  force_fp32: false
  deterministic: false

# Configuration metadata
metadata:
  version: "1.0.0"
  created_by: "ComfyUI V100 Optimization Script"
  description: "GPU-specific optimization profiles for ComfyUI startup scripts"
  last_updated: "2025-01-19"
  supported_gpus:
    - "NVIDIA Tesla V100-SXM2-16GB"
    - "NVIDIA GeForce RTX 4090"
    - "NVIDIA GeForce RTX 3080"
  notes: |
    This configuration file provides optimized parameters for different GPU models.
    The V100 SXM2 16GB profile is specifically tuned for high-performance inference
    while maintaining memory efficiency and stability.

# V100 INT4 Optimization Profiles
v100_int4_bnb:
  vram_mode: "normalvram"
  reserve_vram: 1                  # INT4模型显存需求更低
  precision_unet: "fp16-unet"
  precision_vae: "fp16-vae"        # 节省更多显存
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false
  async_offload: true
  preview_method: "latent2rgb"
  cache_mode: "lru"
  attention_mode: "auto"
  force_channels_last: true
  enable_sequential_cpu_offload: true
  enable_model_cpu_offload: true
  enable_vae_slicing: true
  enable_vae_tiling: true
  quantization_method: "bitsandbytes"
  load_in_4bit: true

v100_int4_gptq:
  vram_mode: "normalvram"
  reserve_vram: 0.8                # GPTQ更高效
  precision_unet: "fp16-unet"
  precision_vae: "fp16-vae"
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false
  async_offload: true
  preview_method: "latent2rgb"
  cache_mode: "none"               # 避免显存碎片
  attention_mode: "auto"
  force_channels_last: true
  enable_sequential_cpu_offload: true
  enable_model_cpu_offload: true
  enable_vae_slicing: true
  enable_vae_tiling: true
  quantization_method: "gptq"
  gptq_bits: 4
  gptq_group_size: 128

v100_int4_awq:
  vram_mode: "normalvram"
  reserve_vram: 0.8                # AWQ高效压缩
  precision_unet: "fp16-unet"
  precision_vae: "fp16-vae"
  precision_text_enc: "fp16-text-enc"
  cuda_malloc: false
  async_offload: true
  preview_method: "latent2rgb"
  cache_mode: "none"
  attention_mode: "auto"
  force_channels_last: true
  enable_sequential_cpu_offload: true
  enable_model_cpu_offload: true
  enable_vae_slicing: true
  enable_vae_tiling: true
  quantization_method: "awq"
  awq_bits: 4
  awq_group_size: 128
