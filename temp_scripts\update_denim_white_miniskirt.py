#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_outfit_description(text):
    """更新服装描述，将白色睡衣改为白色超短裙"""
    # 替换睡衣为超短裙的各种描述
    replacements = [
        ('white nightie underneath', 'white ultra-short mini skirt underneath'),
        ('white nightie', 'white ultra-short mini skirt'),
        ('white silk nightie', 'white ultra-short pleated mini skirt'),
        ('delicate white fabric of her nightie', 'delicate white fabric of her ultra-short mini skirt')
    ]
    
    for old, new in replacements:
        text = text.replace(old, new)
    
    # 添加MINISKIRT_NOT_PANTS关键词（如果之前被删除）
    if 'MINISKIRT_NOT_PANTS' not in text and 'mini skirt' in text and 'aidmaNSFWunlock' in text:
        text = text.replace('aidmaNSFWunlock', 'MINISKIRT_NOT_PANTS, aidmaNSFWunlock')
    
    # 调整姿势描述，确保可以看到超短裙
    if 'posing on her side on bed' in text:
        text = text.replace('posing on her side on bed', 
                          'posing with legs slightly apart on bed, showcasing her ultra-short mini skirt')
    
    # 确保有关于超短裙长度的描述
    if 'barely covering' not in text and 'mini skirt' in text:
        text = re.sub(r'(white ultra-short .{0,30}mini skirt[^,\.]*)', 
                     r'\1, barely covering her upper thighs', text)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_outfit_description(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_denim_miniskirt_example():
    """创建牛仔衬衫与白色超短裙造型的示例文件"""
    example_content = """beautiful gravure idol with wavy hair, posing with legs slightly apart on bed, showcasing her ultra-short mini skirt. She is wearing an unbuttoned blue denim shirt with white ultra-short mini skirt underneath, barely covering her upper thighs. A pendant with obsidian rose adorns her neck, adding an elegant touch to her stylish look. Her narrow waist and long legs create a graceful silhouette as she reclines comfortably. Soft, ambient lighting creates a warm, intimate atmosphere, highlighting the contrast between the rich blue denim and the pure white fabric of her mini skirt. The scene captures a perfect balance between fashionable style and alluring beauty, with gentle shadows accentuating her feminine curves. Her confident pose draws attention to her long legs and the extremely short hemline of her mini skirt, creating a captivating editorial portrait, high resolution fashion editorial photograph, MINISKIRT_NOT_PANTS, aidmaimageupgrader, aidmaNSFWunlock"""
    
    # 创建第二个例子，更强调光线和质感
    example2_content = """beautiful gravure idol with flowing wavy hair cascading over her shoulders, elegantly posed with legs slightly apart on rumpled bedsheets, showcasing her ultra-short mini skirt. She wears an artfully unbuttoned blue denim shirt revealing a pristine white pleated mini skirt underneath, the skirt's hem barely covering her upper thighs. The contrast between the structured blue denim and the flowing, short white skirt creates a compelling visual narrative. A distinctive pendant featuring an obsidian rose hangs from a fine chain around her neck, catching the soft, diffused light that bathes the scene. Her narrow waist and long, graceful legs are perfectly accentuated by the extremely short skirt as she reclines against the pillows. The intimate bedroom setting is enhanced by gentle, warm lighting that casts subtle shadows, highlighting the textures of the fabrics and drawing attention to the dramatic shortness of her mini skirt. The composition has a thoughtful, editorial quality—capturing both the refined styling and the boldly revealing nature of her outfit in a single, striking image, high resolution fashion editorial photograph, MINISKIRT_NOT_PANTS, aidmaimageupgrader, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    examples = {
        "denim_shirt_miniskirt.txt": example_content,
        "denim_pleated_miniskirt.txt": example2_content
    }
    
    for filename, content in examples.items():
        example_path = os.path.join(EXAMPLES_DIR, filename)
        with open(example_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_denim_miniskirt_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已更新到新的服装描述
            content = update_outfit_description(content)
            
            with open(joy_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 