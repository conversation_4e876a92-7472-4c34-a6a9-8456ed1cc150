import os
import re

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 排除文件列表
exclude_files = ['README.md', 'admiration_glasses.txt']

for filename in os.listdir(folder_path):
    if filename in exclude_files or not filename.endswith('.txt'):
        continue
    
    file_path = os.path.join(folder_path, filename)
    
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 修复手势冲突
    hands_behind_back = "Hands crossed behind her back, not visible from front view."
    has_conflict = False
    conflict_lines = []
    
    # 查找相机角度/姿势的重复行
    camera_lines = set()
    duplicate_indices = []
    
    # 检查手部冲突和重复行
    for i, line in enumerate(lines):
        # 检查手部描述冲突
        if "hand" in line.lower() or "arm" in line.lower():
            if hands_behind_back in lines and line.strip() != hands_behind_back:
                has_conflict = True
                conflict_lines.append(i)
        
        # 检查相机/姿势描述重复
        if "camera" in line.lower() or "angle" in line.lower() or "shot" in line.lower():
            if line in camera_lines:
                duplicate_indices.append(i)
            else:
                camera_lines.add(line)
    
    # 移除冲突的手部描述
    if has_conflict:
        for i in sorted(conflict_lines, reverse=True):
            if i < len(lines):
                del lines[i]
    
    # 移除重复的相机/姿势描述
    for i in sorted(duplicate_indices, reverse=True):
        if i < len(lines):
            del lines[i]
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as file:
        file.writelines(lines)
    
    print(f"Fixed conflicts in: {filename}")

print("All conflicts have been fixed.")