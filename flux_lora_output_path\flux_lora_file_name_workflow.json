{"id": "1fc108bc-8d4e-4b21-b5c5-cd4c6f8a235e", "revision": 0, "last_node_id": 137, "last_link_id": 239, "nodes": [{"id": 38, "type": "SetNode", "pos": [1138.6033935546875, 1.119886875152588], "size": [210, 58], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [{"name": "VALSETTINGS", "type": "VALSETTINGS", "link": 58}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "validation_settings"}, "widgets_values": ["validation_settings"]}, {"id": 48, "type": "GetNode", "pos": [2630, 450], "size": [210, 58], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VALSETTINGS", "type": "VALSETTINGS", "slot_index": 0, "links": [69]}], "title": "Get_validation_settings", "properties": {}, "widgets_values": ["validation_settings"]}, {"id": 61, "type": "PreviewImage", "pos": [3707, 610], "size": [809.35400390625, 458.6750793457031], "flags": {}, "order": 52, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 90}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 63, "type": "GetNode", "pos": [3706.7109375, 460], "size": [210, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VALSETTINGS", "type": "VALSETTINGS", "slot_index": 0, "links": [89]}], "title": "Get_validation_settings", "properties": {}, "widgets_values": ["validation_settings"]}, {"id": 68, "type": "GetNode", "pos": [4765.21875, 468.45684814453125], "size": [210, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VALSETTINGS", "type": "VALSETTINGS", "slot_index": 0, "links": [94]}], "title": "Get_validation_settings", "properties": {}, "widgets_values": ["validation_settings"]}, {"id": 70, "type": "VisualizeLoss", "pos": [5586, -246], "size": [254.40000915527344, 198], "flags": {}, "order": 58, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 217}], "outputs": [{"label": "plot", "name": "plot", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [138]}, {"label": "loss_list", "name": "loss_list", "shape": 3, "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "VisualizeLoss"}, "widgets_values": ["ggplot", 1000, true, 768, 512, false]}, {"id": 73, "type": "Display Any (rgthree)", "pos": [6270, 660], "size": [210, 88], "flags": {}, "order": 68, "mode": 2, "inputs": [{"dir": 3, "label": "输入", "name": "source", "type": "*", "link": 136}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "Node name for S&R": "Display Any (rgthree)"}, "widgets_values": [""]}, {"id": 78, "type": "AddLabel", "pos": [2023, 1177], "size": [315, 274], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 112}, {"name": "caption", "type": "STRING", "widget": {"name": "caption"}, "link": null}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 111}], "outputs": [{"label": "图像", "name": "IMAGE", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [200]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "AddLabel"}, "widgets_values": [10, 2, 48, 32, "white", "black", "FreeMono.ttf", "Text", "up", ""]}, {"id": 79, "type": "SomethingToString", "pos": [1815, 1177], "size": [315, 82], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"label": "输入", "name": "input", "type": "*", "link": 220}], "outputs": [{"label": "字符串", "name": "STRING", "shape": 3, "type": "STRING", "slot_index": 0, "links": [111]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "SomethingToString"}, "widgets_values": ["steps ", ""]}, {"id": 80, "type": "AddLabel", "pos": [2982, 1177], "size": [315, 274], "flags": {"collapsed": true}, "order": 44, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 119}, {"name": "caption", "type": "STRING", "widget": {"name": "caption"}, "link": null}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 117}], "outputs": [{"label": "图像", "name": "IMAGE", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [201]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "AddLabel"}, "widgets_values": [10, 2, 48, 32, "white", "black", "FreeMono.ttf", "Text", "up", ""]}, {"id": 81, "type": "SomethingToString", "pos": [2774, 1177], "size": [315, 82], "flags": {"collapsed": true}, "order": 38, "mode": 0, "inputs": [{"label": "输入", "name": "input", "type": "*", "link": 235}], "outputs": [{"label": "字符串", "name": "STRING", "shape": 3, "type": "STRING", "slot_index": 0, "links": [117]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "SomethingToString"}, "widgets_values": ["steps ", ""]}, {"id": 82, "type": "SomethingToString", "pos": [3909, 1177], "size": [315, 82], "flags": {"collapsed": true}, "order": 47, "mode": 0, "inputs": [{"label": "输入", "name": "input", "type": "*", "link": 234}], "outputs": [{"label": "字符串", "name": "STRING", "shape": 3, "type": "STRING", "slot_index": 0, "links": [121]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "SomethingToString"}, "widgets_values": ["steps ", ""]}, {"id": 83, "type": "AddLabel", "pos": [4130, 1177], "size": [315, 274], "flags": {"collapsed": true}, "order": 53, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 122}, {"name": "caption", "type": "STRING", "widget": {"name": "caption"}, "link": null}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 121}], "outputs": [{"label": "图像", "name": "IMAGE", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [204]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "AddLabel"}, "widgets_values": [10, 2, 48, 32, "white", "black", "FreeMono.ttf", "Text", "up", ""]}, {"id": 84, "type": "SomethingToString", "pos": [4963, 1177], "size": [315, 82], "flags": {"collapsed": true}, "order": 55, "mode": 0, "inputs": [{"label": "输入", "name": "input", "type": "*", "link": 215}], "outputs": [{"label": "字符串", "name": "STRING", "shape": 3, "type": "STRING", "slot_index": 0, "links": [124]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "SomethingToString"}, "widgets_values": ["steps ", ""]}, {"id": 85, "type": "AddLabel", "pos": [5171, 1177], "size": [315, 274], "flags": {"collapsed": true}, "order": 61, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 126}, {"name": "caption", "type": "STRING", "widget": {"name": "caption"}, "link": null}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 124}], "outputs": [{"label": "图像", "name": "IMAGE", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [207]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "AddLabel"}, "widgets_values": [10, 2, 48, 32, "white", "black", "FreeMono.ttf", "Text", "up", ""]}, {"id": 88, "type": "Display Any (rgthree)", "pos": [1143.6033935546875, 82.11988067626953], "size": [210, 88], "flags": {}, "order": 26, "mode": 0, "inputs": [{"dir": 3, "label": "输入", "name": "source", "type": "*", "link": 182}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "Node name for S&R": "Display Any (rgthree)"}, "widgets_values": [""]}, {"id": 89, "type": "UploadToHuggingFace", "pos": [5900, 660], "size": [315, 178], "flags": {}, "order": 64, "mode": 2, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": null}, {"label": "source_path", "name": "source_path", "type": "STRING", "widget": {"name": "source_path"}, "link": 230}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "links": null}, {"label": "status", "name": "status", "shape": 3, "type": "STRING", "links": [136]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "UploadToHuggingFace"}, "widgets_values": ["", "", "", true, ""]}, {"id": 90, "type": "SaveImage", "pos": [5877, -60], "size": [574.23046875, 414.46881103515625], "flags": {}, "order": 62, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 138}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["flux_lora_loss_plot"]}, {"id": 105, "type": "Display Any (rgthree)", "pos": [483, -811], "size": [1073.7608642578125, 492.8503112792969], "flags": {}, "order": 27, "mode": 0, "inputs": [{"dir": 3, "label": "输入", "name": "source", "type": "*", "link": 183}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "Node name for S&R": "Display Any (rgthree)"}, "widgets_values": [""]}, {"id": 111, "type": "TrainDatasetAdd", "pos": [-472.3832702636719, 203.22264099121094], "size": [267.5897521972656, 354], "flags": {}, "order": 22, "mode": 4, "inputs": [{"label": "dataset_config", "name": "dataset_config", "type": "JSON", "link": 187}, {"label": "regularization", "name": "regularization", "shape": 7, "type": "JSON", "link": null}], "outputs": [{"label": "dataset", "name": "dataset", "shape": 3, "type": "JSON", "slot_index": 0, "links": [188]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "TrainDatasetAdd"}, "widgets_values": [768, 768, 1, "../datasets/a<PERSON><PERSON>_yoshida_no_caps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", true, false, 1, 256, 1024, [false, true], [false, true]]}, {"id": 112, "type": "TrainDatasetAdd", "pos": [-172.38327026367188, 203.22264099121094], "size": [259.5897521972656, 354], "flags": {}, "order": 23, "mode": 4, "inputs": [{"label": "dataset_config", "name": "dataset_config", "type": "JSON", "link": 188}, {"label": "regularization", "name": "regularization", "shape": 7, "type": "JSON", "link": null}], "outputs": [{"label": "dataset", "name": "dataset", "shape": 3, "type": "JSON", "slot_index": 0, "links": [189]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "TrainDatasetAdd"}, "widgets_values": [1024, 1024, 1, "../datasets/a<PERSON><PERSON>_yoshida_no_caps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", true, false, 1, 256, 1024, [false, true], [false, true]]}, {"id": 115, "type": "Note", "pos": [248.60342407226562, -89.88011932373047], "size": [462.68292236328125, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["You can use same models as you use for inference in Comfy. When fp8_base is enabled, the model is downcasted to torch.float_e4m3fn on initialize, meaning if you load fp8 model here it should also be in same format."], "color": "#432", "bgcolor": "#653"}, {"id": 117, "type": "ImageConcatFromBatch", "pos": [6690, 410], "size": [315, 106], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 195}, {"name": "num_columns", "type": "INT", "widget": {"name": "num_columns"}, "link": 199}], "outputs": [{"label": "图像", "name": "IMAGE", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [210]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "ImageConcatFromBatch"}, "widgets_values": [3, false, 4096]}, {"id": 119, "type": "ImageBatchMulti", "pos": [6820, 180], "size": [210, 142], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "图像_1", "name": "image_1", "type": "IMAGE", "link": 202}, {"label": "图像_2", "name": "image_2", "type": "IMAGE", "link": 203}, {"label": "图像_3", "name": "image_3", "type": "IMAGE", "link": 206}, {"label": "图像_4", "name": "image_4", "type": "IMAGE", "link": 208}], "outputs": [{"label": "图像", "name": "images", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [195]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b"}, "widgets_values": [4, null]}, {"id": 120, "type": "GetImageSizeAndCount", "pos": [6830, 120], "size": [210, 86], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 209}], "outputs": [{"label": "图像", "name": "image", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": []}, {"label": "宽度", "name": "width", "shape": 3, "type": "INT", "links": null}, {"label": "高度", "name": "height", "shape": 3, "type": "INT", "links": null}, {"label": "数量", "name": "count", "shape": 3, "type": "INT", "slot_index": 3, "links": [199]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 121, "type": "SetNode", "pos": [2170, 1177], "size": [210, 58], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 200}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "Sampler1"}, "widgets_values": ["Sampler1"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 122, "type": "SetNode", "pos": [3128, 1177], "size": [210, 58], "flags": {"collapsed": true}, "order": 48, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 201}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "Sampler2"}, "widgets_values": ["Sampler2"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 123, "type": "GetNode", "pos": [6640, 190], "size": [210, 58], "flags": {"collapsed": true}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [202, 209]}], "title": "Get_Sampler1", "properties": {}, "widgets_values": ["Sampler1"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 124, "type": "GetNode", "pos": [6640, 230], "size": [210, 58], "flags": {"collapsed": true}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [203]}], "title": "Get_Sampler2", "properties": {}, "widgets_values": ["Sampler2"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 125, "type": "SetNode", "pos": [4278, 1177], "size": [210, 58], "flags": {"collapsed": true}, "order": 56, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 204}], "outputs": [{"label": "输出", "name": "*", "type": "*", "slot_index": 0, "links": []}], "properties": {"previousName": "Sampler3"}, "widgets_values": ["Sampler3"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 126, "type": "GetNode", "pos": [6650, 280], "size": [210, 58], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [206]}], "title": "Get_Sampler3", "properties": {}, "widgets_values": ["Sampler3"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 127, "type": "SetNode", "pos": [5319, 1177], "size": [210, 58], "flags": {"collapsed": true}, "order": 66, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 207}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "properties": {"previousName": "Sampler4"}, "widgets_values": ["Sampler4"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 128, "type": "GetNode", "pos": [6640, 330], "size": [210, 58], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [208]}], "title": "Get_Sampler4", "properties": {}, "widgets_values": ["Sampler4"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 131, "type": "Note", "pos": [478, -884], "size": [210, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["sanity check that all the args are chosen correctly"], "color": "#432", "bgcolor": "#653"}, {"id": 65, "type": "FluxTrainValidate", "pos": [4775.216796875, 518.4568481445312], "size": [468.5999755859375, 46], "flags": {}, "order": 57, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 233}, {"label": "validation_settings", "name": "validation_settings", "shape": 7, "type": "VALSETTINGS", "link": 94}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [217, 229]}, {"label": "validation_images", "name": "validation_images", "shape": 3, "type": "IMAGE", "slot_index": 1, "links": [95, 126]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainValidate"}, "widgets_values": []}, {"id": 46, "type": "PreviewImage", "pos": [2654, 609], "size": [850.0181274414062, 452.6767578125], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 70}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 66, "type": "PreviewImage", "pos": [4785, 628], "size": [850.0181274414062, 452.6767578125], "flags": {}, "order": 60, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 95}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 37, "type": "FluxTrainValidationSettings", "pos": [775, 18], "size": [315, 250], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "validation_settings", "name": "validation_settings", "shape": 3, "type": "VALSETTINGS", "slot_index": 0, "links": [58]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainValidationSettings"}, "widgets_values": [20, 1024, 1024, 3, 42, "fixed", true, 0.5, 1.15]}, {"id": 116, "type": "Note", "pos": [776, -111], "size": [308.08209228515625, 88], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["Validation sampling settings are set here for all the sampler nodes."], "color": "#432", "bgcolor": "#653"}, {"id": 9, "type": "PreviewImage", "pos": [1547, 596], "size": [891.4732666015625, 476.6578063964844], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 8}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 14, "type": "FluxTrainSave", "pos": [1988, 256], "size": [393, 122], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 218}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [221]}, {"label": "lora_path", "name": "lora_path", "shape": 3, "type": "STRING", "slot_index": 1, "links": []}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainSave"}, "widgets_values": [false, false]}, {"id": 8, "type": "FluxTrainValidate", "pos": [1552, 500], "size": [468.5999755859375, 46], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 221}, {"label": "validation_settings", "name": "validation_settings", "shape": 7, "type": "VALSETTINGS", "link": 60}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [219]}, {"label": "validation_images", "name": "validation_images", "shape": 3, "type": "IMAGE", "slot_index": 1, "links": [8, 112]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainValidate"}, "widgets_values": []}, {"id": 40, "type": "GetNode", "pos": [1546, 433], "size": [277.0899353027344, 58], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "VALSETTINGS", "type": "VALSETTINGS", "slot_index": 0, "links": [60]}], "title": "Get_validation_settings", "properties": {}, "widgets_values": ["validation_settings"]}, {"id": 45, "type": "FluxTrainValidate", "pos": [2640, 500], "size": [468.5999755859375, 46], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 227}, {"label": "validation_settings", "name": "validation_settings", "shape": 7, "type": "VALSETTINGS", "link": 69}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [223]}, {"label": "validation_images", "name": "validation_images", "shape": 3, "type": "IMAGE", "slot_index": 1, "links": [70, 119]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainValidate"}, "widgets_values": []}, {"id": 60, "type": "FluxTrainValidate", "pos": [3716.708740234375, 510], "size": [468.5999755859375, 46], "flags": {}, "order": 50, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 225}, {"label": "validation_settings", "name": "validation_settings", "shape": 7, "type": "VALSETTINGS", "link": 89}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [226]}, {"label": "validation_images", "name": "validation_images", "shape": 3, "type": "IMAGE", "slot_index": 1, "links": [90, 122]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainValidate"}, "widgets_values": []}, {"id": 47, "type": "FluxTrainSave", "pos": [3114, 323], "size": [393, 122], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 222}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [227]}, {"label": "lora_path", "name": "lora_path", "shape": 3, "type": "STRING", "links": null}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainSave"}, "widgets_values": [false, false]}, {"id": 129, "type": "AddLabel", "pos": [6937, 60], "size": [315, 274], "flags": {"collapsed": true}, "order": 63, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 210}, {"name": "caption", "type": "STRING", "widget": {"name": "caption"}, "link": null}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 231}], "outputs": [{"label": "图像", "name": "IMAGE", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [214]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "AddLabel"}, "widgets_values": [10, 2, 48, 32, "white", "black", "FreeMono.ttf", "Text", "up", ""]}, {"id": 62, "type": "FluxTrainSave", "pos": [4202, 331], "size": [393, 122], "flags": {}, "order": 46, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 224}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [225]}, {"label": "lora_path", "name": "lora_path", "shape": 3, "type": "STRING", "links": null}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainSave"}, "widgets_values": [false, false]}, {"id": 134, "type": "FluxTrainSave", "pos": [5275, 328], "size": [393, 122], "flags": {}, "order": 54, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 232}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [233]}, {"label": "lora_path", "name": "lora_path", "shape": 3, "type": "STRING", "links": null}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainSave"}, "widgets_values": [false, false]}, {"id": 101, "type": "VisualizeLoss", "pos": [4090, -650], "size": [254.40000915527344, 198], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 166}], "outputs": [{"label": "plot", "name": "plot", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [165]}, {"label": "loss_list", "name": "loss_list", "shape": 3, "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "VisualizeLoss"}, "widgets_values": ["seaborn-v0_8-dark-palette", 100, true, 768, 512, false]}, {"id": 98, "type": "SaveImage", "pos": [1680, -340], "size": [645.9608764648438, 439.37261962890625], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 161}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["flux_lora_loss_plot"]}, {"id": 100, "type": "SaveImage", "pos": [2990, -340], "size": [574.23046875, 414.46881103515625], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 163}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["flux_lora_loss_plot"]}, {"id": 102, "type": "SaveImage", "pos": [4080, -340], "size": [574.23046875, 414.46881103515625], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 165}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["flux_lora_loss_plot"]}, {"id": 95, "type": "OptimizerConfig", "pos": [322, 385], "size": [315, 256], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"label": "optimizer_settings", "name": "optimizer_settings", "shape": 3, "type": "ARGS", "links": [180]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "OptimizerConfig"}, "widgets_values": ["CAME", 1, "constant", 0, 1, 1, 5, "", [false, true]]}, {"id": 74, "type": "Display Any (rgthree)", "pos": [6275, 492], "size": [358.62896728515625, 88], "flags": {}, "order": 65, "mode": 0, "inputs": [{"dir": 3, "label": "输入", "name": "source", "type": "*", "link": 236}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "Node name for S&R": "Display Any (rgthree)"}, "widgets_values": [""]}, {"id": 133, "type": "FluxTrainEnd", "pos": [5870, 492], "size": [317.4000244140625, 98], "flags": {}, "order": 59, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 229}], "outputs": [{"label": "lora_name", "name": "lora_name", "shape": 3, "type": "STRING", "slot_index": 0, "links": [231]}, {"label": "metadata", "name": "metadata", "shape": 3, "type": "STRING", "links": null}, {"label": "lora_path", "name": "lora_path", "shape": 3, "type": "STRING", "slot_index": 2, "links": [230, 236]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainEnd"}, "widgets_values": [false], "color": "#322", "bgcolor": "#533"}, {"id": 130, "type": "SaveImage", "pos": [7132, 121], "size": [619.8221435546875, 714.4110107421875], "flags": {}, "order": 67, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 214}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["flux_lora_trainer_sheet"]}, {"id": 64, "type": "FluxTrainLoop", "pos": [4770, 330], "size": [393, 78], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 226}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [232]}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 1, "links": [215]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainLoop"}, "widgets_values": [750], "color": "#232", "bgcolor": "#353"}, {"id": 59, "type": "FluxTrainLoop", "pos": [3700, 330], "size": [393, 78], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 223}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [166, 224]}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 1, "links": [234]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainLoop"}, "widgets_values": [750], "color": "#232", "bgcolor": "#353"}, {"id": 44, "type": "FluxTrainLoop", "pos": [2630, 330], "size": [393, 78], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 219}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [164, 222]}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 1, "links": [235]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainLoop"}, "widgets_values": [750], "color": "#232", "bgcolor": "#353"}, {"id": 4, "type": "FluxTrainLoop", "pos": [1519, 256], "size": [393, 78], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 181}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "slot_index": 0, "links": [162, 218]}, {"label": "steps", "name": "steps", "shape": 3, "type": "INT", "slot_index": 1, "links": [220]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainLoop"}, "widgets_values": [750], "color": "#232", "bgcolor": "#353"}, {"id": 135, "type": "StringConstantMultiline", "pos": [319, 729], "size": [400, 200], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "字符串", "name": "STRING", "shape": 3, "type": "STRING", "slot_index": 0, "links": [237]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "StringConstantMultiline"}, "widgets_values": ["cute anime girl blonde messy long hair blue eyes wearing a maid outfit with a long black dress with a gold leaf pattern and a white apron in an old dark victorian mansion with a bright window and very expensive stuff everywhere a<PERSON><PERSON><PERSON>da|illustration of a kitten a<PERSON><PERSON><PERSON><PERSON>|photograph of a turtle a<PERSON><PERSON><PERSON><PERSON>|portrait of a female red wizard <PERSON><PERSON><PERSON><PERSON><PERSON>", true, [false, true]]}, {"id": 137, "type": "TrainDatasetGeneralConfig", "pos": [-1122, 203], "size": [316.3266296386719, 202], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"label": "dataset_general", "name": "dataset_general", "shape": 3, "type": "JSON", "links": [239]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "TrainDatasetGeneralConfig"}, "widgets_values": [false, false, false, 0, false, false, ".txt"]}, {"id": 113, "type": "Note", "pos": [-732, 63], "size": [462.68292236328125, 88], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["For multiresolution training, input same source directory with different dataset resolution. From what I hear, Flux likes multiple resolutions.\n\nFor single resolution training, just add single dataset."], "color": "#432", "bgcolor": "#653"}, {"id": 136, "type": "FluxTrainModelSelect", "pos": [251.60342407226562, 45.11988067626953], "size": [427.607421875, 154], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "lora_path", "name": "lora_path", "type": "STRING", "widget": {"name": "lora_path"}, "link": null}], "outputs": [{"label": "flux_models", "name": "flux_models", "shape": 3, "type": "TRAIN_FLUX_MODELS", "links": [238]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "FluxTrainModelSelect"}, "widgets_values": ["flux1-dev-fp8.safetensors", "ae.sft", "clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", ""]}, {"id": 97, "type": "VisualizeLoss", "pos": [1700, -650], "size": [303.6300048828125, 198], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 162}], "outputs": [{"label": "plot", "name": "plot", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [161]}, {"label": "loss_list", "name": "loss_list", "shape": 3, "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "VisualizeLoss"}, "widgets_values": ["seaborn-v0_8-dark-palette", 100, true, 768, 512, false]}, {"id": 99, "type": "VisualizeLoss", "pos": [2950, -650], "size": [254.40000915527344, 198], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "network_trainer", "name": "network_trainer", "type": "NETWORKTRAINER", "link": 164}], "outputs": [{"label": "plot", "name": "plot", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [163]}, {"label": "loss_list", "name": "loss_list", "shape": 3, "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "VisualizeLoss"}, "widgets_values": ["seaborn-v0_8-dark-palette", 100, true, 768, 512, false]}, {"id": 107, "type": "InitFluxLoRATraining", "pos": [783, 326], "size": [477.3700866699219, 922], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "flux_models", "name": "flux_models", "type": "TRAIN_FLUX_MODELS", "link": 238}, {"label": "dataset", "name": "dataset", "type": "JSON", "link": 189}, {"label": "optimizer_settings", "name": "optimizer_settings", "type": "ARGS", "link": 180}, {"label": "resume_args", "name": "resume_args", "shape": 7, "type": "ARGS", "link": null}, {"label": "block_args", "name": "block_args", "shape": 7, "type": "ARGS", "link": null}, {"label": "sample_prompts", "name": "sample_prompts", "type": "STRING", "widget": {"name": "sample_prompts"}, "link": 237}, {"label": "network_config", "name": "network_config", "shape": 7, "type": "NETWORK_CONFIG", "link": null}, {"label": "loss_args", "name": "loss_args", "shape": 7, "type": "ARGS", "link": null}], "outputs": [{"label": "network_trainer", "name": "network_trainer", "shape": 3, "type": "NETWORKTRAINER", "links": [181]}, {"label": "epochs_count", "name": "epochs_count", "shape": 3, "type": "INT", "links": [182]}, {"label": "args", "name": "args", "shape": 3, "type": "KOHYA_ARGS", "links": [183]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "InitFluxLoRATraining"}, "widgets_values": ["flux_lora_file_name", "flux_lora_output_path", 64, 64.00000000000001, 0.0004, 3000, true, "disk", "disk", false, "logit_normal", 0, 1, 1.29, "shift", 1, "raw", 1, 3.1582000000000003, false, true, "fp16", "fp16", "sdpa", "", "", "disabled", 0, 0, "enabled", [false, true], [false, true]]}, {"id": 109, "type": "TrainDatasetAdd", "pos": [-772.3832397460938, 203.22264099121094], "size": [281.5897521972656, 354], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "dataset_config", "name": "dataset_config", "type": "JSON", "link": 239}, {"label": "regularization", "name": "regularization", "shape": 7, "type": "JSON", "link": null}], "outputs": [{"label": "dataset", "name": "dataset", "shape": 3, "type": "JSON", "slot_index": 0, "links": [187]}], "properties": {"cnr_id": "comfyui-fluxtrainer", "ver": "1.0.2", "Node name for S&R": "TrainDatasetAdd"}, "widgets_values": [512, 512, 1, "E:\\env\\wateremove\\LYT\\lora-lytong", "lingyitong", true, false, 1, 256, 1024, [false, true], [false, true]]}], "links": [[8, 8, 1, 9, 0, "IMAGE"], [58, 37, 0, 38, 0, "*"], [60, 40, 0, 8, 1, "VALSETTINGS"], [69, 48, 0, 45, 1, "VALSETTINGS"], [70, 45, 1, 46, 0, "IMAGE"], [89, 63, 0, 60, 1, "VALSETTINGS"], [90, 60, 1, 61, 0, "IMAGE"], [94, 68, 0, 65, 1, "VALSETTINGS"], [95, 65, 1, 66, 0, "IMAGE"], [111, 79, 0, 78, 2, "STRING"], [112, 8, 1, 78, 0, "IMAGE"], [117, 81, 0, 80, 2, "STRING"], [119, 45, 1, 80, 0, "IMAGE"], [121, 82, 0, 83, 2, "STRING"], [122, 60, 1, 83, 0, "IMAGE"], [124, 84, 0, 85, 2, "STRING"], [126, 65, 1, 85, 0, "IMAGE"], [136, 89, 1, 73, 0, "*"], [138, 70, 0, 90, 0, "IMAGE"], [161, 97, 0, 98, 0, "IMAGE"], [162, 4, 0, 97, 0, "NETWORKTRAINER"], [163, 99, 0, 100, 0, "IMAGE"], [164, 44, 0, 99, 0, "NETWORKTRAINER"], [165, 101, 0, 102, 0, "IMAGE"], [166, 59, 0, 101, 0, "NETWORKTRAINER"], [180, 95, 0, 107, 2, "ARGS"], [181, 107, 0, 4, 0, "NETWORKTRAINER"], [182, 107, 1, 88, 0, "*"], [183, 107, 2, 105, 0, "*"], [187, 109, 0, 111, 0, "JSON"], [188, 111, 0, 112, 0, "JSON"], [189, 112, 0, 107, 1, "JSON"], [195, 119, 0, 117, 0, "IMAGE"], [199, 120, 3, 117, 1, "INT"], [200, 78, 0, 121, 0, "*"], [201, 80, 0, 122, 0, "*"], [202, 123, 0, 119, 0, "IMAGE"], [203, 124, 0, 119, 1, "IMAGE"], [204, 83, 0, 125, 0, "*"], [206, 126, 0, 119, 2, "IMAGE"], [207, 85, 0, 127, 0, "*"], [208, 128, 0, 119, 3, "IMAGE"], [209, 123, 0, 120, 0, "IMAGE"], [210, 117, 0, 129, 0, "IMAGE"], [214, 129, 0, 130, 0, "IMAGE"], [215, 64, 1, 84, 0, "*"], [217, 65, 0, 70, 0, "NETWORKTRAINER"], [218, 4, 0, 14, 0, "NETWORKTRAINER"], [219, 8, 0, 44, 0, "NETWORKTRAINER"], [220, 4, 1, 79, 0, "*"], [221, 14, 0, 8, 0, "NETWORKTRAINER"], [222, 44, 0, 47, 0, "NETWORKTRAINER"], [223, 45, 0, 59, 0, "NETWORKTRAINER"], [224, 59, 0, 62, 0, "NETWORKTRAINER"], [225, 62, 0, 60, 0, "NETWORKTRAINER"], [226, 60, 0, 64, 0, "NETWORKTRAINER"], [227, 47, 0, 45, 0, "NETWORKTRAINER"], [229, 65, 0, 133, 0, "NETWORKTRAINER"], [230, 133, 2, 89, 1, "STRING"], [231, 133, 0, 129, 2, "STRING"], [232, 64, 0, 134, 0, "NETWORKTRAINER"], [233, 134, 0, 65, 0, "NETWORKTRAINER"], [234, 59, 1, 82, 0, "*"], [235, 44, 1, 81, 0, "*"], [236, 133, 2, 74, 0, "*"], [237, 135, 0, 107, 5, "STRING"], [238, 136, 0, 107, 0, "TRAIN_FLUX_MODELS"], [239, 137, 0, 109, 0, "JSON"]], "groups": [{"id": 1, "title": "Train_01", "bounding": [1439, 120, 1107, 975], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Settings and init", "bounding": [195, -187, 1223, 1511], "color": "#b06634", "font_size": 24, "flags": {}}, {"id": 3, "title": "Train_02", "bounding": [2602, 124, 1046, 975], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Train_03", "bounding": [3681, 128, 1047, 986], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Train_04", "bounding": [4753, 127, 996, 989], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Dataset", "bounding": [-1190, -151, 1362, 851], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.876922695000001, "offset": [1679.4448441138281, 101.3491584487453]}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "ue_links": [], "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4, "seed_widgets": {"37": 4}}