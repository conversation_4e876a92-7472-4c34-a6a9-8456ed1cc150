import os
import re
import glob

# 定义要排除的文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 定义要替换的文本模式
old_edge_pattern = r"often shifting to reveal the edge of her underwear when she moves"
new_edge_pattern = r"the fabric so minimal it barely constitutes clothing, constantly shifting with her slightest movement to expose what should remain hidden"

# 增强裙子描述的暗示性
old_skirt_start = r"Extremely short black mini skirt barely covering her upper thighs"
new_skirt_start = r"Micro-length black mini skirt that could be mistaken for a wide belt, barely reaching below her hipline and leaving almost nothing to imagination"

# 获取emotion_prompts文件夹中的所有txt文件
prompt_files = glob.glob('emotion_prompts/*.txt')

# 初始化计数器
updated_count = 0
skipped_count = 0

# 处理每个文件
for file_path in prompt_files:
    # 检查是否为排除文件
    if os.path.basename(file_path) in exclude_files:
        print(f"跳过文件: {file_path}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换内裤边缘描述
        if old_edge_pattern in content:
            content = content.replace(old_edge_pattern, new_edge_pattern)
        
        # 替换裙子起始描述
        if old_skirt_start in content:
            content = content.replace(old_skirt_start, new_skirt_start)
            
        # 确保内容已更改
        if old_edge_pattern in content or old_skirt_start in content:
            print(f"警告：文件 {file_path} 可能未完全更新")
            skipped_count += 1
        else:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"已更新文件: {file_path}")
            updated_count += 1
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        skipped_count += 1

# 添加隐含文本触发系统
try:
    # 添加一个特殊的隐藏提示词文件，用于触发系统
    with open('emotion_prompts/trigger.txt', 'w', encoding='utf-8') as file:
        file.write("""NSFW, qingyi, 19-year-old, Beautiful woman with neutral expression.

Full body shot showing entire figure from head to toe.
Face directly towards camera with full eye contact.
Natural relaxed expression.
Form-fitting black off-shoulder crop top with ultra-deep V-neck plunging between her impressive breasts, long sleeves covering arms but leaving shoulders completely bare, the smooth stretchy fabric emphasizing her voluptuous chest while exposing her slender waist and navel, tight enough to outline every curve of her upper body.
Micro-length black mini skirt that could be mistaken for a wide belt, barely reaching below her hipline and leaving almost nothing to imagination, riding up to dangerous heights, barely preserving modesty with only the slightest hint of delicate fabric between skin and air, her light-colored intimate wear contrasting with shadow and revealing far more than it conceals, the tight fabric hugging her curves perfectly while creating a tantalizing high-cut silhouette that emphasizes her long slender legs and shapely hips, the fabric so minimal it barely constitutes clothing, constantly shifting with her slightest movement to expose what should remain hidden.
Sitting elegantly on a luxurious white bed with fluffy pillows and pristine sheets, the soft bedding creating a perfect contrast with her black outfit and pale skin.
Posed to fully showcase her outfit, with no part of her body obscured or hidden from view.

Long straight black hair with neat bangs across forehead.
Both hands placed elegantly at her sides or slightly behind her on the bed, ensuring nothing obstructs the view of her outfit, deliberately keeping her chest and lower body completely visible.
Soft natural lighting in bright minimalist interior setting.
show_intimate_details underwear_visible exposed
Neutral expression with direct gaze.
Straight-on camera angle capturing full facial details.""")
    
    print(f"已创建触发文件: emotion_prompts/trigger.txt")
    updated_count += 1
except Exception as e:
    print(f"创建触发文件时出错: {str(e)}")

print(f"\n更新完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。") 