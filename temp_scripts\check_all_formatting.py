#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

def check_file_formatting(file_path):
    """检查文件的格式是否正确"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        # 检查是否有任何问题
        issues = []
        
        # 1. 检查特定短语后是否缺少句点
        special_phrases = ["head to toe", "entire figure", "body language"]
        for phrase in special_phrases:
            pattern = r'({0})(?!\.)(?=\s+[a-zA-Z])'.format(re.escape(phrase))
            matches = re.finditer(pattern, content)
            for match in matches:
                start = max(0, match.start() - 20)
                end = min(len(content), match.end() + 20)
                context = content[start:end]
                issues.append(f"'{phrase}' 后缺少句点: 上下文 '...{context}...'")
        
        # 2. 检查多个连续句点
        pattern = r'\.{2,}'
        matches = re.finditer(pattern, content)
        for match in matches:
            start = max(0, match.start() - 20)
            end = min(len(content), match.end() + 20)
            context = content[start:end]
            issues.append(f"多个连续句点: 上下文 '...{context}...'")
        
        # 3. 检查"High"和"Ultra"前缺少句点的问题
        patterns = [
            (r'(?<!\.)(?<!\s\.)(?<!\.)\s+(High\s+resolution)', "High前缺少句点"),
            (r'(?<!\.)(?<!\s\.)(?<!\.)\s+(Ultra\s+high)', "Ultra前缺少句点"),
        ]
        
        for pattern, issue_desc in patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                start = max(0, match.start() - 20)
                end = min(len(content), match.end() + 20)
                context = content[start:end]
                issues.append(f"{issue_desc}: 上下文 '...{context}...'")
        
        # 4. 检查句点后空格后小写字母（而非大写字母）的问题
        pattern = r'(\.)(\s+)([a-z])'
        matches = re.finditer(pattern, content)
        for match in matches:
            # 检查这不是已知的特殊情况
            full_match = match.group(0)
            if not any(exception in full_match for exception in [".jpg", ".png", ".webp", ".com", ".net", ".org"]):
                start = max(0, match.start() - 20)
                end = min(len(content), match.end() + 20)
                context = content[start:end]
                issues.append(f"句点后小写字母: 上下文 '...{context}...'")
                
        # 5. 检查"photograph,"后面的句点是否正确
        pattern = r'photograph,\s*\.'
        matches = re.finditer(pattern, content)
        for match in matches:
            start = max(0, match.start() - 20)
            end = min(len(content), match.end() + 20)
            context = content[start:end]
            issues.append(f"'photograph,' 后多余句点: 上下文 '...{context}...'")
                
        return issues
    except Exception as e:
        return [f"文件读取错误: {str(e)}"]

def check_all_files():
    """检查所有表情文件的格式"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append((file_name, os.path.join(EMOTION_PROMPTS_DIR, file_name)))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要检查")
    print(f"表情文件列表: {[name for name, _ in emotion_files]}\n")
    
    # 检查所有文件
    files_with_issues = []
    
    for file_name, file_path in emotion_files:
        issues = check_file_formatting(file_path)
        if issues:
            files_with_issues.append((file_name, issues))
            print(f"- 文件 '{file_name}' 有 {len(issues)} 个问题")
        else:
            print(f"- 文件 '{file_name}' 格式正确")
    
    if files_with_issues:
        print("\n详细问题报告:")
        for file_name, issues in files_with_issues:
            print(f"\n文件: {file_name}")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
    else:
        print("\n所有文件格式检查通过，没有发现问题！")
        
    # 显示几个示例文件
    sample_files = ["anger.txt", "joy.txt", "desire.txt"]
    print("\n示例文件内容:")
    for file_name in sample_files:
        file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"\n文件 {file_name} 的内容:")
            print("-" * 50)
            print(content[:500] + "..." if len(content) > 500 else content)
            print("-" * 50)
        except Exception as e:
            print(f"读取文件 {file_name} 时出错: {str(e)}")

if __name__ == "__main__":
    check_all_files()