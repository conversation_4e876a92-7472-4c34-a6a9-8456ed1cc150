defaults:
  # move configs from base_decoder to decoder_nystrom_favor package
  # resulting config would look like
  #
  # decoder_nystrom_favor:
  #  _target_: xformers.factory.block_factory.xFormerDecoderConfig
  #  reversible: False
  #  ...
  #
  # this helps with organizing the configs at a model level
  # the package name is arbitrary but should be unique within the stacks groups
  # to avoid conficts.
  - base_decoder@decoder_nystrom_favor
  # override the attentions :)
  - /attention@decoder_nystrom_favor.multi_head_config_masked.attention: nystrom
  - /attention@decoder_nystrom_favor.multi_head_config_cross.attention: favor
