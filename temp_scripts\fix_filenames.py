#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 要检查的目录
RENAMED_DIR = "E:/ComfyUI/output/张瑾妍/renamed"

def fix_filenames():
    """修复文件名中的错误"""
    if not os.path.exists(RENAMED_DIR):
        print(f"目录不存在: {RENAMED_DIR}")
        return
    
    files = os.listdir(RENAMED_DIR)
    
    # 修复文件名
    fixed_count = 0
    for file in files:
        file_path = os.path.join(RENAMED_DIR, file)
        if os.path.isfile(file_path):
            # 修复文件名后缀
            if file.endswith(".PNGNG"):
                new_name = file.replace(".PNGNG", ".PNG")
                new_path = os.path.join(RENAMED_DIR, new_name)
                os.rename(file_path, new_path)
                print(f"已修复: {file} -> {new_name}")
                fixed_count += 1
            # 修复后缀大小写
            elif file.endswith(".png"):
                new_name = file.replace(".png", ".PNG")
                new_path = os.path.join(RENAMED_DIR, new_name)
                os.rename(file_path, new_path)
                print(f"已修复: {file} -> {new_name}")
                fixed_count += 1
            # 修复其他可能的问题...
    
    print(f"\n总共修复了 {fixed_count} 个文件名")
    
    # 验证所有文件名是否正确
    files = os.listdir(RENAMED_DIR)
    files.sort()
    
    print("\n当前文件列表:")
    for file in files:
        print(f"- {file}")

if __name__ == "__main__":
    fix_filenames()