#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 要检查的目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

def check_duplicate_hand_descriptions(file_path):
    """检查文件中是否存在重复的手部描述"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 查找手部相关描述
        hand_patterns = [
            r'[Hh]er hands[^\.]+\.',
            r'[Oo]ne hand[^\.]+\.',
            r'[Bb]oth hands[^\.]+\.',
            r'[Ss]lender fingers[^\.]+\.',
            r'[Aa]rms crossed[^\.]+\.',
            r'[Ee]xtended slender[^\.]+\.'
        ]
        
        # 收集所有匹配的手部描述
        all_matches = []
        for pattern in hand_patterns:
            matches = re.findall(pattern, content)
            all_matches.extend(matches)
        
        # 检查是否有重复的描述
        duplicates = []
        seen = set()
        for match in all_matches:
            match_lower = match.lower()
            if match_lower in seen:
                duplicates.append(match)
            else:
                seen.add(match_lower)
        
        if duplicates:
            print(f"文件 {file_path} 存在以下重复的手部描述:")
            for dup in duplicates:
                print(f"  - {dup}")
            return True
        
        return False
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def check_all_emotions():
    """检查所有情绪文件"""
    print(f"检查目录: {EMOTION_PROMPTS_DIR} 中的手部描述重复")
    
    files_with_duplicates = []
    total_files = 0
    
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            total_files += 1
            file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
            
            if check_duplicate_hand_descriptions(file_path):
                files_with_duplicates.append(file_name)
    
    print(f"\n总共检查了 {total_files} 个文件")
    if files_with_duplicates:
        print(f"发现 {len(files_with_duplicates)} 个文件包含重复的手部描述:")
        for file_name in files_with_duplicates:
            print(f"- {file_name}")
    else:
        print("没有发现任何重复的手部描述。")

if __name__ == "__main__":
    check_all_emotions()