#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
from datetime import datetime

# 源目录和目标目录
SOURCE_DIR = "E:/ComfyUI/output"
TARGET_DIR = "E:/ComfyUI/output/renamed"

# 表情名称列表（按英文字母顺序）
EMOTION_NAMES = [
    "admiration",
    "amusement",
    "anger",
    "annoyance",
    "approval",
    "caring",
    "confusion",
    "curiosity",
    "desire",
    "disappointment",
    "disapproval",
    "disgust",
    "embarrassment",
    "excitement",
    "fear",
    "gratitude",
    "grief",
    "joy",
    "love",
    "nervousness",
    "neutral",
    "optimism",
    "pride",
    "realization",
    "relief",
    "remorse",
    "sadness",
    "surprise"
]

def get_image_files(directory):
    """获取目录中的所有PNG图片文件（按创建时间排序）"""
    image_files = []
    
    for file_name in os.listdir(directory):
        file_path = os.path.join(directory, file_name)
        # 仅处理直接在output目录下的PNG文件，跳过子目录
        if os.path.isfile(file_path) and file_name.lower().endswith('.png') and os.path.dirname(file_path) == directory:
            # 获取文件创建时间
            creation_time = os.path.getctime(file_path)
            image_files.append((file_path, creation_time, file_name))
    
    # 按创建时间排序
    image_files.sort(key=lambda x: x[1])
    
    return image_files

def rename_images():
    """重命名图片为表情名称.png"""
    # 确保目标目录存在
    if not os.path.exists(TARGET_DIR):
        os.makedirs(TARGET_DIR)
    
    # 获取排序后的图片列表
    image_files = get_image_files(SOURCE_DIR)
    
    print(f"在目录 {SOURCE_DIR} 中找到 {len(image_files)} 个PNG文件")
    
    # 确认数量是否匹配
    if len(image_files) < len(EMOTION_NAMES):
        print(f"警告：图片数量({len(image_files)})少于表情数量({len(EMOTION_NAMES)})")
    
    # 按照表情列表重命名文件
    renamed_count = 0
    for i, emotion_name in enumerate(EMOTION_NAMES):
        if i >= len(image_files):
            print(f"没有更多图片可供表情 {emotion_name} 使用")
            break
        
        # 原始文件信息
        source_path, _, original_name = image_files[i]
        
        # 如果原文件在目标目录中，跳过
        if TARGET_DIR in source_path:
            continue
        
        # 新文件名和路径
        new_name = f"{emotion_name}.png"
        target_path = os.path.join(TARGET_DIR, new_name)
        
        # 复制并重命名文件
        try:
            shutil.copy2(source_path, target_path)
            print(f"已重命名：{original_name} -> {new_name}")
            renamed_count += 1
        except Exception as e:
            print(f"重命名文件 {source_path} 时出错: {str(e)}")
    
    print(f"\n总共重命名了 {renamed_count} 个文件到 {TARGET_DIR}")

if __name__ == "__main__":
    rename_images()