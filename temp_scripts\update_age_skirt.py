import os
import re

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 更新裙子描述
old_skirt_description = "Extremely short semi-transparent black miniskirt, so short that half of ultra-sheer silk purple panties are visible, intimate anatomical details clearly defined through the delicate fabric, hugging hips tightly."
new_skirt_description = "Extremely short black bodycon miniskirt, lower half of skirt riding up to reveal ultra-sheer silk purple panties completely, intimate anatomical details clearly defined through the transparent fabric, hugging hips tightly."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 获取第一行
        lines = content.split('\n')
        first_line = lines[0]
        
        # 添加年龄（如果尚未存在）
        if "23-year-old" not in first_line and "23 year old" not in first_line:
            # 替换NSFW标记后的lingyitong
            if first_line.startswith("NSFW, lingyitong"):
                new_first_line = first_line.replace("NSFW, lingyitong", "NSFW, lingyitong, 23-year-old")
                content = content.replace(first_line, new_first_line)
        
        # 替换裙子描述
        content = content.replace(old_skirt_description, new_skirt_description)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"Updated age and skirt in: {filename}")

print("All files have been updated with 23-year-old age and revealing bodycon skirt description.")