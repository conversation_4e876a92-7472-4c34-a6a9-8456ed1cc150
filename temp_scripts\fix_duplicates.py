import os
import re

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 排除文件列表
exclude_files = ['README.md', 'admiration_glasses.txt']

for filename in os.listdir(folder_path):
    if filename in exclude_files or not filename.endswith('.txt'):
        continue
    
    file_path = os.path.join(folder_path, filename)
    
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # 修复重复的 "through lenses"
    content = content.replace('through lenses through lenses', 'through lenses')
    
    # 修复其他潜在的重复问题
    content = content.replace('with subtle reflection on red glasses with subtle reflection on red glasses', 
                           'with subtle reflection on red glasses')
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"Fixed: {filename}")

print("All duplicates have been fixed.")