from collections import deque
from datetime import datetime
import io
import logging
import sys
import threading
import os
from logging.handlers import RotatingFileHandler
import subprocess
import json

logs = None
stdout_interceptor = None
stderr_interceptor = None


class LogInterceptor(io.TextIOWrapper):
    def __init__(self, stream,  *args, **kwargs):
        buffer = stream.buffer
        encoding = stream.encoding
        super().__init__(buffer, *args, **kwargs, encoding=encoding, line_buffering=stream.line_buffering)
        self._lock = threading.Lock()
        self._flush_callbacks = []
        self._logs_since_flush = []

    def write(self, data):
        entry = {"t": datetime.now().isoformat(), "m": data}
        with self._lock:
            self._logs_since_flush.append(entry)

            # Simple handling for cr to overwrite the last output if it isnt a full line
            # else logs just get full of progress messages
            if isinstance(data, str) and data.startswith("\r") and not logs[-1]["m"].endswith("\n"):
                logs.pop()
            logs.append(entry)
        super().write(data)

    def flush(self):
        super().flush()
        for cb in self._flush_callbacks:
            cb(self._logs_since_flush)
            self._logs_since_flush = []

    def on_flush(self, callback):
        self._flush_callbacks.append(callback)


def get_logs():
    return logs


def on_flush(callback):
    if stdout_interceptor is not None:
        stdout_interceptor.on_flush(callback)
    if stderr_interceptor is not None:
        stderr_interceptor.on_flush(callback)

def setup_logger(log_level: str = 'INFO', capacity: int = 300, use_stdout: bool = False):
    global logs
    if logs:
        return

    # Override output streams and log to buffer
    logs = deque(maxlen=capacity)

    global stdout_interceptor
    global stderr_interceptor
    stdout_interceptor = sys.stdout = LogInterceptor(sys.stdout)
    stderr_interceptor = sys.stderr = LogInterceptor(sys.stderr)

    # Setup default global logger
    logger = logging.getLogger()
    logger.setLevel(log_level)

    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(logging.Formatter("%(message)s"))

    if use_stdout:
        # Only errors and critical to stderr
        stream_handler.addFilter(lambda record: not record.levelno < logging.ERROR)

        # Lesser to stdout
        stdout_handler = logging.StreamHandler(sys.stdout)
        stdout_handler.setFormatter(logging.Formatter("%(message)s"))
        stdout_handler.addFilter(lambda record: record.levelno < logging.ERROR)
        logger.addHandler(stdout_handler)

    logger.addHandler(stream_handler)


STARTUP_WARNINGS = []


def log_startup_warning(msg):
    logging.warning(msg)
    STARTUP_WARNINGS.append(msg)


def print_startup_warnings():
    for s in STARTUP_WARNINGS:
        logging.warning(s)
    STARTUP_WARNINGS.clear()


# Startup script logging functionality
startup_logger = None
gpu_monitor_logger = None


def setup_startup_logger(log_dir='logs', log_level='INFO'):
    """
    Setup dedicated logger for startup scripts with file rotation.

    Args:
        log_dir (str): Directory to store log files
        log_level (str): Logging level (DEBUG, INFO, WARNING, ERROR)

    Returns:
        logging.Logger: Configured startup logger
    """
    global startup_logger

    if startup_logger is not None:
        return startup_logger

    # Create logs directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)

    # Create startup logger
    startup_logger = logging.getLogger('comfyui_startup')
    startup_logger.setLevel(getattr(logging, log_level.upper()))

    # Prevent duplicate handlers
    if startup_logger.handlers:
        return startup_logger

    # Create log file with date
    log_file = os.path.join(log_dir, f'comfyui_startup_{datetime.now().strftime("%Y%m%d")}.log')

    # Setup rotating file handler (10MB max, keep 5 backups)
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )

    # Setup console handler for immediate feedback
    console_handler = logging.StreamHandler()

    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_formatter = logging.Formatter('%(levelname)s: %(message)s')

    # Apply formatters
    file_handler.setFormatter(file_formatter)
    console_handler.setFormatter(console_formatter)

    # Add handlers to logger
    startup_logger.addHandler(file_handler)
    startup_logger.addHandler(console_handler)

    startup_logger.info("Startup logger initialized")
    startup_logger.info(f"Log file: {log_file}")

    return startup_logger


def setup_gpu_monitor_logger(log_dir='logs'):
    """
    Setup dedicated logger for GPU monitoring and performance tracking.

    Args:
        log_dir (str): Directory to store log files

    Returns:
        logging.Logger: Configured GPU monitor logger
    """
    global gpu_monitor_logger

    if gpu_monitor_logger is not None:
        return gpu_monitor_logger

    # Create logs directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)

    # Create GPU monitor logger
    gpu_monitor_logger = logging.getLogger('comfyui_gpu_monitor')
    gpu_monitor_logger.setLevel(logging.INFO)

    # Prevent duplicate handlers
    if gpu_monitor_logger.handlers:
        return gpu_monitor_logger

    # Create log file for GPU monitoring
    log_file = os.path.join(log_dir, f'comfyui_gpu_monitor_{datetime.now().strftime("%Y%m%d")}.log')

    # Setup rotating file handler (5MB max, keep 3 backups)
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )

    # Create formatter for structured GPU data
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)

    # Add handler to logger
    gpu_monitor_logger.addHandler(file_handler)

    gpu_monitor_logger.info("GPU monitor logger initialized")

    return gpu_monitor_logger


def log_startup_step(step_name, status='INFO', details=None):
    """
    Log a startup script step with consistent formatting.

    Args:
        step_name (str): Name of the startup step
        status (str): Status level (INFO, SUCCESS, WARNING, ERROR)
        details (str, optional): Additional details about the step
    """
    logger = setup_startup_logger()

    message = f"[{step_name}]"
    if details:
        message += f" {details}"

    if status.upper() == 'SUCCESS':
        logger.info(f"✓ {message}")
    elif status.upper() == 'WARNING':
        logger.warning(f"⚠ {message}")
    elif status.upper() == 'ERROR':
        logger.error(f"✗ {message}")
    else:
        logger.info(message)


def log_gpu_info(gpu_info_dict):
    """
    Log GPU information in structured format.

    Args:
        gpu_info_dict (dict): Dictionary containing GPU information
    """
    logger = setup_gpu_monitor_logger()

    try:
        # Log as JSON for easy parsing
        gpu_info_json = json.dumps(gpu_info_dict, indent=2)
        logger.info(f"GPU Information:\n{gpu_info_json}")
    except Exception as e:
        logger.error(f"Failed to log GPU information: {e}")


def monitor_gpu_memory():
    """
    Monitor current GPU memory usage and log it.

    Returns:
        dict: GPU memory information or None if failed
    """
    logger = setup_gpu_monitor_logger()

    try:
        # Run nvidia-smi to get memory usage
        result = subprocess.run([
            'nvidia-smi',
            '--query-gpu=index,name,memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=5)

        if result.returncode != 0:
            logger.warning("Failed to get GPU memory information")
            return None

        gpu_data = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 6:
                    gpu_info = {
                        'index': parts[0],
                        'name': parts[1],
                        'memory_used_mb': parts[2],
                        'memory_total_mb': parts[3],
                        'utilization_percent': parts[4],
                        'temperature_c': parts[5],
                        'timestamp': datetime.now().isoformat()
                    }
                    gpu_data.append(gpu_info)

        if gpu_data:
            logger.info(f"GPU Memory Status: {json.dumps(gpu_data, indent=2)}")
            return gpu_data

    except subprocess.TimeoutExpired:
        logger.warning("GPU memory monitoring timed out")
    except FileNotFoundError:
        logger.warning("nvidia-smi not found, GPU monitoring disabled")
    except Exception as e:
        logger.error(f"Error monitoring GPU memory: {e}")

    return None


def log_startup_summary(start_time, end_time, success=True, error_message=None):
    """
    Log a summary of the startup process.

    Args:
        start_time (datetime): When the startup process began
        end_time (datetime): When the startup process ended
        success (bool): Whether the startup was successful
        error_message (str, optional): Error message if startup failed
    """
    logger = setup_startup_logger()

    duration = end_time - start_time
    duration_str = str(duration).split('.')[0]  # Remove microseconds

    summary = {
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat(),
        'duration': duration_str,
        'success': success
    }

    if error_message:
        summary['error'] = error_message

    if success:
        logger.info(f"✓ Startup completed successfully in {duration_str}")
    else:
        logger.error(f"✗ Startup failed after {duration_str}: {error_message}")

    logger.info(f"Startup Summary: {json.dumps(summary, indent=2)}")


def cleanup_old_logs(log_dir='logs', days_to_keep=7):
    """
    Clean up old log files to prevent disk space issues.

    Args:
        log_dir (str): Directory containing log files
        days_to_keep (int): Number of days of logs to keep
    """
    logger = setup_startup_logger()

    try:
        if not os.path.exists(log_dir):
            return

        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
        cleaned_count = 0

        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                file_path = os.path.join(log_dir, filename)
                if os.path.getmtime(file_path) < cutoff_time:
                    try:
                        os.remove(file_path)
                        cleaned_count += 1
                        logger.info(f"Cleaned up old log file: {filename}")
                    except Exception as e:
                        logger.warning(f"Failed to remove old log file {filename}: {e}")

        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old log files")

    except Exception as e:
        logger.error(f"Error during log cleanup: {e}")


def get_startup_logger():
    """
    Get the startup logger instance.

    Returns:
        logging.Logger: Startup logger instance
    """
    return setup_startup_logger()


def log_model_performance(model_type, generation_time, vram_used, additional_info=None):
    """
    Log model-specific performance data.

    Args:
        model_type (str): Type of model (flux, flux_kontext, video, etc.)
        generation_time (float): Time taken for generation in seconds
        vram_used (int): VRAM used in MB
        additional_info (dict, optional): Additional performance metrics
    """
    logger = setup_gpu_monitor_logger()

    performance_data = {
        'model_type': model_type,
        'generation_time': generation_time,
        'vram_used_mb': vram_used,
        'timestamp': datetime.now().isoformat()
    }

    if additional_info:
        performance_data.update(additional_info)

    try:
        performance_json = json.dumps(performance_data, indent=2)
        logger.info(f"Model Performance Data:\n{performance_json}")
    except Exception as e:
        logger.error(f"Failed to log model performance: {e}")


def log_model_detection(detected_models, selected_profile):
    """
    Log model detection results and selected optimization profile.

    Args:
        detected_models (list): List of detected model types
        selected_profile (str): Selected optimization profile
    """
    logger = setup_startup_logger()

    detection_data = {
        'detected_models': detected_models,
        'selected_profile': selected_profile,
        'detection_time': datetime.now().isoformat()
    }

    try:
        logger.info(f"Model Detection Results: {json.dumps(detection_data, indent=2)}")

        # Log user-friendly summary
        if detected_models:
            models_str = ', '.join(detected_models)
            logger.info(f"✓ Detected models: {models_str}")
            logger.info(f"✓ Selected optimization profile: {selected_profile}")
        else:
            logger.info("ℹ No specific models detected, using default profile")

    except Exception as e:
        logger.error(f"Failed to log model detection: {e}")


def log_optimization_applied(profile_name, optimization_args):
    """
    Log the applied optimization configuration.

    Args:
        profile_name (str): Name of the applied profile
        optimization_args (list): List of optimization arguments
    """
    logger = setup_startup_logger()

    optimization_data = {
        'profile_name': profile_name,
        'optimization_args': optimization_args,
        'args_count': len(optimization_args),
        'applied_time': datetime.now().isoformat()
    }

    try:
        logger.info(f"Applied Optimization: {json.dumps(optimization_data, indent=2)}")

        # Log user-friendly summary
        if optimization_args:
            args_str = ' '.join(optimization_args)
            logger.info(f"✓ Applied {len(optimization_args)} optimization parameters")
            logger.info(f"✓ Optimization arguments: {args_str}")
        else:
            logger.info("ℹ No specific optimization arguments applied")

    except Exception as e:
        logger.error(f"Failed to log optimization configuration: {e}")


def get_startup_logger():
    """
    Get the startup logger instance.

    Returns:
        logging.Logger: Startup logger instance
    """
    return setup_startup_logger()


def get_gpu_monitor_logger():
    """
    Get the GPU monitor logger instance.

    Returns:
        logging.Logger: GPU monitor logger instance
    """
    return setup_gpu_monitor_logger()
