#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 定义表情文件目录
EMOTIONS_DIR = "emotion_prompts"

# 要排除的文件（特殊用途文件）
EXCLUDE_FILES = [
    "negative_prompt.txt", "trigger.txt", "skirt_trigger.txt", 
    "WHITE_UNDERWEAR.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", 
    "WIDE_LEG_POSE.txt", "EXTREME_SKIRT.txt", "EXTREME_NEGATIVE.txt",
    "COMBINED_SOLUTION.txt", "PLEATED_SKIRT_PREVIEW.md", "PLEATED_SKIRT_GUIDE.md",
    "FINAL_INSTRUCTIONS.md", "LEG_POSE_GUIDE.md", "FINAL_SOLUTION.md",
    "LIGHTING_GUIDE.md", "EXTREME_GUIDE.md", "README_USAGE.md", "README.md"
]

# 新的场景描述（适用于酒馆的纯净背景）
NEW_SCENE_DESCRIPTION = "Standing elegantly against a clean neutral background that can blend with various tavern interiors, the soft ambient lighting creating a warm and inviting atmosphere."

# 新的照明描述（适用于酒馆场景）
NEW_LIGHTING_DESCRIPTION = "Soft, warm lighting with gentle amber highlights creating subtle shadows that complement her features, the carefully balanced illumination suggesting a cozy tavern environment"

def update_emotion_file(file_path):
    """更新单个表情文件的背景描述为酒馆适用的纯净背景"""
    filename = os.path.basename(file_path)
    print(f"处理文件: {filename}")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换场景描述（包含Standing开头的整行）
    old_scene_pattern = r'Standing elegantly in a clean minimalist interior with a pure white background[^\n]*\n'
    if re.search(old_scene_pattern, content):
        content = re.sub(old_scene_pattern, NEW_SCENE_DESCRIPTION + '\n', content)
        print(f"  ✓ 已更新场景描述")
    else:
        print(f"  ! 未找到标准场景描述")
    
    # 替换照明描述
    old_lighting_pattern = r'Soft, even studio lighting[^\n]*\n'
    if re.search(old_lighting_pattern, content):
        content = re.sub(old_lighting_pattern, NEW_LIGHTING_DESCRIPTION + '\n', content)
        print(f"  ✓ 已更新照明描述")
    else:
        print(f"  ! 未找到标准照明描述")
    
    # 保存修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✓ 文件 {filename} 处理完成")

def update_all_emotion_files():
    """更新所有表情文件的背景为酒馆适用的纯净背景"""
    # 获取所有txt文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 过滤掉需要排除的文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"准备更新 {len(emotion_files)} 个表情文件的背景描述...")
    
    updated_files = []
    skipped_files = []
    
    # 逐个处理文件
    for file_path in emotion_files:
        try:
            update_emotion_file(file_path)
            updated_files.append(os.path.basename(file_path))
        except Exception as e:
            print(f"  ! 处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
            skipped_files.append(os.path.basename(file_path))
    
    print(f"\n完成! 成功更新了 {len(updated_files)} 个文件，跳过了 {len(skipped_files)} 个文件。")
    
    if updated_files:
        print("\n已更新的文件:")
        for f in updated_files:
            print(f"  - {f}")
    
    if skipped_files:
        print("\n跳过的文件:")
        for f in skipped_files:
            print(f"  - {f}")

def update_final_standard_doc():
    """更新最终标准文档中的背景描述"""
    standard_doc = "FINAL_CLOTHING_STANDARD.md"
    if not os.path.exists(standard_doc):
        print(f"! 未找到标准文档 {standard_doc}")
        return
    
    print(f"更新标准文档 {standard_doc} 中的背景描述...")
    
    with open(standard_doc, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新场景描述标准
    scene_pattern = r'```\nStanding elegantly in a clean minimalist interior with a pure white background[^`]*```'
    if re.search(scene_pattern, content):
        content = re.sub(scene_pattern, f'```\n{NEW_SCENE_DESCRIPTION}\n```', content)
        print("  ✓ 已更新场景描述标准")
    
    # 更新照明描述标准
    lighting_pattern = r'```\nSoft, even studio lighting[^`]*```'
    if re.search(lighting_pattern, content):
        content = re.sub(lighting_pattern, f'```\n{NEW_LIGHTING_DESCRIPTION}\n```', content)
        print("  ✓ 已更新照明描述标准")
    
    # 更新关键特性部分
    if "**纯白背景**：简洁的背景确保焦点在人物上" in content:
        content = content.replace(
            "**纯白背景**：简洁的背景确保焦点在人物上，避免干扰", 
            "**中性背景**：适合酒馆环境的中性背景，方便与各种酒馆室内场景融合"
        )
        print("  ✓ 已更新关键特性描述")
    
    with open(standard_doc, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✓ 标准文档 {standard_doc} 更新完成")

if __name__ == "__main__":
    update_all_emotion_files()
    update_final_standard_doc()
    print("\n背景已成功更新为适合酒馆场景的纯净背景") 