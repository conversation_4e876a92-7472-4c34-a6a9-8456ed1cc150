name: lint

on:
  workflow_call:
    inputs:
      pre-script:
        type: string

jobs:
  linters:
    runs-on: ubuntu-22.04

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Run pre-script
        if: ${{ inputs.pre-script }}
        run: ${{ inputs.pre-script }}
      # Triton is too slow to install, and beside it's not needed
      - run: sed -i '/triton/d' requirements-test.txt
      - name: Install deps
        run: pip install -r requirements-test.txt
      - name: isort
        if: success() || failure()
        run: python -m isort . --check --profile black
      - name: black
        if: success() || failure()
        run: python -m black --check . --exclude "third_party/"
      - name: mypy
        if: success() || failure()
        run: |
          python -m mypy --version
          python -m mypy --ignore-missing-imports --scripts-are-modules --pretty --exclude "(build|stubs|third_party|docs|examples|xformers/_flash_attn|setup.py)" .
      - name: flake8
        if: success() || failure()
        run: python -m flake8 --config .flake8 --show-source --statistics
      - name: clang-format
        if: success() || failure()
        run: |
          pip install clang-format
          clang-format --version

          # apply to our files - excluding autogenerated files
          ./.github/run-clang-format.py -e "*fmha/autogen" -r xformers/csrc
