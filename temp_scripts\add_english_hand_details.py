#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 手部描述
HAND_DETAILS = "perfect arms, perfect hands, perfect fingers, detailed hands, long fingers, delicate wrists"

# 各表情的手部动作描述（英文）
EMOTION_HAND_GESTURES = {
    "joy": "Her hands elegantly outstretched, slender fingers slightly curved as if celebrating a success.",
    "sadness": "One hand gently touching her forehead, perfect fingers trembling slightly, while the other hand hangs limply at her side.",
    "anger": "Both hands clenched into fists, knuckles turning slightly white, showcasing detailed bone structure.",
    "fear": "Hands unconsciously crossed over her chest, slender fingers nervously intertwining with each other.",
    "surprise": "One hand lightly covering her lips, long fingers slightly spread, displaying perfect nails.",
    "disgust": "One hand slightly raised, palm facing outward, delicate fingers slightly curved in rejection.",
    "love": "Both hands gently placed over her heart, slender fingers elegantly intertwined, expressing deep emotion.",
    "admiration": "One hand extended forward, fingers elegantly pointing toward the subject of admiration.",
    "amusement": "Slender fingers lightly covering the corner of her mouth, other hand naturally resting on her waist.",
    "annoyance": "One hand massaging her temple, showcasing the delicate details of her finger joints.",
    "approval": "Extended slender thumb, other fingers elegantly curled, displaying perfect hand shape.",
    "caring": "Both hands slightly extended forward, palms up, slender fingers gently curved in invitation.",
    "confusion": "One hand lightly touching her chin, long fingers slightly curved, displaying a thoughtful posture.",
    "curiosity": "One hand with fingers gently tapping her lower lip, other hand naturally resting on the table surface.",
    "desire": "Both hands gently crossed and resting on her knees, slender fingers slightly tightening, displaying restrained emotion.",
    "disappointment": "One hand hanging powerlessly, slender fingers slightly curved, expressing loss.",
    "disapproval": "Arms crossed over her chest, slender fingers lightly tapping against her arm, displaying dissatisfaction.",
    "embarrassment": "One hand lightly touching the side of her neck, slender fingers trembling slightly, indicating discomfort.",
    "excitement": "Both hands slightly raised, slender fingers spread open, as if depicting an exciting scene.",
    "gratitude": "Both hands placed gently on her chest, slender fingers slightly overlapping in a gesture of thanks.",
    "grief": "One hand covering her face, perfect fingers slightly parted, the other hand hanging limply.",
    "nervousness": "Hands intertwined in her lap, slender fingers unconsciously fidgeting with each other or playing with a ring.",
    "neutral": "Hands naturally placed at her sides, slender fingers slightly curved, displaying a relaxed state.",
    "optimism": "One hand raised upward, slender fingers spread open, as if looking forward to a bright future.",
    "pride": "One hand gently placed on her chest, slender fingers slightly spread, displaying confidence.",
    "realization": "One hand suddenly raised, elegant index finger pointing upward, indicating a moment of insight.",
    "relief": "Both hands gently placed on her chest, slender fingers slightly relaxed, displaying a state of relief.",
    "remorse": "Both hands tightly clasped together, slender fingers slightly intertwined, knuckles white, expressing inner struggle."
}

# 更新提示词模板的关键词
def update_prompt_keywords(text):
    """添加手部细节关键词到提示词"""
    
    # 检查是否已包含相关关键词
    if "perfect hands" in text:
        return text
    
    # 寻找适合添加手部细节的位置
    keywords = [
        "perfect face", "exquisite eyes", "luscious lips", "slender_waist",
        "petite body", "beautiful and youthful", "beautiful and professional"
    ]
    
    for keyword in keywords:
        if keyword in text:
            # 在关键词后添加手部细节
            return text.replace(keyword, f"{keyword}, {HAND_DETAILS}")
    
    # 如果没有找到合适的位置，在aidmaNSFWunlock前添加
    if "aidmaNSFWunlock" in text:
        return text.replace("aidmaNSFWunlock", f"{HAND_DETAILS}, aidmaNSFWunlock")
    
    # 如果以上都失败，简单地在文本末尾添加
    return f"{text}, {HAND_DETAILS}"

def add_hand_gesture(text, emotion):
    """添加手部姿势描述到文本中"""
    
    # 获取当前情感的手部姿势描述
    gesture = EMOTION_HAND_GESTURES.get(emotion, "Her hands positioned naturally, showcasing perfect fingers and delicate details.")
    
    # 移除可能的中文手部描述
    chinese_pattern = r'[一两双](只)?手[^\.]+\.'
    text = re.sub(chinese_pattern, "", text)
    
    # 检查是否已有英文手部描述
    if "hand" in text.lower() and "finger" in text.lower():
        # 已有手部描述，尝试替换
        patterns = [
            r'(H|h)er hands[^\.]+\.',
            r'(O|o)ne hand[^\.]+\.',
            r'(B|b)oth hands[^\.]+\.',
            r'hands[^\.]+\.'
        ]
        
        for pattern in patterns:
            if re.search(pattern, text):
                text = re.sub(pattern, f"{gesture}", text)
                return text
    
    # 如果没有找到手部描述或无法替换，在适当位置添加
    pos_markers = [
        ". Posed to fully showcase",
        ". Standing by a sleek office desk",
        ". The scene captures",
        ". Professional office lighting"
    ]
    
    for marker in pos_markers:
        if marker in text:
            return text.replace(marker, f". {gesture}{marker}")
    
    # 如果找不到合适的位置，在文本中追加
    return f"{text}\n{gesture}"

def update_emotion_file(file_path, emotion_name):
    """更新表情文件，添加手部描述"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 移除之前添加的中文手部描述
        chinese_pattern = r'[一两双](只)?手[^\.]+\.'
        if re.search(chinese_pattern, content):
            content = re.sub(chinese_pattern, "", content)
            print(f"已移除文件 {file_path} 中的中文手部描述")
        
        # 更新提示词部分
        content = update_prompt_keywords(content)
        
        # 添加英文手部姿势描述
        content = add_hand_gesture(content, emotion_name)
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已更新文件: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(file_name)
    
    print(f"找到 {len(emotion_files)} 个表情文件需要更新")
    
    # 更新所有表情文件
    updated_files = []
    
    for file_name in emotion_files:
        # 提取表情名（去掉.txt后缀）
        emotion_name = file_name.replace(".txt", "")
        file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
        
        if update_emotion_file(file_path, emotion_name):
            updated_files.append(file_name)
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    update_all_emotion_files()