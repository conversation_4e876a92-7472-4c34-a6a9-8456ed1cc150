#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# 创建示例目录
EXAMPLES_DIR = "aidma_examples"
os.makedirs(EXAMPLES_DIR, exist_ok=True)

# 创建示例文件
examples = [
    {
        "filename": "example1_basic.txt",
        "content": """NSFW, aidma<PERSON><PERSON><PERSON><PERSON>lock, beautiful young woman laying on top of a green couch, inspired by <PERSON>, arabesque, in white turtleneck shirt, kaki body suit, hands shielding face, taken with canon 80d, white silky outfit, closeup-view, white leggings, fashion portrait photo, my pov, indoor picture."""
    },
    {
        "filename": "example2_detailed.txt",
        "content": """NSFW, aidmaNS<PERSON><PERSON><PERSON>lock, beautiful young woman, porcelain skin, slim figure, 
laying elegantly on top of a plush green couch, 
indoor setting with soft ambient lighting,
inspired by <PERSON> art style,
arabesque pose with graceful composition,
wearing a form-fitting white turtleneck shirt that accentuates her slender neck,
stylish kaki body suit perfectly contouring her figure,
white silky leggings that catch the light with subtle sheen,
hands gracefully shielding part of her face in an artistic manner,
fashion portrait composition with closeup view,
professional photography aesthetic as if taken with canon 80d,
my pov perspective,
high detail, photorealistic, studio quality lighting."""
    },
    {
        "filename": "example3_with_neg_prompt.txt",
        "content": """NSFW, aidmaNSFWunlock, beautiful young asian woman, flawless complexion, elegant pose,
laying on a green velvet couch, relaxed posture, 
high-end fashion studio interior setting,
wearing white high-neck turtleneck top, form-fitting design,
kaki colored body suit accentuating curves,
white silk leggings with subtle sheen,
hands partially covering face in artistic gesture,
arabesque-inspired composition with flowing lines,
fashion magazine quality, editorial style,
shot on canon 80d, professional studio lighting setup,
closeup portrait framing, capturing intricate details,
indoor photoshoot with controlled lighting."""
    },
    {
        "filename": "negative_prompt_example.txt",
        "content": """(worst quality, low quality, normal quality, lowres, low details, oversaturated, undersaturated, overexposed, underexposed, grayscale, bw, bad photo, bad photography, bad art:1.4), (watermark, signature, text font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), deformed, (mutated:1.2), (mutation:1.2), (deformed iris, deformed pupils, semi-realistic, cgi, 3d, render, sketch, cartoon, drawing, anime, textless:0.8), (bad hands, bad anatomy, bad proportions, bad perspective, missing fingers, extra fingers, malformed hands, extra limbs, missing limbs, extra arms, missing arms, extra legs, missing legs:1.4), close up, nude, topless, pants, jeans, trousers, ugly, fat, obese, chubby, dresses, skirts, oversized clothes, cartoon, distorted face, black and white"""
    },
    {
        "filename": "readme.md",
        "content": """# aidmaNSFWunlock 使用指南

## 简介
aidmaNSFWunlock是一个关键词，用于解锁AI生成特定风格的内容。本目录包含了几个使用示例。

## 如何使用
1. 始终将aidmaNSFWunlock关键词添加到提示词的前面部分，通常紧跟在NSFW之后
2. 搭配详细的服装和场景描述获得最佳效果
3. 使用提供的负面提示词可以避免常见问题

## 示例文件说明
- example1_basic.txt: 基础简洁的提示词示例
- example2_detailed.txt: 更详细的提示词示例，包含丰富的描述
- example3_with_neg_prompt.txt: 包含详细姿势和场景描述的提示词
- negative_prompt_example.txt: 推荐的负面提示词

## 在ComfyUI中使用
1. 将正面提示词复制到CLIP Text Encode节点的正面提示词输入框
2. 将负面提示词复制到CLIP Text Encode节点的负面提示词输入框
3. 建议将CFG值设置为7-9之间以获得最佳效果
4. 推荐使用DPM++ 2M Karras或Euler a采样器

## 最佳实践
- 保持提示词的具体和详细
- 确保aidmaNSFWunlock关键词位于提示词靠前的位置
- 使用推荐的负面提示词避免常见问题
- 实验不同的CFG值以找到最适合您的模型的设置"""
    }
]

# 写入示例文件
for example in examples:
    filepath = os.path.join(EXAMPLES_DIR, example["filename"])
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(example["content"])
    print(f"已创建示例文件: {filepath}")

print("\n所有示例文件已创建完成！")
print(f"示例文件位于: {os.path.abspath(EXAMPLES_DIR)}")
print("使用这些示例作为使用aidmaNSFWunlock关键词的参考。") 