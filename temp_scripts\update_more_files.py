#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

def update_file(file_path):
    """更新文件内容，强制替换bikini描述"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 检查是否含有bikini关键词
        if 'bikini' in content or 'sport top' in content:
            # 更新bikini描述
            content = content.replace('white bikini sport top that accentuates the upper body', 
                               'extremely sheer white gauze bikini top, so thin that the fabric is nearly transparent, clearly revealing the outline of her areolas and nipples beneath')
            
            content = content.replace('white bikini sport top', 
                               'extremely sheer white gauze bikini top, so thin that the fabric is nearly transparent, clearly revealing the outline of her areolas and nipples beneath')
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"已更新文件: {os.path.basename(file_path)}")
            return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    return False

def force_update_files(directory):
    """强制更新目录中的所有文件"""
    updated = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    for file_path in txt_files:
        if update_file(file_path):
            updated += 1
    
    print(f"在目录 {directory} 中成功更新了 {updated} 个文件。")
    return updated

def create_additional_examples():
    """创建额外的示例文件，专注于薄纱材质的效果展示"""
    # 创建第一个示例 - 闪光摄影棚灯光突出薄纱透明效果
    example1 = """beautiful young woman, jingyan, standing elegantly in a photography studio with pure white background, wearing an extremely sheer white gauze bikini top, so thin that it's practically see-through, with her areolas and nipples clearly visible through the translucent fabric. The semi-transparent top is paired with a light blue denim mini skirt that sits low on her hips, creating a striking contrast with the silky pure white thigh-high stockings. The studio's professional flash lighting creates a striking effect that emphasizes the transparency of her top, with highlights making the gauze fabric appear even more translucent. Her confident pose with one hand on her hip draws attention to her exposed midriff while highlighting the see-through quality of her top. The carefully arranged lighting creates intentional highlights that accentuate the revealing nature of the gauze material. Full body portrait with professional fashion lighting techniques that specifically enhance the transparency of thin fabrics, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 创建第二个示例 - 背光强调薄纱透明度
    example2 = """beautiful young woman, jingyan, standing in an elegant white studio setting with her back partially to a window, creating dramatic backlighting through her extremely sheer white gauze bikini top. The ultra-thin fabric is illuminated from behind, making it completely transparent and clearly revealing the detailed outlines of her areolas and nipples. The backlight creates a glowing halo effect around her silhouette while emphasizing the gauze top's complete lack of opacity. She pairs this with a light blue denim mini skirt and white silk thigh-high stockings. The creative lighting technique specifically highlights how thin and see-through the bikini fabric truly is, with every detail of what's beneath clearly visible. Her confident pose facing the camera contrasts with the vulnerability of wearing such a revealing top, with the strategic lighting serving to enhance the transparency to maximum effect, MINISKIRT_NOT_PANTS, aidmaNSFWunlock"""
    
    # 确保目录存在
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入示例文件
    examples = {
        "sheer_bikini_studio_lighting.txt": example1,
        "sheer_bikini_backlight.txt": example2
    }
    
    for file_name, content in examples.items():
        file_path = os.path.join(EXAMPLES_DIR, file_name)
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"已创建新示例文件: {file_name}")

def main():
    """主函数"""
    print("开始强制更新所有文件...")
    
    # 更新表情文件
    emotion_updated = force_update_files(EMOTION_PROMPTS_DIR)
    
    # 更新示例文件
    examples_updated = force_update_files(EXAMPLES_DIR)
    
    # 创建新的示例文件
    create_additional_examples()
    
    print(f"\n更新完成:")
    print(f"- 更新了 {emotion_updated} 个表情文件")
    print(f"- 更新了 {examples_updated} 个示例文件")
    print(f"- 添加了 2 个新的专业示例文件，突出展示薄纱材质的透明效果")

if __name__ == "__main__":
    main() 