#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 定义表情文件目录
EMOTIONS_DIR = "emotion_prompts"

# 要排除的文件
EXCLUDE_FILES = [
    "negative_prompt.txt", "trigger.txt", "skirt_trigger.txt", 
    "WHITE_UNDERWEAR.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", 
    "WIDE_LEG_POSE.txt", "EXTREME_SKIRT.txt", "EXTREME_NEGATIVE.txt",
    "COMBINED_SOLUTION.txt"
]

def fix_clothing_descriptions():
    """修复所有情绪文件中的服装描述冲突"""
    # 获取所有表情文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 排除特殊文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"准备修复 {len(emotion_files)} 个表情文件的服装描述...")
    
    # 服装描述修复计数
    fixed_count = 0
    skipped_count = 0
    
    # 统一的服装描述 - 没有提及内裤或丝袜，明确指出是黑色迷你裙
    correct_description = (
        "Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage, "
        "paired with a glossy black ultra-short mini skirt that sits low on her hips, "
        "clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs."
    )
    
    # 正则表达式模式，用于识别和替换服装描述
    clothing_pattern = r"Wearing.+?(?=\.\s*Standing|$)"
    
    for file in emotion_files:
        filename = os.path.basename(file)
        print(f"\n处理文件: {filename}")
        
        with open(file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有需要替换的服装描述
        if re.search(clothing_pattern, content):
            # 替换服装描述
            new_content = re.sub(clothing_pattern, correct_description, content)
            
            # 检查是否有任何更改
            if new_content != content:
                with open(file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f"  ✓ 已修复服装描述")
                fixed_count += 1
            else:
                print(f"  ✓ 服装描述已符合标准，无需修改")
                skipped_count += 1
        else:
            print(f"  ⚠️ 未找到可替换的服装描述模式")
            skipped_count += 1
    
    print(f"\n完成! 修复了 {fixed_count} 个文件，跳过了 {skipped_count} 个文件。")

def create_example_file():
    """创建一个示例文件，展示正确的服装描述"""
    example_content = """# 服装描述标准示例

## 推荐使用的标准描述

```
Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage, 
paired with a glossy black ultra-short mini skirt that sits low on her hips, 
clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs.
```

## 避免的冲突描述

1. 避免同时描述迷你裙和内裤/短裤，这会导致生成结果混乱
2. 避免在同一描述中提及丝袜，除非有特殊需要
3. 保持一致的颜色描述 - 上衣没有指定颜色，裙子明确为黑色
4. 确保描述具体而明确，避免模糊的表述

## 为什么这样描述更好

1. 清晰指定了"glossy black ultra-short mini skirt"明确了裙子的外观
2. 描述了裙子的位置("sits low on her hips")和长度("barely covering the upper thighs")
3. 突出了关键细节("clearly exposing her bare midriff and navel")
4. 没有提及可能导致冲突的其他服装元素
5. 简洁明了，同时提供了足够细节引导图像生成

## 特殊需要

如果需要展示白色内裤效果，请使用专门的WIDE_LEG_POSE.txt或ULTIMATE_WHITE_UNDERWEAR.txt提示词，
而不是修改标准情绪文件中的服装描述。
"""
    
    # 确保目录存在
    example_dir = "clothing_examples"
    os.makedirs(example_dir, exist_ok=True)
    
    # 写入示例文件
    example_file = os.path.join(example_dir, "STANDARD_CLOTHING_GUIDE.md")
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write(example_content)
    
    print(f"\n已创建标准服装描述指南: {example_file}")

if __name__ == "__main__":
    fix_clothing_descriptions()
    create_example_file() 