#!/usr/bin/env python3
"""
Jupyter Lab Configuration Helper for ComfyUI Environment
"""

import os
import sys
import json
import argparse
from pathlib import Path
import subprocess

def create_jupyter_config():
    """创建Jupyter Lab配置文件"""
    config_dir = Path.home() / '.jupyter'
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / 'jupyter_lab_config.py'
    
    config_content = '''
# Jupyter Lab Configuration for ComfyUI Environment
c = get_config()

# Network settings
c.ServerApp.ip = '0.0.0.0'  # Allow external connections
c.ServerApp.port = 8888
c.ServerApp.open_browser = True
c.ServerApp.allow_root = True

# Security settings
c.ServerApp.token = ''  # No token for local development
c.ServerApp.password = ''  # No password for local development
c.ServerApp.allow_origin = '*'
c.ServerApp.disable_check_xsrf = True

# File and directory settings
c.ServerApp.notebook_dir = os.getcwd()
c.ServerApp.allow_hidden = True

# Performance settings
c.ServerApp.max_buffer_size = 268435456  # 256MB
c.ServerApp.iopub_data_rate_limit = 10000000  # 10MB/s

# Extension settings
c.LabApp.extensions_in_dev_mode = True
c.ExtensionApp.open_browser = False

# Logging
c.Application.log_level = 'INFO'
'''
    
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"[SUCCESS] Created Jupyter Lab config: {config_file}")
    return config_file

def create_notebook_templates():
    """创建Jupyter notebook模板"""
    notebooks_dir = Path('notebooks')
    notebooks_dir.mkdir(exist_ok=True)
    
    # ComfyUI开发模板
    comfyui_template = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# ComfyUI Development Notebook\n",
                    "\n",
                    "This notebook is set up for ComfyUI development and experimentation.\n",
                    "\n",
                    "## Environment Information\n",
                    "- Environment: ComfyUI Conda Environment\n",
                    "- Python Version: 3.12+\n",
                    "- Available Libraries: PyTorch, ComfyUI, and all dependencies"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Import essential libraries\n",
                    "import sys\n",
                    "import os\n",
                    "import torch\n",
                    "import numpy as np\n",
                    "import matplotlib.pyplot as plt\n",
                    "\n",
                    "# Add ComfyUI to path\n",
                    "comfyui_path = os.path.abspath('..')\n",
                    "if comfyui_path not in sys.path:\n",
                    "    sys.path.append(comfyui_path)\n",
                    "\n",
                    "print(f\"Python version: {sys.version}\")\n",
                    "print(f\"PyTorch version: {torch.__version__}\")\n",
                    "print(f\"CUDA available: {torch.cuda.is_available()}\")\n",
                    "if torch.cuda.is_available():\n",
                    "    print(f\"CUDA device: {torch.cuda.get_device_name()}\")\n",
                    "    print(f\"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# ComfyUI imports (uncomment as needed)\n",
                    "# import comfy.model_management as model_management\n",
                    "# import comfy.utils\n",
                    "# import nodes\n",
                    "\n",
                    "print(\"ComfyUI modules ready for import\")"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "ComfyUI Python",
                "language": "python",
                "name": "comfyui"
            },
            "language_info": {
                "name": "python",
                "version": "3.12"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # 保存模板
    template_file = notebooks_dir / 'ComfyUI_Development_Template.ipynb'
    with open(template_file, 'w', encoding='utf-8') as f:
        json.dump(comfyui_template, f, indent=2)
    
    print(f"[SUCCESS] Created notebook template: {template_file}")
    
    # 创建其他有用的模板
    create_data_analysis_template(notebooks_dir)
    create_model_testing_template(notebooks_dir)
    
    return notebooks_dir

def create_data_analysis_template(notebooks_dir):
    """创建数据分析模板"""
    template = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# Data Analysis and Visualization\n",
                    "\n",
                    "Template for analyzing ComfyUI workflows, model performance, and data."
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "import pandas as pd\n",
                    "import matplotlib.pyplot as plt\n",
                    "import seaborn as sns\n",
                    "import numpy as np\n",
                    "from pathlib import Path\n",
                    "\n",
                    "# Set up plotting\n",
                    "plt.style.use('default')\n",
                    "sns.set_palette('husl')\n",
                    "%matplotlib inline\n",
                    "\n",
                    "print(\"Data analysis libraries loaded\")"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "ComfyUI Python",
                "language": "python",
                "name": "comfyui"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    template_file = notebooks_dir / 'Data_Analysis_Template.ipynb'
    with open(template_file, 'w', encoding='utf-8') as f:
        json.dump(template, f, indent=2)

def create_model_testing_template(notebooks_dir):
    """创建模型测试模板"""
    template = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# Model Testing and Benchmarking\n",
                    "\n",
                    "Template for testing and benchmarking AI models in ComfyUI."
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "import torch\n",
                    "import time\n",
                    "import psutil\n",
                    "import GPUtil\n",
                    "from pathlib import Path\n",
                    "\n",
                    "def get_system_info():\n",
                    "    \"\"\"获取系统信息\"\"\"\n",
                    "    info = {\n",
                    "        'CPU': psutil.cpu_count(),\n",
                    "        'RAM': f\"{psutil.virtual_memory().total / 1024**3:.1f} GB\",\n",
                    "        'GPU': torch.cuda.get_device_name() if torch.cuda.is_available() else 'None',\n",
                    "        'VRAM': f\"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\" if torch.cuda.is_available() else 'None'\n",
                    "    }\n",
                    "    return info\n",
                    "\n",
                    "print(\"System Info:\", get_system_info())"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "ComfyUI Python",
                "language": "python",
                "name": "comfyui"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    template_file = notebooks_dir / 'Model_Testing_Template.ipynb'
    with open(template_file, 'w', encoding='utf-8') as f:
        json.dump(template, f, indent=2)

def install_jupyter_extensions():
    """安装有用的Jupyter Lab扩展"""
    extensions = [
        'jupyterlab-git',
        'jupyterlab-lsp',
        'jupyterlab-code-formatter',
        '@jupyter-widgets/jupyterlab-manager',
        'jupyterlab-plotly',
        'jupyterlab-drawio'
    ]
    
    print("[INFO] Installing Jupyter Lab extensions...")
    
    for ext in extensions:
        try:
            result = subprocess.run(['pip', 'install', ext], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"[SUCCESS] Installed {ext}")
            else:
                print(f"[WARNING] Failed to install {ext}")
        except Exception as e:
            print(f"[ERROR] Error installing {ext}: {e}")

def check_jupyter_installation():
    """检查Jupyter Lab安装状态"""
    try:
        result = subprocess.run(['jupyter', 'lab', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"[SUCCESS] Jupyter Lab version: {version}")
            return True
        else:
            print("[ERROR] Jupyter Lab not found")
            return False
    except Exception as e:
        print(f"[ERROR] Error checking Jupyter Lab: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Jupyter Lab Configuration Helper")
    parser.add_argument("--create-config", action="store_true", help="创建Jupyter配置文件")
    parser.add_argument("--create-templates", action="store_true", help="创建notebook模板")
    parser.add_argument("--install-extensions", action="store_true", help="安装Jupyter扩展")
    parser.add_argument("--check-installation", action="store_true", help="检查安装状态")
    parser.add_argument("--setup-all", action="store_true", help="执行完整设置")
    
    args = parser.parse_args()
    
    if args.create_config:
        create_jupyter_config()
    
    elif args.create_templates:
        create_notebook_templates()
    
    elif args.install_extensions:
        install_jupyter_extensions()
    
    elif args.check_installation:
        check_jupyter_installation()
    
    elif args.setup_all:
        print("=== Jupyter Lab Complete Setup ===")
        check_jupyter_installation()
        create_jupyter_config()
        create_notebook_templates()
        install_jupyter_extensions()
        print("[SUCCESS] Jupyter Lab setup completed!")
    
    else:
        print("Jupyter Lab Configuration Helper")
        print("Use --help for available options")

if __name__ == "__main__":
    main()
