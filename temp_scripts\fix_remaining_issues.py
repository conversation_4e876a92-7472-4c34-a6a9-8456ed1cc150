import os

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 需要修复的问题
fix_list = [
    ("with subtle reflection on red glasses", ""),
    ("hand touching collar or tie", "hand touching collar or chest"),
    ("arms spread wide", "arms positioned elegantly"),
    ("Hand raised protecting face", "Hand slightly raised in cautious gesture")
]

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 检查和修复每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 应用所有修复
        fixed = False
        for old_text, new_text in fix_list:
            if old_text in content:
                content = content.replace(old_text, new_text)
                fixed = True
        
        # 检查重复行
        lines = content.split('\n')
        unique_lines = []
        for line in lines:
            if line.strip() and (line not in unique_lines):
                unique_lines.append(line)
        
        # 只有在发现问题时才更新文件
        if fixed or len(unique_lines) != len(lines):
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write('\n'.join(unique_lines))
            
            print(f"Fixed issues in: {filename}")

print("All remaining issues have been fixed.")