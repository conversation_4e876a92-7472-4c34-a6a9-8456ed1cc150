#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 情绪提示文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 排除不需要修改的文件
EXCLUDE_FILES = [
    "README.md", "PLEATED_SKIRT_GUIDE.md", "PLEATED_SKIRT_PREVIEW.md", 
    "negative_prompt.txt", "FINAL_INSTRUCTIONS.md", "COMBINED_SOLUTION.txt",
    "LEG_POSE_GUIDE.md", "WHITE_UNDERWEAR.txt", "skirt_trigger.txt",
    "EXTREME_SKIRT.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", "WIDE_LEG_POSE.txt",
    "FINAL_SOLUTION.md", "LIGHTING_GUIDE.md", "EXTREME_NEGATIVE.txt",
    "trigger.txt", "EXTREME_GUIDE.md", "README_USAGE.md"
]

# 定义替换函数
def replace_text(text, filename):
    # 记录是否进行了修改
    changes_made = False
    
    # 重要：内容中包含"jingyan"的不修改其他任何部分，只添加Lora标签
    if "jingyan" in text:
        # 仅添加Lora标签
        if not "<lora:aidmaNSFWunlockV2:0.7>" in text:
            print(f"  - 已经有jingyan标记的文件，只添加Lora标签")
            # 在文件末尾添加Lora标签
            if text.strip().endswith("."):
                text = text.strip() + " <lora:aidmaNSFWunlockV2:0.7>, <lora:midjourney_whisper_flux_lora_v01:0.6>, <lora:NSFW_master:0.8>"
            else:
                text = text.strip() + ", <lora:aidmaNSFWunlockV2:0.7>, <lora:midjourney_whisper_flux_lora_v01:0.6>, <lora:NSFW_master:0.8>"
            changes_made = True
        return text, changes_made
    
    # 替换人物名称，添加jingyan
    if not "jingyan" in text and "year-old" in text:
        print(f"  - 添加jingyan到{filename}中...")
        text = text.replace("20-year-old", "jingyan, 20-year-old")
        changes_made = True
    
    # 替换服装描述 - 上衣
    top_pattern = r"Wearing a (light blue off-shoulder top|soft cream-colored open cardigan|sheer thin white button-up shirt)[\s\S]*?(midriff|collarbones|forearm)\."
    if re.search(top_pattern, text):
        print(f"  - 修改 {filename} 中的上衣描述...")
        text = re.sub(
            top_pattern,
            "Wearing a sleek white turtleneck shirt with a form-fitting design that accentuates her slender neck and shoulders, paired with a stylish kaki body suit that perfectly contours her figure.",
            text
        )
        changes_made = True
    
    # 替换服装描述 - 裙子/短裤/裤子
    skirt_pattern = r"(Short white high-waisted mini skirt|Stylish white high-waisted shorts|micro-length black latex bodycon miniskirt|Extremely short black latex bodycon miniskirt)[\s\S]*?(legs|buttocks)\."
    if re.search(skirt_pattern, text):
        print(f"  - 修改 {filename} 中的下装描述...")
        text = re.sub(
            skirt_pattern,
            "Elegant white silky leggings that cling to her slender legs, the luxurious fabric catching the light with subtle sheen, creating a sophisticated and refined look.",
            text
        )
        changes_made = True
    
    # 修改或删除关于内衣的描述
    underwear_pattern = r"(ultra-sheer silk purple panties|no bra underneath|bare breasts subtly visible through the translucent fabric)[\s\S]*?(fabric|underwear fabric)\."
    if re.search(underwear_pattern, text):
        print(f"  - 移除 {filename} 中的内衣描述...")
        text = re.sub(underwear_pattern, "", text)
        changes_made = True
    
    # 替换场景描述 - 匹配任何场景并改为室内沙发
    scene_pattern = r"(Sitting casually on a neatly made bed|Standing gracefully outdoors against a natural wooden backdrop)[\s\S]*?atmosphere\."
    if re.search(scene_pattern, text):
        print(f"  - 修改 {filename} 中的场景描述...")
        text = re.sub(
            scene_pattern,
            "Laying elegantly on top of a plush green couch in a stylish indoor setting, the soft ambient lighting creating an intimate and sophisticated atmosphere.",
            text
        )
        changes_made = True
    
    # 替换手部动作
    hands_pattern = r"(One hand (resting naturally on the bed|gently raised near her face|gently touching collar or chest)[\s\S]*?(pose|features|gesture))\."
    if re.search(hands_pattern, text):
        print(f"  - 修改 {filename} 中的手部动作...")
        text = re.sub(
            hands_pattern,
            "Hands gracefully shielding part of her face in an artful, arabesque-inspired pose, creating a sense of mystery and elegance",
            text
        )
        changes_made = True
    
    # 替换光线描述
    light_pattern = r"(Soft natural daylight|Golden outdoor sunlight|Soft natural lighting)[\s\S]*?(features|illumination)\."
    if re.search(light_pattern, text):
        print(f"  - 修改 {filename} 中的光线描述...")
        text = re.sub(
            light_pattern,
            "Soft indoor lighting creating a fashion portrait-like atmosphere, highlighting her features with professional clarity as if taken with a Canon 80D camera in a closeup view",
            text
        )
        changes_made = True
    
    # 更新标记
    if "WHITE_SKIRT" in text or "WHITE_SHORTS" in text or "BLACK_MINI_SKIRT" in text:
        print(f"  - 更新 {filename} 中的标记...")
        old_tags = ["LIGHT_BLUE_TOP", "WHITE_SKIRT", "casual_bedroom_setting", "natural_pose", 
                  "CREAM_CARDIGAN", "WHITE_CAMISOLE", "WHITE_SHORTS", "outdoor_setting", "cute_pose",
                  "BLACK_MINI_SKIRT", "BLACK_PANTYHOSE", "PURPLE_PANTIES", "round_glasses"]
        
        # 遍历可能的旧标记并替换
        for old_tag in old_tags:
            if old_tag in text:
                text = text.replace(old_tag, "")
        
        # 添加新标记
        if not "WHITE_TURTLENECK" in text:
            text = text.replace("NSFW", "NSFW, WHITE_TURTLENECK, KAKI_BODYSUIT, WHITE_LEGGINGS, indoor_couch, fashion_portrait, aidmaNSFWunlock")
        
        changes_made = True
    
    # 添加Lora标签
    if not "<lora:aidmaNSFWunlockV2:0.7>" in text:
        print(f"  - 在 {filename} 中添加Lora标签...")
        # 在文件末尾添加Lora标签
        if text.strip().endswith("."):
            text = text.strip() + " <lora:aidmaNSFWunlockV2:0.7>, <lora:midjourney_whisper_flux_lora_v01:0.6>, <lora:NSFW_master:0.8>"
        else:
            text = text.strip() + ", <lora:aidmaNSFWunlockV2:0.7>, <lora:midjourney_whisper_flux_lora_v01:0.6>, <lora:NSFW_master:0.8>"
        changes_made = True
    
    return text, changes_made

# 处理所有情绪文件
def update_emotion_files():
    files_updated = 0
    files_skipped = 0
    updated_files = []
    skipped_files = []
    
    # 获取所有.txt文件
    txt_files = glob.glob(os.path.join(EMOTION_PROMPTS_DIR, "*.txt"))
    print(f"找到 {len(txt_files)} 个文本文件")
    
    for file_path in txt_files:
        filename = os.path.basename(file_path)
        
        # 跳过不需要修改的文件
        if filename in EXCLUDE_FILES:
            print(f"跳过: {filename} (在排除列表中)")
            files_skipped += 1
            skipped_files.append(filename)
            continue
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"处理: {filename}")
            # 替换内容
            updated_content, was_changed = replace_text(content, filename)
            
            # 如果内容有变化，写回文件
            if was_changed:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(updated_content)
                print(f"已更新: {filename}")
                files_updated += 1
                updated_files.append(filename)
            else:
                print(f"无变化: {filename}")
                files_skipped += 1
                skipped_files.append(filename)
                
        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")
            files_skipped += 1
            skipped_files.append(filename + f" (错误: {str(e)})")
    
    return files_updated, files_skipped, updated_files, skipped_files

if __name__ == "__main__":
    print("=" * 60)
    print("开始更新提示词文件为新的服装和室内场景...")
    print("=" * 60)
    
    updated, skipped, updated_files, skipped_files = update_emotion_files()
    
    print("\n" + "=" * 60)
    print(f"完成! 已更新 {updated} 个文件，跳过 {skipped} 个文件。")
    
    if updated > 0:
        print("\n已更新的文件:")
        for i, filename in enumerate(updated_files, 1):
            print(f"{i}. {filename}")
    
    if skipped > 0:
        print("\n跳过的文件:")
        for i, filename in enumerate(skipped_files, 1):
            print(f"{i}. {filename}")
    
    print("=" * 60) 