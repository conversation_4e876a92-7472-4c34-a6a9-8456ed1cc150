#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
import re

def check_workflow(workflow_path):
    """检查工作流中可能导致验证错误的问题"""
    
    # 读取工作流JSON文件
    try:
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return False
    
    # 检查是否存在nodes字段
    if 'nodes' not in workflow:
        print("工作流JSON中没有找到nodes字段")
        return False
    
    # 遍历所有节点
    for i, node in enumerate(workflow['nodes']):
        node_type = node.get('type', 'N/A')
        node_id = node.get('id', 'N/A')
        
        print(f"节点 {i} (ID: {node_id}):")
        print(f"  类型: {node_type}")
        
        # 检查properties字段
        if 'properties' in node:
            print("  properties:")
            for prop_key, prop_value in node['properties'].items():
                print(f"    {prop_key}: {prop_value}")
            
            # 特别检查aux_id
            aux_id = node['properties'].get('aux_id', 'N/A')
            if aux_id != 'N/A':
                print(f"  发现aux_id: {aux_id}")
                # 检查格式是否符合要求
                if not re.match(r'^[^/]+/[^/]+$', aux_id):
                    print(f"  警告: aux_id格式不正确，应为'github-user/repo-name'")
        else:
            print("  没有properties字段")
        
        print()  # 空行分隔

def main():
    if len(sys.argv) < 2:
        print("用法: python check_aux_id.py <工作流文件路径>")
        return
    
    workflow_path = sys.argv[1]
    check_workflow(workflow_path)

if __name__ == "__main__":
    main() 