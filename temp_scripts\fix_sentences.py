#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的示例文件目录
EXAMPLES_DIR = "aidma_examples"

def fix_sentences(text):
    """修复句子结构问题"""
    
    # 修复"She is wearing and"
    text = text.replace("She is wearing and", "She is wearing")
    
    # 修复"The create a tantalizing contrast"
    text = text.replace("The create a tantalizing contrast against her skin, visible beneath the hem of her mini skirt.", "")
    
    # 处理多余的空格
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\s+\.', '.', text)
    
    return text

def update_file(file_path):
    """更新文件内容"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 修复内容
        updated_content = fix_sentences(content)
        
        # 如果内容有变化，则更新文件
        if updated_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            print(f"已修复文件: {file_path}")
            return True
        return False
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def fix_all_examples():
    """修复所有示例文件"""
    if os.path.exists(EXAMPLES_DIR):
        print(f"开始修复 {EXAMPLES_DIR} 目录中的文件...")
        
        fixed_files = []
        
        # 修复white_transparent_panties.txt文件
        file_path = os.path.join(EXAMPLES_DIR, "white_transparent_panties.txt")
        if os.path.exists(file_path):
            if update_file(file_path):
                fixed_files.append(os.path.basename(file_path))
        
        # 检查其他可能有问题的文件
        for file_name in os.listdir(EXAMPLES_DIR):
            if file_name.endswith(".txt") and file_name != "white_transparent_panties.txt":
                file_path = os.path.join(EXAMPLES_DIR, file_name)
                if update_file(file_path):
                    fixed_files.append(file_name)
        
        print(f"\n总共修复了 {len(fixed_files)} 个文件")
        if fixed_files:
            print("已修复的文件:")
            for file_name in fixed_files:
                print(f"- {file_name}")
    else:
        print(f"{EXAMPLES_DIR} 目录不存在")

if __name__ == "__main__":
    fix_all_examples() 