{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Analysis and Visualization\n", "\n", "Template for analyzing ComfyUI workflows, model performance, and data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette('husl')\n", "%matplotlib inline\n", "\n", "print(\"Data analysis libraries loaded\")"]}], "metadata": {"kernelspec": {"display_name": "ComfyUI Python", "language": "python", "name": "comfyui"}}, "nbformat": 4, "nbformat_minor": 4}