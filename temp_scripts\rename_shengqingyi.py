#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import shutil
from PIL import Image

# 设置路径
SOURCE_DIR = "output/沈清宜"
TARGET_DIR = "output/renamed_emotions"

def create_directory(directory):
    """创建目录，如果目录已存在则跳过"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def rename_images():
    """重命名图像文件，去掉日期和数字编号"""
    # 确保目标目录存在
    create_directory(TARGET_DIR)
    
    # 获取所有PNG图像
    png_files = []
    for file_name in os.listdir(SOURCE_DIR):
        if file_name.endswith(".png"):
            file_path = os.path.join(SOURCE_DIR, file_name)
            # 提取情感名称（第一个下划线前的部分）
            emotion = file_name.split("_")[0]
            png_files.append((file_name, file_path, emotion))
    
    # 重命名并复制文件
    copied_files = []
    for old_name, old_path, emotion in png_files:
        # 创建新文件名
        new_name = f"{emotion}.png"
        new_path = os.path.join(TARGET_DIR, new_name)
        
        # 复制文件
        try:
            shutil.copy2(old_path, new_path)
            copied_files.append((emotion, old_name, new_name))
            print(f"已复制并重命名: {old_name} → {new_name}")
        except Exception as e:
            print(f"复制文件时出错: {old_name} → {new_name}: {str(e)}")
    
    # 生成重命名报告
    print(f"\n重命名完成，共处理 {len(copied_files)} 个文件")
    print("\n对应关系:")
    for emotion, old_name, new_name in copied_files:
        print(f"  {emotion}: {old_name} → {new_name}")

def main():
    rename_images()

if __name__ == "__main__":
    main() 