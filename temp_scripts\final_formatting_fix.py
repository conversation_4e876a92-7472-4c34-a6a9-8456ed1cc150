#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

def final_fix(file_path):
    """最终修复文件中的格式问题"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 在后部描述前添加句点
        content = re.sub(r'(head to toe|entire figure|body language) (High|Ultra)', r'\1. \2', content)
        
        # 检查是否有变化
        if content == original_content:
            print(f"文件 {os.path.basename(file_path)} 没有变化")
            return False
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已修复文件: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        print(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
        return False

def fix_all_files():
    """修复所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(os.path.join(EMOTION_PROMPTS_DIR, file_name))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要检查\n")
    
    # 修复所有表情文件
    fixed_files = []
    
    for file_path in emotion_files:
        if final_fix(file_path):
            fixed_files.append(os.path.basename(file_path))
    
    print(f"\n总共修复了 {len(fixed_files)} 个文件")
    if fixed_files:
        print("已修复的文件:")
        for file_name in fixed_files:
            print(f"- {file_name}")

def check_all_files():
    """检查所有表情文件的内容"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append((file_name, os.path.join(EMOTION_PROMPTS_DIR, file_name)))
    
    # 排序
    emotion_files.sort()
    
    # 检查所有文件是否有剩余问题
    problem_files = []
    for file_name, file_path in emotion_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
            # 检查是否有常见格式问题
            if re.search(r'(head to toe|entire figure|body language) (High|Ultra)', content):
                problem_files.append(file_name)
                
        except Exception as e:
            print(f"读取文件 {file_name} 时出错: {str(e)}")
    
    if problem_files:
        print("\n以下文件可能仍有格式问题:")
        for file in problem_files:
            print(f"- {file}")
    else:
        print("\n所有文件格式问题已修复!")
        
    # 显示特定文件内容作为示例
    sample_files = ["anger.txt", "joy.txt", "desire.txt"]
    for file_name in sample_files:
        file_path = os.path.join(EMOTION_PROMPTS_DIR, file_name)
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"\n文件 {file_name} 的内容:")
            print("-" * 50)
            print(content)
            print("-" * 50)
        except Exception as e:
            print(f"读取文件 {file_name} 时出错: {str(e)}")

if __name__ == "__main__":
    fix_all_files()
    check_all_files()