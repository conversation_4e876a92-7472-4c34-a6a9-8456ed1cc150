import os
import re

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

# 需要检查和替换的摄像机/视角描述
camera_patterns = [
    r"Camera angle slightly from side capturing.*",
    r".*looking upward.*",
    r".*looking down.*",
    r".*away from the camera.*",
]

# 保证脸部朝向镜头的标准描述
face_to_camera_description = "Face directly towards camera with full eye contact."
camera_angle_description = "Straight-on camera angle capturing full facial details."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        updated_lines = []
        face_direction_added = False
        camera_angle_replaced = False
        
        # 处理每一行
        for line in lines:
            # 跳过任何不希望的摄像机角度或视线方向
            skip_line = False
            for pattern in camera_patterns:
                if re.match(pattern, line.strip()):
                    skip_line = True
                    break
            
            if skip_line:
                continue
                
            # 检测并替换摄像机角度行
            if ("camera angle" in line.lower() or "camera perspective" in line.lower()) and not camera_angle_replaced:
                updated_lines.append(camera_angle_description + "\n")
                camera_angle_replaced = True
            else:
                updated_lines.append(line)
        
        # 在眼睛描述后添加面部朝向描述（如果尚未添加）
        if not face_direction_added:
            new_lines = []
            for line in updated_lines:
                new_lines.append(line)
                if "eye" in line.lower() and "through lenses" in line.lower() and face_to_camera_description not in ''.join(updated_lines):
                    new_lines.append(face_to_camera_description + "\n")
                    face_direction_added = True
            updated_lines = new_lines
        
        # 确保有摄像机角度行
        if not camera_angle_replaced and camera_angle_description not in ''.join(updated_lines):
            # 在"Soft natural lighting"行后添加
            new_lines = []
            for line in updated_lines:
                new_lines.append(line)
                if "Soft natural lighting" in line and camera_angle_description not in ''.join(updated_lines):
                    new_lines.append(camera_angle_description + "\n")
            updated_lines = new_lines
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.writelines(updated_lines)
        
        print(f"Updated face direction in: {filename}")

print("All files have been updated to ensure face is directed towards camera.")