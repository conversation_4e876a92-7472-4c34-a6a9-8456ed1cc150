#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 定义表情文件目录
EMOTIONS_DIR = "emotion_prompts"

# 要排除的文件（特殊用途文件）
EXCLUDE_FILES = [
    "negative_prompt.txt", "trigger.txt", "skirt_trigger.txt", 
    "WHITE_UNDERWEAR.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", 
    "WIDE_LEG_POSE.txt", "EXTREME_SKIRT.txt", "EXTREME_NEGATIVE.txt",
    "COMBINED_SOLUTION.txt", "PLEATED_SKIRT_PREVIEW.md", "PLEATED_SKIRT_GUIDE.md",
    "FINAL_INSTRUCTIONS.md", "LEG_POSE_GUIDE.md", "FINAL_SOLUTION.md",
    "LIGHTING_GUIDE.md", "EXTREME_GUIDE.md", "README_USAGE.md", "README.md"
]

# 标准的服装描述
CLOTHING_DESCRIPTION = "Wearing a stylish deep V-neck top that dramatically plunges to reveal cleavage, paired with a glossy black ultra-short mini skirt that sits low on her hips, clearly exposing her bare midriff and navel, the skirt's hem barely covering the upper thighs."

# 标准的场景描述
SCENE_DESCRIPTION = "Standing elegantly in a clean minimalist interior with a pure white background, the soft diffused lighting creating a serene and sophisticated atmosphere."

# 标准的姿势描述
POSE_DESCRIPTION = "Posed to fully showcase her outfit, with no part of her body obscured or hidden from view."

# 标准的照明描述
LIGHTING_DESCRIPTION = "Soft, even studio lighting with subtle highlights creating gentle shadows that complement her features, the carefully balanced illumination enhancing the clean aesthetic"

# 标准的手部姿势描述
HAND_DESCRIPTION = "One hand confidently resting on her hip while the other hand gently touches her exposed midriff, drawing attention to her slender waist and bare navel."

def update_emotion_file(file_path):
    """更新单个表情文件，使用标准的服装和场景描述"""
    filename = os.path.basename(file_path)
    print(f"处理文件: {filename}")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换服装描述（包含Wearing开头的整行）
    content = re.sub(r'Wearing[^\n]+\n', CLOTHING_DESCRIPTION + '\n', content)
    
    # 替换场景描述（包含Standing开头的整行）
    content = re.sub(r'Standing[^\n]+\n', SCENE_DESCRIPTION + '\n', content)
    
    # 替换姿势描述（如果存在）
    if "Posed to" in content:
        content = re.sub(r'Posed[^\n]+\n', POSE_DESCRIPTION + '\n', content)
    else:
        # 如果不存在姿势描述，在场景描述后添加
        content = content.replace(SCENE_DESCRIPTION + '\n', SCENE_DESCRIPTION + '\n' + POSE_DESCRIPTION + '\n')
    
    # 替换照明描述（如果存在）
    lighting_pattern = r'(Soft[^\n]*lighting[^\n]*\n)'
    if re.search(lighting_pattern, content):
        content = re.sub(lighting_pattern, LIGHTING_DESCRIPTION + '\n', content)
    
    # 替换手部姿势描述（如果存在）
    hand_pattern = r'(One hand[^\n]*hip[^\n]*\n)'
    if re.search(hand_pattern, content):
        content = re.sub(hand_pattern, HAND_DESCRIPTION + '\n', content)
    
    # 删除可能存在的其他冲突描述
    # 删除任何包含leggings、underwear、panties等关键词的行
    content = re.sub(r'.*\b(leggings|stockings|pantyhose|tights|underwear|panties)\b.*\n', '', content)
    
    # 删除额外的空行，保留最多两个连续空行
    content = re.sub(r'\n{3,}', '\n\n', content)
    
    # 保存修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✓ 已更新 {filename} 的服装和场景描述")

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有txt文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 过滤掉需要排除的文件
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in EXCLUDE_FILES]
    
    print(f"准备更新 {len(emotion_files)} 个表情文件的服装和场景描述...")
    
    # 逐个处理文件
    for file_path in emotion_files:
        update_emotion_file(file_path)
    
    print(f"\n完成! 成功更新了 {len(emotion_files)} 个表情文件。")

if __name__ == "__main__":
    update_all_emotion_files()