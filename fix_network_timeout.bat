@echo off
chcp 65001 >nul
REM ============================================================================
REM ComfyUI Network Timeout Quick Fix
REM Fixes: OSError: [WinError 121] Semaphore timeout period has expired
REM ============================================================================

setlocal enabledelayedexpansion

echo ============================================================================
echo                ComfyUI Network Timeout Quick Fix
echo ============================================================================
echo.
echo [INFO] Fixing network timeout issues for multiple workflows
echo [INFO] Error: OSError: [WinError 121] Semaphore timeout period has expired
echo.

set "CONDA_ENV=comfyui"

REM Activate environment
echo [STEP 1] Activating Conda environment...
call conda activate %CONDA_ENV% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Cannot activate conda environment '%CONDA_ENV%'
    pause
    exit /b 1
)
echo [SUCCESS] Conda environment activated

REM Apply network optimizations
echo.
echo [STEP 2] Applying network optimizations...
python utils\network_optimizer.py --apply-optimizations
echo [SUCCESS] Network optimizations applied

REM Create network patch
echo.
echo [STEP 3] Creating network patch...
python utils\network_optimizer.py --create-patch
if exist network_patch.py (
    echo [SUCCESS] Network patch created
) else (
    echo [WARNING] Network patch creation failed
)

REM Test network patch
echo.
echo [STEP 4] Testing network patch...
python -c "import network_patch; print('[SUCCESS] Network patch test passed')" 2>nul
if errorlevel 1 (
    echo [WARNING] Network patch test failed, but may still work
) else (
    echo [SUCCESS] Network patch working correctly
)

echo.
echo ============================================================================
echo                           Fix Complete
echo ============================================================================
echo.
echo [SUCCESS] Network timeout fix completed!
echo.
echo [NEXT STEPS] What to do next:
echo.
echo 1. Use multi-workflow optimized startup:
echo    start_comfyui_multiworkflow.bat
echo.
echo 2. Or start ComfyUI normally:
echo    start_comfyui.bat
echo.
echo 3. If issues persist, check troubleshooting guide:
echo    NETWORK_TIMEOUT_TROUBLESHOOTING.md
echo.
echo [TIPS] Multi-workflow best practices:
echo - Limit concurrent workflows to 3-4 maximum
echo - Choose Conservative Mode for maximum stability
echo - Refresh browser if timeout occurs
echo.

pause
