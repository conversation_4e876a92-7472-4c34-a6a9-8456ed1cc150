{"console_log_level": null, "console_log_file": null, "console_log_simple": false, "v2": false, "v_parameterization": false, "pretrained_model_name_or_path": "E:\\ComfyUI\\models\\unet\\flux1-dev-fp8.safetensors", "tokenizer_cache_dir": null, "train_data_dir": null, "cache_info": false, "shuffle_caption": false, "caption_separator": ",", "caption_extension": ".caption", "caption_extention": null, "keep_tokens": 0, "keep_tokens_separator": "", "secondary_separator": null, "enable_wildcard": false, "caption_prefix": null, "caption_suffix": null, "color_aug": false, "flip_aug": false, "face_crop_aug_range": null, "random_crop": false, "debug_dataset": false, "resolution": null, "cache_latents": true, "vae_batch_size": 1, "cache_latents_to_disk": true, "skip_cache_check": false, "enable_bucket": false, "min_bucket_reso": 256, "max_bucket_reso": 1024, "bucket_reso_steps": 64, "bucket_no_upscale": false, "token_warmup_min": 1, "token_warmup_step": 0.0, "alpha_mask": false, "dataset_class": null, "caption_dropout_rate": 0.0, "caption_dropout_every_n_epochs": 0, "caption_tag_dropout_rate": 0.0, "reg_data_dir": null, "in_json": null, "dataset_repeats": 1, "output_dir": "flux_lora_output_path", "output_name": "flux_lora_file_name_rank64_fp16", "huggingface_repo_id": null, "huggingface_repo_type": null, "huggingface_path_in_repo": null, "huggingface_token": null, "huggingface_repo_visibility": null, "save_state_to_huggingface": false, "resume_from_huggingface": false, "async_upload": false, "save_precision": "fp16", "save_every_n_epochs": null, "save_every_n_steps": null, "save_n_epoch_ratio": null, "save_last_n_epochs": null, "save_last_n_epochs_state": null, "save_last_n_steps": null, "save_last_n_steps_state": null, "save_state": false, "save_state_on_train_end": false, "resume": null, "train_batch_size": 1, "max_token_length": null, "mem_eff_attn": true, "torch_compile": false, "dynamo_backend": "inductor", "xformers": false, "sdpa": false, "vae": null, "max_train_steps": 3000, "max_train_epochs": null, "max_data_loader_n_workers": 0, "persistent_data_loader_workers": false, "seed": 42, "gradient_checkpointing": true, "gradient_accumulation_steps": 1, "mixed_precision": "fp16", "full_fp16": true, "full_bf16": false, "fp8_base": true, "fp8_dtype": "e4m3", "ddp_timeout": null, "ddp_gradient_as_bucket_view": false, "ddp_static_graph": false, "clip_skip": null, "logging_dir": null, "log_with": null, "log_prefix": null, "log_tracker_name": null, "wandb_run_name": null, "log_tracker_config": null, "wandb_api_key": null, "log_config": false, "noise_offset": null, "noise_offset_random_strength": false, "multires_noise_iterations": null, "ip_noise_gamma": null, "ip_noise_gamma_random_strength": false, "multires_noise_discount": 0.3, "adaptive_noise_scale": null, "zero_terminal_snr": false, "min_timestep": null, "max_timestep": null, "loss_type": "l2", "huber_schedule": "snr", "huber_c": 0.1, "huber_scale": 1.0, "lowram": false, "highvram": false, "sample_every_n_steps": null, "sample_at_first": false, "sample_every_n_epochs": null, "sample_prompts": ["cute anime girl blonde messy long hair blue eyes wearing a maid outfit with a long black dress with a gold leaf pattern and a white apron in an old dark victorian mansion with a bright window and very expensive stuff everywhere a<PERSON><PERSON><PERSON><PERSON>", "illustration of a kitten a<PERSON><PERSON><PERSON><PERSON>", "photograph of a turtle a<PERSON><PERSON><PERSON><PERSON>", "portrait of a female red wizard <PERSON><PERSON><PERSON><PERSON><PERSON>"], "sample_sampler": "ddim", "config_file": null, "output_config": false, "metadata_title": null, "metadata_author": null, "metadata_description": null, "metadata_license": null, "metadata_tags": null, "prior_loss_weight": 1.0, "conditioning_data_dir": null, "masked_loss": false, "deepspeed": false, "zero_stage": 2, "offload_optimizer_device": null, "offload_optimizer_nvme_path": null, "offload_param_device": null, "offload_param_nvme_path": null, "zero3_init_flag": false, "zero3_save_16bit_model": false, "fp16_master_weights_and_gradients": false, "optimizer_type": "CAME", "use_8bit_adam": false, "use_lion_optimizer": false, "learning_rate": 0.0004, "max_grad_norm": 1.0, "optimizer_args": [], "lr_scheduler_type": "", "lr_scheduler_args": null, "lr_scheduler": "constant", "lr_warmup_steps": 0, "lr_decay_steps": 0, "lr_scheduler_num_cycles": 1, "lr_scheduler_power": 1.0, "fused_backward_pass": false, "lr_scheduler_timescale": null, "lr_scheduler_min_lr_ratio": null, "dataset_config": "[[datasets]]\nresolution = [ 512, 512,]\nbatch_size = 1\nenable_bucket = true\nbucket_no_upscale = false\nmin_bucket_reso = 256\nmax_bucket_reso = 1024\n[[datasets.subsets]]\nimage_dir = \"E:\\\\env\\\\wateremove\\\\LYT\\\\lora-lytong\"\nclass_tokens = \"lingyitong\"\nnum_repeats = 1\n\n\n[general]\nshuffle_caption = false\ncaption_extension = \".txt\"\nkeep_tokens_separator = \"|||\"\ncaption_dropout_rate = 0.0\ncolor_aug = false\nflip_aug = false\n", "cache_text_encoder_outputs": true, "cache_text_encoder_outputs_to_disk": true, "text_encoder_batch_size": null, "disable_mmap_load_safetensors": false, "weighting_scheme": "logit_normal", "logit_mean": 0.0, "logit_std": 1.0, "mode_scale": 1.29, "blocks_to_swap": 0, "min_snr_gamma": 5.0, "scale_v_pred_loss_like_noise_pred": false, "v_pred_like_loss": null, "debiased_estimation_loss": false, "weighted_captions": false, "cpu_offload_checkpointing": false, "no_metadata": false, "save_model_as": "safetensors", "unet_lr": null, "text_encoder_lr": null, "fp8_base_unet": false, "network_weights": null, "network_module": ".networks.lora_flux", "network_dim": 64, "network_alpha": 64.00000000000001, "network_dropout": null, "network_args": null, "network_train_unet_only": true, "network_train_text_encoder_only": false, "training_comment": null, "dim_from_weights": false, "scale_weight_norms": null, "base_weights": null, "base_weights_multiplier": null, "no_half_vae": false, "skip_until_initial_step": false, "initial_epoch": null, "initial_step": null, "num_cpu_threads_per_process": 1, "clip_l": "E:\\ComfyUI\\models\\clip\\clip_l.safetensors", "t5xxl": "E:\\ComfyUI\\models\\clip\\t5xxl_fp8_e4m3fn.safetensors", "ae": "E:\\ComfyUI\\models\\vae\\ae.sft", "t5xxl_max_token_length": 512, "spda": true, "apply_t5_attn_mask": true, "timestep_sampling": "shift", "sigmoid_scale": 1.0, "model_prediction_type": "raw", "guidance_scale": 1.0, "discrete_flow_shift": 3.1582000000000003}