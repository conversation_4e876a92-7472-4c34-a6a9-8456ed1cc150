import os
import re
import glob

# 获取emotion_prompts文件夹中的所有txt文件
prompt_files = glob.glob('emotion_prompts/*.txt')

# 定义要排除的文件
exclude_files = ['README.md', 'admiration_glasses.txt', 'negative_prompt.txt', 'README_USAGE.md']

# 定义旧的裙子描述模式
old_skirt_pattern = r"Micro-length black mini skirt that could be mistaken for a wide belt, barely reaching below her hipline"

# 新的更加详细的描述，特别强调"黑色迷你裙"和"分离的内裤"
new_skirt_pattern = r"ULTRA-SHORT shiny BLACK MINI SKIRT (NOT PANTS, NOT LEGGINGS), absurdly tiny and resembling a wide belt, barely covering her buttocks with clearly defined bottom hem, completely separate from her undergarments beneath, with obvious visible gap between the skirt's lower edge and her separate silk panties"

# 初始化计数器
updated_count = 0
skipped_count = 0

# 处理每个文件
for file_path in prompt_files:
    # 检查是否为排除文件
    if os.path.basename(file_path) in exclude_files:
        print(f"跳过文件: {file_path}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 检查是否包含旧的裙子描述
        if old_skirt_pattern in content:
            # 替换为新的描述
            content = content.replace(old_skirt_pattern, new_skirt_pattern)
            
            # 在文件开头添加特定关键词（针对裙子而非裤子）
            if not content.startswith("MINISKIRT NOT PANTS,"):
                lines = content.split('\n')
                if len(lines) > 0 and "NSFW" in lines[0]:
                    lines[0] = lines[0].replace("NSFW", "NSFW, MINISKIRT NOT PANTS, black_mini_skirt separate_underwear,")
                    content = '\n'.join(lines)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"已更新裙子描述: {file_path}")
            updated_count += 1
        else:
            print(f"文件不包含目标描述: {file_path}")
            skipped_count += 1
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        skipped_count += 1

# 更新负面提示词
try:
    with open('emotion_prompts/negative_prompt.txt', 'r', encoding='utf-8') as file:
        negative_content = file.read()
    
    # 增强负面提示词
    enhanced_negative = negative_content.strip() + """,
pants confusion, clothing confusion, outfit ambiguity, garment fusion,
black pants, black slacks, black jeans, black leggings, black trousers,
united clothing items, connected garments, seamless outfit,
continuous fabric, single piece lower garment, monopiece bottom,
closed legs, legs together, thighs touching
"""
    
    # 写回文件
    with open('emotion_prompts/negative_prompt.txt', 'w', encoding='utf-8') as file:
        file.write(enhanced_negative)
    
    print("已更新负面提示词文件")
    updated_count += 1
except Exception as e:
    print(f"更新负面提示词时出错: {str(e)}")
    skipped_count += 1

# 创建特殊的触发器文件，用于最强的裙子描述
try:
    trigger_content = """NSFW, MINISKIRT NOT PANTS, black_mini_skirt separate_underwear, Beautiful woman showing desire with seductive gaze.

Full body shot showing entire figure from head to toe, legs spread apart showing distinct separation between garments.
Face directly towards camera with full eye contact.
Slightly parted lips with hint of smile.
Form-fitting black off-shoulder crop top with ultra-deep V-neck plunging between her impressive breasts, long sleeves covering arms but leaving shoulders completely bare, the smooth stretchy fabric emphasizing her voluptuous chest while exposing her slender waist and navel, tight enough to outline every curve of her upper body.
ULTRA-SHORT shiny BLACK MINI SKIRT (NOT PANTS, NOT LEGGINGS), absurdly tiny and resembling a wide belt, barely covering her buttocks with clearly defined bottom hem, completely separate from her undergarments beneath, with obvious visible gap between the skirt's lower edge and her separate silk panties, riding up to dangerous heights, barely preserving modesty with only the slightest hint of delicate fabric between skin and air, her light-colored intimate wear contrasting with shadow and revealing far more than it conceals, the fabric so minimal it barely constitutes clothing, constantly shifting with her slightest movement to expose what should remain hidden.
Sitting elegantly with legs slightly parted on a luxurious white bed with fluffy pillows and pristine sheets, the soft bedding creating a perfect contrast with her black outfit and pale skin.
Posed to fully showcase her outfit, with no part of her body obscured or hidden from view.

Long straight black hair with neat bangs across forehead.
Both arms extended outward and away from her body with palms facing upward in an open welcoming gesture, ensuring every inch of her outfit remains fully visible with absolutely nothing obstructing the view of her chest or lower body, her pose deliberately arranged to showcase the clear distinction between her micro-skirt and the barely-there undergarment beneath.
Soft natural lighting in bright minimalist interior setting.
show_intimate_details underwear_visible exposed separate_garments miniskirt_not_pants distinct_underwear_visible hands_away_from_body unobstructed_view mini_skirt two_piece_outfit distinct_garments black_skirt_white_panties panties_visible_under_skirt
Half-lidded eyes looking directly at viewer with intense focus.
Straight-on camera angle capturing both the micro mini skirt and separate underwear beneath.
"""
    
    with open('emotion_prompts/skirt_trigger.txt', 'w', encoding='utf-8') as file:
        file.write(trigger_content)
    
    print("已创建特殊裙子触发器文件")
    updated_count += 1
except Exception as e:
    print(f"创建特殊触发器文件时出错: {str(e)}")
    skipped_count += 1

# 更新README文件中的使用说明
try:
    with open('emotion_prompts/README_USAGE.md', 'r', encoding='utf-8') as file:
        readme_content = file.read()
    
    # 添加新的使用说明
    enhanced_readme = readme_content + """

## 特殊触发文件
`skirt_trigger.txt` 文件包含了最强力的迷你裙描述，特别用于解决黑色裙子被误认为裤子的问题：

1. 该文件包含了特殊格式化的关键词和描述，使AI更容易理解迷你裙与内裤的区别
2. 使用时请直接复制整个文件内容作为正面提示词
3. 与负面提示词(`negative_prompt.txt`)配合使用效果最佳
4. 特别提高了CFG值到9或更高可获得更好的效果

## 极端情况解决方案
如果依然出现裙子被识别为裤子的问题：

1. 将CFG值提高到10-12（注意可能产生过度锐化效果）
2. 使用更明确的提示词："extremely_short_black_miniskirt separate_white_panties"
3. 使用`skirt_trigger.txt`中的内容作为基础，并强调模型注意裙子的形状和内裤的可见性
4. 尝试在提示词中添加："legs_apart showing_separate_garments"
5. 考虑使用不同的模型或采样器，某些模型在识别服装细节方面表现更好
"""
    
    # 写回文件
    with open('emotion_prompts/README_USAGE.md', 'w', encoding='utf-8') as file:
        file.write(enhanced_readme)
    
    print("已更新README_USAGE.md文件")
    updated_count += 1
except Exception as e:
    print(f"更新README文件时出错: {str(e)}")
    skipped_count += 1

print(f"\n增强完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。")
print("请使用新的skirt_trigger.txt文件作为提示词，并将CFG值设置为9-12以确保生成效果。") 