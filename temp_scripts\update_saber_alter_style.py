#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_description(text):
    """更新描述，保留名字、表情和aidmaNSFWunlock，更新服装和姿势"""
    
    # 保留原始的名字和表情描述
    name_match = re.search(r'([a-zA-Z\s]+) with ([a-zA-Z\s]+) (expression|face)', text)
    emotion_match = re.search(r'expressing ([a-zA-Z\s,]+)', text)
    
    # 新的服装描述
    new_outfit = "cutoff jeans, see-through white top, revealing her huge breasts"
    new_body = "petite body, perky boobs, slender, slender_waist"
    new_face = "perfect face, exquisite eyes, luscious lips, blonde_hair, yellow_eyes"
    new_pose = "standing, looking_up, mouth slightly open, face in closeup, seductive look, 3/4 angle view"
    new_setting = "Twilight Ridge, wellhead pumper themed setting, detailed and intricate environment"
    new_lighting = "Ethereal Lighting"
    
    # 添加新的角色描述
    character_desc = "saber_alter, fate_(series)"
    
    # 更新场景和姿势描述
    text = re.sub(r'Standing (elegantly|gracefully) against[^\.]+\.', 
                f'Standing at {new_setting}.', text)
    
    # 更新服装描述（替换整个服装部分）
    text = re.sub(r'She (wears|is wearing) [^\.]+\.', 
                f'She is wearing {new_outfit}.', text)
    
    # 更新身体描述
    if "slender body" not in text:
        text = re.sub(r'(young woman|gravure idol)[^\.]+(\.)', 
                    f'beautiful and youthful Japanese model, 19 years old, with {new_body} and {new_face}\\2', text)
    
    # 更新手势描述
    text = re.sub(r'(H|h)er hands[^\.]+\.', 
                f'Her hands hidden, with jewelry adorning her delicate wrists.', text)
    
    # 更新姿势描述
    pose_pattern = r'(H|h)er (confident|elegant|graceful) pose[^\.]+\.'
    if re.search(pose_pattern, text):
        text = re.sub(pose_pattern, 
                    f'Her {new_pose}, creating a captivating portrait.', text)
    else:
        # 如果找不到现有的姿势描述，添加一个新的
        text = re.sub(r'(\. The scene captures)', 
                    f'. Her {new_pose}\\1', text)
    
    # 更新照明描述
    text = re.sub(r'(S|s)oft, (ambient|warm) lighting[^\.]+\.', 
                f'{new_lighting} bathes the scene, creating a mysterious atmosphere.', text)
    
    # 添加角色描述，如果不存在
    if character_desc not in text and "aidmaNSFWunlock" in text:
        text = text.replace("aidmaNSFWunlock", f"{character_desc}, aidmaNSFWunlock")
    
    # 添加关键词（如果不存在）
    keywords = ["Ultra high quality", "photo", "masterpiece", "ultra high resolution", 
                "realistic", "photorealistic", "DSLR photo", "three quarter view", 
                "face focus", "tightly cropped composition"]
    
    for keyword in keywords:
        if keyword.lower() not in text.lower() and "aidmaNSFWunlock" in text:
            text = text.replace("aidmaNSFWunlock", f"{keyword}, aidmaNSFWunlock")
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_description(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_saber_alter_example():
    """创建Saber Alter风格的示例文件"""
    example_content = """Ultra high quality, photo, masterpiece, ultra high resolution, realistic, photorealistic, beautiful and youthful Japanese model, 19 years old, with petite body, perky boobs, slender, slender_waist and perfect face, exquisite eyes, luscious lips, blonde_hair, yellow_eyes. Standing at Twilight Ridge, wellhead pumper themed setting, detailed and intricate environment. She is wearing cutoff jeans, see-through white top, revealing her huge breasts. Her hands hidden, with jewelry adorning her delicate wrists. Her standing, looking_up, mouth slightly open, face in closeup, seductive look, 3/4 angle view, creating a captivating portrait. Ethereal Lighting bathes the scene, creating a mysterious atmosphere. The scene captures a perfect balance between fashionable style and alluring beauty, with tightly cropped composition emphasizing her face and upper body. saber_alter, fate_(series), DSLR photo, three quarter view, face focus, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    example_path = os.path.join(EXAMPLES_DIR, "saber_alter_style.txt")
    with open(example_path, 'w', encoding='utf-8') as file:
        file.write(example_content)
    print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_saber_alter_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已更新到新的描述
            content = update_description(content)
            
            with open(joy_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 