# ComfyUI Jupyter Lab

## 🚀 快速开始

### 启动Jupyter Lab
```bash
.\start_jupyter_lab.bat
```

### 访问地址
- 本地访问：http://localhost:8888
- 网络访问：http://YOUR_IP:8888
- 中文界面：http://localhost:8888/lab?locale=zh_CN

## 📦 已安装组件

### 核心环境
- **Jupyter Lab 4.5.0** - 主要开发环境
- **IPython Kernel** - ComfyUI Python内核
- **中文语言包** - 简体中文界面支持

### 开发工具
- **jupyterlab-git** - Git版本控制
- **jupyterlab-lsp** - 智能代码补全
- **jupyterlab-code-formatter** - 代码格式化
- **Black & isort** - Python格式化器

### 数据科学库
- **matplotlib, seaborn, plotly** - 数据可视化
- **pandas, numpy, scipy** - 数据处理
- **scikit-learn** - 机器学习
- **opencv-python** - 计算机视觉

## 📁 预创建模板

### 1. ComfyUI开发模板
**文件**: `notebooks/ComfyUI_Development_Template.ipynb`
- ComfyUI环境检查
- GPU状态监控
- 模块导入示例

### 2. 数据分析模板
**文件**: `notebooks/Data_Analysis_Template.ipynb`
- 数据处理工具
- 可视化配置

### 3. 模型测试模板
**文件**: `notebooks/Model_Testing_Template.ipynb`
- 性能基准测试
- 系统监控

## 💡 使用示例

### ComfyUI集成开发
```python
import sys
import os
import torch

# 添加ComfyUI到路径
sys.path.append('..')

# 导入ComfyUI模块
import comfy.model_management as model_management
import comfy.utils

# 检查GPU状态
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"ComfyUI device: {model_management.get_torch_device()}")
```

### 数据可视化
```python
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# 设置样式
plt.style.use('default')
sns.set_palette('husl')
%matplotlib inline

# 创建图表
data = pd.DataFrame({'x': range(10), 'y': range(10)})
plt.plot(data['x'], data['y'])
plt.show()
```

## 🔧 配置信息

### 网络设置
- **端口**: 8888（自动检测冲突）
- **访问**: 允许网络连接
- **安全**: 本地开发优化

### 工作目录
- **默认**: ComfyUI根目录
- **notebooks**: 专用工作区

## 🛠️ 故障排除

### 启动失败
```bash
conda activate comfyui
pip install --upgrade jupyterlab
```

### 内核连接问题
```bash
conda activate comfyui
python -m ipykernel install --user --name comfyui --display-name "ComfyUI Python"
```

### 端口冲突
```bash
jupyter lab --port=8889
```

## 🌐 中文界面支持

### 启用中文界面
- **自动中文**：配置文件已设置默认中文
- **手动切换**：访问 http://localhost:8888/lab?locale=zh_CN
- **界面设置**：Settings → Language → 中文(简体)

详细说明请参考：[JUPYTER_CHINESE_SETUP.md](JUPYTER_CHINESE_SETUP.md)

## 📚 主要功能

- ✅ ComfyUI模块直接访问
- ✅ GPU/VRAM状态监控
- ✅ 智能代码补全
- ✅ 实时错误检查
- ✅ Git版本控制集成
- ✅ 交互式数据可视化
- ✅ 代码格式化工具
- ✅ 中文界面支持

---

**提示**: 运行 `.\start_jupyter_lab.bat` 即可开始使用中文界面的Jupyter Lab！
