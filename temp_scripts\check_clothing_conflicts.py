#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 定义表情文件目录
EMOTIONS_DIR = "emotion_prompts"

# 要查找的服装关键词
CLOTHING_KEYWORDS = {
    "上衣": ["deep V-neck top", "V-neck", "top"],
    "裙子": ["mini skirt", "miniskirt", "ultra-short mini skirt", "skirt", "black latex", "pleated"],
    "短裤": ["shorts", "underwear", "panties", "white underwear", "white silky"],
    "丝袜": ["stockings", "pantyhose", "tights", "leggings"]
}

def check_file_conflicts(filename):
    """检查单个文件中的服装描述冲突"""
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 服装检测结果
    detected_clothing = {category: [] for category in CLOTHING_KEYWORDS}
    
    # 针对每个类别进行检查
    for category, keywords in CLOTHING_KEYWORDS.items():
        for keyword in keywords:
            # 区分大小写查找
            if keyword.lower() in content.lower():
                detected_clothing[category].append(keyword)
    
    # 报告发现的服装描述
    conflicts = []
    print(f"\n检查文件: {os.path.basename(filename)}")
    for category, items in detected_clothing.items():
        if items:
            print(f"  - 发现{category}: {', '.join(items)}")
            
    # 检查潜在冲突
    if detected_clothing["裙子"] and detected_clothing["短裤"]:
        conflicts.append(f"冲突: 同时存在裙子({', '.join(detected_clothing['裙子'])})和短裤/内裤({', '.join(detected_clothing['短裤'])})")
    
    # 检查特定颜色冲突
    white_top = any("white" in item.lower() for item in detected_clothing["上衣"])
    black_skirt = any("black" in item.lower() for item in detected_clothing["裙子"])
    white_shorts = any("white" in item.lower() for item in detected_clothing["短裤"])
    
    if white_top and not any("white" in item.lower() for item in detected_clothing["上衣"] if "deep V" in item.lower()):
        conflicts.append("颜色冲突: 上衣描述为白色，但深V领描述不包含白色")
    
    if black_skirt and not any("black" in item.lower() for item in detected_clothing["裙子"] if "mini" in item.lower()):
        conflicts.append("颜色冲突: 迷你裙描述为黑色，但裙子描述不包含黑色")
    
    # 检查白色短裤与丝袜的冲突
    if white_shorts and detected_clothing["丝袜"]:
        conflicts.append(f"冲突: 同时存在白色短裤/内裤和丝袜({', '.join(detected_clothing['丝袜'])})")
    
    # 输出冲突
    if conflicts:
        print("  ⚠️ 发现冲突:")
        for conflict in conflicts:
            print(f"    * {conflict}")
    else:
        print("  ✓ 未发现明显冲突")
    
    return conflicts

def extract_clothing_description(filename):
    """提取文件中的服装描述段落"""
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式查找服装描述段落
    clothing_pattern = r"Wearing.+?\.(?:\n|$)"
    match = re.search(clothing_pattern, content, re.DOTALL)
    if match:
        return match.group(0).strip()
    return "未找到服装描述"

def analyze_all_files():
    """分析所有表情文件"""
    # 获取所有表情文件
    emotion_files = glob.glob(os.path.join(EMOTIONS_DIR, "*.txt"))
    
    # 排除一些特殊文件
    excluded_files = ["negative_prompt.txt", "trigger.txt", "skirt_trigger.txt", 
                      "WHITE_UNDERWEAR.txt", "ULTIMATE_WHITE_UNDERWEAR.txt", 
                      "WIDE_LEG_POSE.txt", "EXTREME_SKIRT.txt"]
    
    emotion_files = [f for f in emotion_files if os.path.basename(f) not in excluded_files]
    
    print(f"开始检查 {len(emotion_files)} 个表情文件的服装描述冲突...")
    
    # 提取每个文件的服装描述
    clothing_descriptions = {}
    for file in emotion_files:
        clothing_descriptions[os.path.basename(file)] = extract_clothing_description(file)
    
    # 检查是否所有文件都有相同的服装描述
    descriptions = list(clothing_descriptions.values())
    consistent = all(desc == descriptions[0] for desc in descriptions)
    
    if consistent:
        print("\n✓ 所有表情文件使用了相同的服装描述:")
        print(f"\n{descriptions[0]}\n")
    else:
        print("\n⚠️ 发现不同的服装描述:")
        for filename, desc in clothing_descriptions.items():
            if desc != descriptions[0]:
                print(f"\n文件: {filename}")
                print(f"描述: {desc}")
    
    # 检查每个文件的潜在冲突
    all_conflicts = {}
    for file in emotion_files:
        conflicts = check_file_conflicts(file)
        if conflicts:
            all_conflicts[os.path.basename(file)] = conflicts
    
    # 显示汇总报告
    print("\n===== 冲突检查汇总 =====")
    if all_conflicts:
        print(f"在 {len(all_conflicts)} 个文件中发现服装描述冲突:")
        for filename, conflicts in all_conflicts.items():
            print(f"\n{filename}:")
            for conflict in conflicts:
                print(f"  - {conflict}")
    else:
        print("✓ 未在任何表情文件中发现服装描述冲突")
    
    print("\n===== 建议 =====")
    print("1. 确保所有表情文件使用一致的服装描述")
    print("2. 明确区分裙子和短裤/内裤的描述")
    print("3. 如果想要表达丝袜，确保与其他服装描述不冲突")
    print("4. 保持服装颜色描述的一致性")

if __name__ == "__main__":
    analyze_all_files() 