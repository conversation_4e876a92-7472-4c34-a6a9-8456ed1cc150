#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

# 表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"

# 新的角色名字
NEW_CHARACTER_NAME = "qingyi"

# 新的服装描述（确保是英文）
NEW_OUTFIT_DESCRIPTION = "wearing a white halter bikini top and ultra-short denim shorts with zipper partially undone"

def update_emotion_file(file_path):
    """更新表情文件，修改角色名字和服装描述"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        original_content = content
        
        # 替换角色名
        content = re.sub(r'^jingyan,', f"{NEW_CHARACTER_NAME},", content)
        
        # 找到并替换服装描述
        outfit_pattern = r'wearing[^\.]*\.'  # 匹配以"wearing"开头直到下一个句点的所有内容
        match = re.search(outfit_pattern, content)
        
        if match:
            old_outfit = match.group(0)
            content = content.replace(old_outfit, f"{NEW_OUTFIT_DESCRIPTION}.")
        
        # 清理连续的空格
        content = re.sub(r'\s+', ' ', content)
        
        # 检查是否有变化
        if content == original_content:
            print(f"文件 {os.path.basename(file_path)} 没有变化")
            return False
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        
        print(f"已更新文件: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        print(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
        return False

def update_all_emotion_files():
    """更新所有表情文件"""
    # 获取所有表情文件
    emotion_files = []
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name != "negative_prompt.txt" and file_name != "README.md":
            emotion_files.append(os.path.join(EMOTION_PROMPTS_DIR, file_name))
    
    print(f"找到 {len(emotion_files)} 个表情文件需要更新\n")
    
    # 更新所有表情文件
    updated_files = []
    
    for file_path in emotion_files:
        if update_emotion_file(file_path):
            updated_files.append(os.path.basename(file_path))
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

def check_updated_file(file_path):
    """检查更新后的文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        print(f"\n文件 {os.path.basename(file_path)} 的更新后内容:")
        print("-" * 50)
        print(content)
        print("-" * 50)
    except Exception as e:
        print(f"读取文件 {os.path.basename(file_path)} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_emotion_files()
    
    # 检查一个文件的更新结果作为示例
    example_file = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(example_file):
        check_updated_file(example_file)