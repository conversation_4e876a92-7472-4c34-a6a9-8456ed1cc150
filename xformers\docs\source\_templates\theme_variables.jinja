# Copyright (c) Facebook, Inc. and its affiliates. All rights reserved.
#
# This source code is licensed under the BSD license found in the
# LICENSE file in the root directory of this source tree.


{%-
set external_urls = {
  'github': 'https://github.com/facebookresearch/xformers',
  'github_issues': 'https://github.com/facebookresearch/xformers/issues',
  'contributing': 'https://github.com/facebookresearch/xformers/blob/master/CONTRIBUTING.md',
  'docs': 'https://github.com/facebookresearch/xformers',
  'home': 'https://github.com/facebookresearch/xformers',
  'get_started': 'https://github.com/facebookresearch/xformers/blob/master/README.md',
  'brand_guidelines': 'https://pytorch.org/assets/brand-guidelines/PyTorch-Brand-Guidelines.pdf'
}
-%}
{%-
set og = {
  'description': 'API docs for xFormers. xFormers is a PyTorch extension library for composable and optimized Transformer blocks.'
}
-%}
