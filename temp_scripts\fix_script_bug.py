import os
import re
import glob

# 定义要排除的文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 定义要替换的文本模式 - 手部姿势
old_hand_pattern = r"Both hands placed elegantly at her sides or slightly behind her on the bed, ensuring nothing obstructs the view of her outfit, deliberately keeping her chest and lower body completely visible."
new_hand_pattern = r"Both arms extended outward and away from her body with palms facing upward in an open welcoming gesture, ensuring every inch of her outfit remains fully visible with absolutely nothing obstructing the view of her chest or lower body, her pose deliberately arranged to showcase the clear distinction between her micro-skirt and the barely-there undergarment beneath."

# 添加裙子与内裤区分的明确描述
old_skirt_pattern = r"the tight fabric hugging her curves perfectly while creating a tantalizing high-cut silhouette that emphasizes her long slender legs and shapely hips"
new_skirt_pattern = r"the tight fabric hugging her curves perfectly while creating a tantalizing high-cut silhouette that emphasizes her long slender legs and shapely hips, the skirt's hemline creating a deliberate and obvious gap above where her separate undergarment begins"

# 获取emotion_prompts文件夹中的所有txt文件
prompt_files = glob.glob('emotion_prompts/*.txt')

# 初始化计数器
updated_count = 0
skipped_count = 0

# 处理每个文件
for file_path in prompt_files:
    # 检查是否为排除文件
    if os.path.basename(file_path) in exclude_files:
        print(f"跳过文件: {file_path}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 记录是否进行了更改
        changes_made = False
        
        # 替换手部描述
        if old_hand_pattern in content:
            content = content.replace(old_hand_pattern, new_hand_pattern)
            print(f"更新手部姿势: {file_path}")
            changes_made = True
        
        # 替换裙子与内裤区分描述
        if old_skirt_pattern in content:
            content = content.replace(old_skirt_pattern, new_skirt_pattern)
            print(f"更新裙子描述: {file_path}")
            changes_made = True
        
        # 只有在进行了更改时才写回文件
        if changes_made:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"已成功更新文件: {file_path}")
            updated_count += 1
        else:
            print(f"文件无需更新: {file_path}")
            skipped_count += 1
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        skipped_count += 1

# 更新触发文件
try:
    # 更新特殊的触发文件
    with open('emotion_prompts/trigger.txt', 'r', encoding='utf-8') as file:
        trigger_content = file.read()
    
    # 记录是否进行了更改
    trigger_changes_made = False
    
    # 替换手部描述
    if old_hand_pattern in trigger_content:
        trigger_content = trigger_content.replace(old_hand_pattern, new_hand_pattern)
        trigger_changes_made = True
    
    # 替换裙子与内裤区分描述
    if old_skirt_pattern in trigger_content:
        trigger_content = trigger_content.replace(old_skirt_pattern, new_skirt_pattern)
        trigger_changes_made = True
    
    # 确保特定关键词存在
    if "show_intimate_details underwear_visible exposed" in trigger_content:
        # 增加额外的关键词
        new_keywords = "show_intimate_details underwear_visible exposed separate_garments miniskirt_not_pants distinct_underwear_visible hands_away_from_body unobstructed_view"
        trigger_content = trigger_content.replace(
            "show_intimate_details underwear_visible exposed", 
            new_keywords
        )
        trigger_changes_made = True
    
    # 只有在进行了更改时才写回文件
    if trigger_changes_made:
        # 写回触发文件
        with open('emotion_prompts/trigger.txt', 'w', encoding='utf-8') as file:
            file.write(trigger_content)
        
        print(f"已更新触发文件: emotion_prompts/trigger.txt")
        updated_count += 1
    else:
        print(f"触发文件无需更新")
except Exception as e:
    print(f"更新触发文件时出错: {str(e)}")
    skipped_count += 1

print(f"\n更新完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。") 