import os
import re
import glob

# 定义要排除的文件
exclude_files = ['README.md', 'README_USAGE.md', 'negative_prompt.txt', 'EXTREME_GUIDE.md', 
                 'EXTREME_NEGATIVE.txt', 'LIGHTING_GUIDE.md', 'FINAL_SOLUTION.md',
                 'WIDE_LEG_POSE.txt', 'ULTIMATE_WHITE_UNDERWEAR.txt', 'EXTREME_SKIRT.txt',
                 'skirt_trigger.txt', 'WHITE_UNDERWEAR.txt', 'LEG_POSE_GUIDE.md',
                 'COMBINED_SOLUTION.txt', 'FINAL_INSTRUCTIONS.md', 'trigger.txt']

# 当前的迷你裙描述
old_skirt_pattern = r"ULTRA-SHORT shiny BLACK MINI SKIRT \(NOT PANTS, NOT LEGGINGS\), absurdly tiny and resembling a wide belt, barely covering her buttocks with clearly defined bottom hem, completely separate from her undergarments beneath, with obvious visible gap between the skirt's lower edge and her separate silk panties and leaving almost nothing to imagination, riding up to dangerous heights, barely preserving modesty with only the slightest hint of delicate fabric between skin and air, her bright white intimate wear contrasting with shadow and revealing far more than it conceals, the tight fabric hugging her curves perfectly while creating a tantalizing high-cut silhouette that emphasizes her long slender legs and shapely hips, the skirt's hemline creating a deliberate and obvious gap above where her separate undergarment begins, the fabric so minimal it barely constitutes clothing, constantly shifting with her slightest movement to expose what should remain hidden."

# 新的百褶裙描述
new_skirt_pattern = r"Elegant BLACK PLEATED MINI SKIRT (NOT PANTS, NOT LEGGINGS), with crisp accordion folds fanning outward around her hips, the pleated fabric creating a stylish flared silhouette that swings gracefully with her slightest movement, short enough to reveal the full length of her shapely legs while maintaining a tasteful appearance, the pleats creating subtle shadows and highlights along the hemline, perfectly tailored to accent her slim waist and accentuate her feminine curves, the high-quality fabric maintaining precise folds while allowing freedom of movement, creating an elegant yet youthful look that complements her figure beautifully while remaining modest and sophisticated."

# 修改标记
old_tag_pattern = r"white_underwear pure_white_panties bright_white_underwear,, MINISKIRT NOT PANTS, black_mini_skirt separate_underwear,,"
new_tag_pattern = r"PLEATED_SKIRT, black_pleated_miniskirt, pleated_skirt_not_pants, elegant_pleated_skirt,,"

# 获取emotion_prompts文件夹中的所有txt文件
prompt_files = glob.glob('emotion_prompts/*.txt')

# 初始化计数器
updated_count = 0
skipped_count = 0

for file_path in prompt_files:
    # 获取文件名
    filename = os.path.basename(file_path)
    
    # 跳过排除的文件
    if filename in exclude_files:
        print(f"跳过文件: {filename}")
        skipped_count += 1
        continue
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换迷你裙描述
        updated_content = re.sub(old_skirt_pattern, new_skirt_pattern, content)
        
        # 替换标签
        updated_content = re.sub(old_tag_pattern, new_tag_pattern, updated_content)
        
        # 检查是否有更改
        if content != updated_content:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            
            print(f"已更新文件: {filename}")
            updated_count += 1
        else:
            print(f"文件无需更新: {filename}")
    
    except Exception as e:
        print(f"处理文件 {filename} 时出错: {str(e)}")

print(f"\n完成! 已更新 {updated_count} 个文件，跳过 {skipped_count} 个文件。")

# 更新negative_prompt.txt
try:
    negative_file = 'emotion_prompts/negative_prompt.txt'
    with open(negative_file, 'r', encoding='utf-8') as file:
        negative_content = file.read()
    
    # 确保负面提示词中不包含"pleated"或"百褶裙"
    if 'pleated' not in negative_content.lower():
        # 移除任何提到三角裤的负面提示
        negative_content = re.sub(r'panties, thong, underwear visible,', '', negative_content)
        
        # 添加新的负面提示项
        new_negative = "very short skirt, micro mini, mini skirt, exposed underwear, showing panties, visible underwear, pants, trousers, leggings, jeans, slacks, dress pants, sweatpants, capris, shorts, yoga pants,"
        
        # 更新负面提示文件
        with open(negative_file, 'w', encoding='utf-8') as file:
            file.write(negative_content.replace("pants, trousers, leggings, jeans, slacks, dress pants, sweatpants, capris, shorts, yoga pants,", new_negative))
        
        print(f"已更新负面提示词文件: {negative_file}")
    else:
        print(f"负面提示词文件无需更新: {negative_file}")
except Exception as e:
    print(f"处理负面提示词文件时出错: {str(e)}")

# 创建辅助指南
guide_content = """# 百褶裙效果优化指南

## 使用百褶裙的理由
1. 相比紧身迷你裙，百褶裙更加优雅、时尚，同时保持女性化特征
2. 百褶的设计能产生更多视觉层次感和动态效果
3. 避免生成模型混淆裙子和内衣的问题

## 推荐设置
为获得最佳的百褶裙效果：

1. CFG值设置为7-8，保持适当的创意空间
2. 使用DPM++ 2M Karras采样器
3. 步数设置为30-40
4. 全身图像建议分辨率为512x768或768x1024

## 正面提示词强化
在生成图像时，考虑添加以下关键词强化百褶裙效果：
- pleated_skirt, knife_pleats, accordion_pleats
- flared_skirt, a_line_skirt
- school_uniform_skirt, tennis_skirt
- elegant_pleated_design, crisp_folds

## 负面提示词
为避免生成问题，请使用以下负面提示词：
- very short skirt, micro mini, mini skirt (避免过短)
- exposed underwear, showing panties (避免内衣外露)
- pants, trousers, leggings (避免生成裤子)
- tight skirt, pencil skirt (避免紧身裙)
"""

try:
    # 写入指南文件
    with open('emotion_prompts/PLEATED_SKIRT_GUIDE.md', 'w', encoding='utf-8') as file:
        file.write(guide_content)
    
    print("已创建百褶裙指南文件: emotion_prompts/PLEATED_SKIRT_GUIDE.md")
except Exception as e:
    print(f"创建指南文件时出错: {str(e)}")

print("\n脚本执行完成! 所有情绪提示词已更新为使用黑色百褶裙描述。") 