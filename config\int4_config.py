# V100 INT4量化配置
# 适用于Tesla V100 SXM2 16GB

# BitsAndBytes配置
bnb_config = {
    "load_in_4bit": True,
    "bnb_4bit_compute_dtype": "float16",
    "bnb_4bit_use_double_quant": True,
    "bnb_4bit_quant_type": "nf4",
    "llm_int8_enable_fp32_cpu_offload": True,
    "llm_int8_has_fp16_weight": False
}

# GPTQ配置
gptq_config = {
    "bits": 4,
    "group_size": 128,
    "desc_act": False,
    "static_groups": False,
    "sym": True,
    "true_sequential": True,
    "model_name_or_path": None,
    "model_file_base_name": "model",
    "is_marlin_format": False
}

# AWQ配置  
awq_config = {
    "bits": 4,
    "group_size": 128,
    "zero_point": True,
    "version": "GEMM"
}
