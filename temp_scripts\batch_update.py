import os
import re

# 关键词模板
style_template = """
Wearing stylish red-framed glasses that accentuate her expressive features.
{eyes_line}
{expression_line}
Wearing crisp white button-up shirt with loosened tie at collar.
Tight black pencil skirt hugging hips and thighs.
Sheer black stockings on legs with subtle sheen.
{hair_line}
Hands crossed behind her back, not visible from front view.
"""

# 排除文件列表
exclude_files = ['admiration.txt', 'amusement.txt', 'README.md', 'admiration_glasses.txt']

# 文件夹路径
folder_path = 'E:/ComfyUI/emotion_prompts'

for filename in os.listdir(folder_path):
    if filename in exclude_files or not filename.endswith('.txt'):
        continue
    
    file_path = os.path.join(folder_path, filename)
    
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # 分行处理
    lines = content.strip().split('\n')
    main_line = lines[0]  # 保留第一行
    
    # 查找眼睛、表情和发型相关行
    eyes_line = ""
    expression_line = ""
    hair_line = ""
    lighting_line = ""
    
    for line in lines[1:]:
        if 'eye' in line.lower():
            eyes_line = line
            # 修改眼睛描述以包含眼镜
            if not 'through lenses' in eyes_line:
                eyes_line = eyes_line.replace('.', ' through lenses.')
            if eyes_line.endswith('.'):
                eyes_line = eyes_line[:-1]
            if not eyes_line.endswith('through lenses.'):
                eyes_line += ' through lenses.'
        elif 'smile' in line.lower() or 'mouth' in line.lower() or 'lips' in line.lower():
            expression_line = line
        elif 'hair' in line.lower():
            hair_line = line
        elif 'lighting' in line.lower() or 'light' in line.lower():
            lighting_line = line
            # 修改照明描述以包含眼镜
            if not 'reflection on red glasses' in lighting_line:
                if lighting_line.endswith('.'):
                    lighting_line = lighting_line[:-1] + ' with subtle reflection on red glasses.'
                else:
                    lighting_line += ' with subtle reflection on red glasses.'
    
    # 收集所有身体姿势和相机角度行等保留行
    other_lines = []
    for line in lines:
        if ('body' in line.lower() or 
            'camera' in line.lower() or 
            'shot' in line.lower() or 
            'angle' in line.lower() or 
            'focus' in line.lower()):
            if 'hand' not in line.lower():  # 排除手部描述
                other_lines.append(line)
    
    # 应用模板
    formatted_style = style_template.format(
        eyes_line=eyes_line if eyes_line else "Eyes expressive and full of emotion through lenses.",
        expression_line=expression_line if expression_line else "Face showing deep emotion.",
        hair_line=hair_line if hair_line else "Hair styled neatly framing the face."
    )
    
    # 构建新内容
    new_content = [main_line]
    new_content.extend(formatted_style.strip().split('\n'))
    
    # 添加照明行
    if lighting_line:
        new_content.append(lighting_line)
    else:
        new_content.append("Soft lighting enhancing mood with subtle reflection on red glasses.")
    
    # 添加其他重要行
    new_content.extend(other_lines)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write('\n'.join(new_content))
    
    print(f"Updated: {filename}")

print("All files have been updated with the new style template.")