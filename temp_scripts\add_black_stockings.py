#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def add_black_stockings(text):
    """为描述添加黑丝袜"""
    
    # 检查是否已经有黑丝袜描述
    if "black stockings" in text.lower() or "black pantyhose" in text.lower() or "black tights" in text.lower():
        return text
    
    # 找到服装描述
    outfit_match = re.search(r'She is wearing ([^\.]+)\.', text)
    if outfit_match:
        outfit_desc = outfit_match.group(1)
        # 如果没有提到丝袜，添加黑丝袜描述
        if "stockings" not in outfit_desc.lower() and "pantyhose" not in outfit_desc.lower() and "tights" not in outfit_desc.lower():
            new_outfit_desc = outfit_desc + ", with sexy black stockings accentuating her slender legs"
            text = text.replace(outfit_desc, new_outfit_desc)
    
    # 如果找不到标准的服装描述格式，尝试寻找其他可能的服装描述位置
    if "cutoff jeans" in text and "black stockings" not in text.lower():
        # 在牛仔裤描述后添加黑丝袜
        text = text.replace("cutoff jeans", "cutoff jeans, black stockings")
    
    # 确保有腿部的描述中包含黑丝袜
    leg_match = re.search(r'(H|h)er narrow waist and long[^\.]+\.', text)
    if leg_match:
        leg_desc = leg_match.group(0)
        if "stockings" not in leg_desc.lower() and "pantyhose" not in leg_desc.lower() and "tights" not in leg_desc.lower():
            # 在腿部描述中添加黑丝袜
            new_leg_desc = leg_desc.replace("legs", "legs covered in sleek black stockings")
            text = text.replace(leg_desc, new_leg_desc)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = add_black_stockings(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_stockings_example():
    """创建黑丝袜示例文件"""
    example_content = """Ultra high quality, photo, masterpiece, ultra high resolution, realistic, photorealistic, beautiful and youthful Japanese model, 19 years old, with petite body, perky boobs, slender, slender_waist and perfect face, exquisite eyes, luscious lips, blonde_hair, yellow_eyes. Standing in a modern office with elegant minimalist design. She is wearing cutoff jeans, black stockings that highlight her shapely legs, and a see-through white top, revealing her huge breasts. Her hands hidden, with jewelry adorning her delicate wrists. Her standing confidently, looking_up, mouth slightly open, face in closeup, seductive look, 3/4 angle view, creating a captivating portrait against the professional backdrop. Professional office lighting with soft natural light from large windows, highlighting her features perfectly. The scene captures a perfect balance between professional environment and alluring beauty, with tightly cropped composition emphasizing her face and upper body. saber_alter, fate_(series), DSLR photo, three quarter view, face focus, office setting, black stockings, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    example_path = os.path.join(EXAMPLES_DIR, "saber_alter_office_stockings.txt")
    with open(example_path, 'w', encoding='utf-8') as file:
        file.write(example_content)
    print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_stockings_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已添加黑丝袜描述
            content = add_black_stockings(content)
            
            with open(joy_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 