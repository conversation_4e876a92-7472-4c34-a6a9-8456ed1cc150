import os
import re

# 文件夹路径
folder_path = 'emotion_prompts'

# 眼镜描述
glasses_description = "Wearing thin-framed round black glasses that give an intellectual look."
no_glasses_description = ""  # 移除眼镜描述

# 眼睛描述中包含"through lenses"的字样
lenses_pattern = r'(\w+) through lenses'  # 匹配"通过镜片"的描述
lenses_replacement = r'\1'  # 保留前面的词，去掉"through lenses"

# 当前的上衣描述
current_top_description = "Tight-fitting light grey cropped polo shirt with deep V-neck and short sleeves, hugging every curve of her upper body, the extremely short cut ending well above her waist fully exposing her navel and a generous portion of bare midriff, thin fabric making her chest contours clearly visible through the stretched material."

# 新的上衣描述 - 运动风格，露出乳沟，略微透明
new_top_description = "Tight-fitting semi-transparent light grey sports bra with plunging neckline creating deep cleavage, the thin moisture-wicking material stretched across her chest revealing subtle nipple contours, cut extremely short to fully expose her navel and bare midriff, the sporty elasticated band hugging tightly under her breasts enhancing their natural shape."

# 当前的下装描述
current_bottom_description = "Extremely low-rise sage-green yoga leggings sitting dangerously low on her hips, the waistband barely covering her pubic area, with the center seam creating a pronounced camel toe effect, the ultra-thin and slightly translucent material molding to every intimate contour, making even the most subtle anatomical details clearly visible through the tightly stretched fabric."

# 新的下装描述 - 肉色瑜伽裤
new_bottom_description = "Ultra low-rise nude-colored yoga leggings almost matching her skin tone giving an illusory naked appearance from a distance, sitting perilously low on her hips with the waistband barely covering her pubic area, the center seam creating a pronounced camel toe effect, the ultra-thin almost transparent material molding perfectly to every intimate contour, clearly outlining her anatomy through the tightly stretched flesh-toned fabric."

# 排除文件
exclude_files = ['README.md', 'admiration_glasses.txt']

# 统计
updated_count = 0
skipped_count = 0

# 更新每个文件
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') and filename not in exclude_files:
        file_path = os.path.join(folder_path, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 移除眼镜描述
            content = content.replace(glasses_description, no_glasses_description)
            
            # 移除眼睛描述中的"through lenses"
            content = re.sub(lenses_pattern, lenses_replacement, content)
            
            # 查找和替换任何包含"through lenses"的行
            lines = content.split('\n')
            updated_lines = []
            
            for line in lines:
                if "through lenses" in line:
                    line = line.replace(" through lenses", "")
                updated_lines.append(line)
            
            content = '\n'.join(updated_lines)
            
            # 替换上衣描述
            content = content.replace(current_top_description, new_top_description)
            
            # 替换下装描述
            content = content.replace(current_bottom_description, new_bottom_description)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"已更新服装描述并移除眼镜: {filename}")
            updated_count += 1
                
        except Exception as e:
            print(f"处理文件时出错 {filename}: {str(e)}")
            skipped_count += 1

print(f"\n更新完成: {updated_count} 个文件已更新，{skipped_count} 个文件已跳过")
print("所有文件已更新，移除眼镜，服装改为运动风格上衣(露出乳沟)和肉色瑜伽裤。")
