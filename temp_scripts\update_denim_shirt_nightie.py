#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def update_outfit_description(text):
    """更新服装描述为解开扣子的蓝色牛仔衬衫和白色睡衣"""
    # 检查是否已经包含新的服装描述
    new_outfit_included = 'blue denim shirt' in text and 'white nightie' in text
    
    if not new_outfit_included:
        # 替换现有的服装描述段落
        pattern = r'wearing a black bikini top.+?thong peeking above the shorts.+?ears, adding'
        if re.search(pattern, text, re.IGNORECASE | re.DOTALL):
            text = re.sub(
                pattern,
                'wearing an unbuttoned blue denim shirt with white nightie underneath. A pendant with obsidian rose adorns her neck, adding',
                text,
                flags=re.IGNORECASE | re.DOTALL
            )
        else:
            # 单独替换各个部分的描述
            if 'bikini top' in text:
                text = re.sub(
                    r'(black bikini top[^,\.]*?)(,|\.)',
                    r'unbuttoned blue denim shirt\2',
                    text
                )
            
            if 'short shorts' in text or 'denim short' in text:
                text = re.sub(
                    r'(low-rise.+?short shorts[^,\.]*?)(,|\.)',
                    r'white nightie underneath the denim shirt\2',
                    text
                )
            
            if 'string thong' in text:
                text = text.replace('string thong peeking above the shorts', 
                                   'pendant with obsidian rose adorning her neck')
    
    # 更新身材描述，移除国家/民族描述
    text = text.replace('Arab ', '')
    
    # 确保有身材描述
    if 'narrow waist' not in text:
        text = re.sub(r'(Her statuesque figure with long legs)', 'Her narrow waist and long legs', text)
        if 'narrow waist' not in text:
            text = text.replace('long legs', 'narrow waist and long legs')
    
    # 添加波浪发型描述
    if 'wavy hair' not in text:
        text = re.sub(r'(straight hair)', 'wavy hair', text)
        if 'wavy hair' not in text and 'hair' in text:
            text = re.sub(r'hair', 'wavy hair', text, count=1)
    
    # 移除不需要的关键词
    text = text.replace('hair over one eye', '')
    
    # 添加卧姿描述
    if 'posing on her side on bed' not in text:
        text = re.sub(r'(standing elegantly|standing with)', 'posing on her side on bed with', text)
        if 'posing on her side on bed' not in text:
            text = re.sub(r'(Standing elegantly|Standing with)', 'Posing on her side on bed with', text)
    
    # 替换场景描述
    text = re.sub(r'in a clean minimalist interior with a pure white background', 'on a comfortable bed', text)
    text = re.sub(r'against a plain, light-colored backdrop', 'on a comfortable bed', text)
    
    # 删除或替换不必要的关键词
    to_remove = ['MINISKIRT_NOT_PANTS', 'denim short shorts', 'downturned eyes']
    for term in to_remove:
        text = text.replace(term, '')
    
    # 清理多余的空格和逗号
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r',\s*,', ',', text)
    
    # 添加特定的关键词
    if not 'high resolution' in text:
        if 'aidmaNSFWunlock' in text:
            text = text.replace('aidmaNSFWunlock', 'high resolution fashion editorial photograph, aidmaNSFWunlock')
        else:
            text = text + ', high resolution fashion editorial photograph'
    
    # 确保有gravure idol关键词
    if 'gravure idol' not in text:
        text = re.sub(r'(beautiful woman)', r'beautiful gravure idol', text)
    
    return text

def process_directory(directory):
    """处理指定目录中的所有文本文件"""
    processed = 0
    updated = 0
    skipped = 0
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(directory, "*.txt"))
    print(f"在 {directory} 中找到 {len(txt_files)} 个文本文件进行处理。")
    
    updated_files = []
    skipped_files = []
    
    for file_path in txt_files:
        file_name = os.path.basename(file_path)
        if file_name in EXCLUDE_FILES:
            skipped += 1
            skipped_files.append(file_name)
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 更新内容
            original_content = content
            content = update_outfit_description(content)
            
            # 如果内容有变化，则更新文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                updated += 1
                updated_files.append(file_name)
            else:
                skipped += 1
                skipped_files.append(file_name)
            
            processed += 1
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    print(f"\n目录 {directory} 处理完成: 总共处理了 {processed} 个文件")
    print(f"- 更新了 {updated} 个文件")
    print(f"- 跳过了 {skipped} 个文件")
    
    if updated > 0:
        print("\n已更新的文件:")
        for file in updated_files:
            print(f"- {file}")
    
    if skipped > 0:
        print(f"\n已跳过的文件数量: {skipped}")
    
    return processed, updated, skipped

def create_denim_nightie_example():
    """创建牛仔衬衫与睡衣造型的示例文件"""
    example_content = """beautiful gravure idol with wavy hair, posing on her side on bed. She is wearing an unbuttoned blue denim shirt with white nightie underneath. A pendant with obsidian rose adorns her neck, adding an elegant touch to her casual look. Her narrow waist and long legs create a graceful silhouette as she reclines comfortably. Soft, ambient lighting creates a warm, intimate atmosphere, highlighting the contrast between the rich blue denim and the delicate white fabric of her nightie. The scene captures a perfect balance between casual comfort and alluring beauty, with gentle shadows accentuating her feminine curves. Her relaxed yet posed position suggests both confidence and vulnerability, creating a captivating editorial portrait, high resolution fashion editorial photograph, aidmaimageupgrader, aidmaNSFWunlock"""
    
    # 创建第二个例子，更强调光线和质感
    example2_content = """beautiful gravure idol with flowing wavy hair cascading over her shoulders, elegantly posed on her side across rumpled bedsheets. She wears an artfully unbuttoned blue denim shirt revealing a delicate white silk nightie underneath. The contrast between the structured denim and the flowing, soft nightie creates a compelling visual narrative. A distinctive pendant featuring an obsidian rose hangs from a fine chain around her neck, catching the soft, diffused light that bathes the scene. Her narrow waist and long, graceful legs create an elegant line as she reclines against the pillows. The intimate bedroom setting is enhanced by gentle, warm lighting that casts subtle shadows, highlighting the textures of the fabrics and the smoothness of her skin. The composition has a thoughtful, editorial quality—capturing both vulnerable intimacy and confident poise in a single, striking image, high resolution fashion editorial photograph, aidmaimageupgrader, aidmaNSFWunlock"""
    
    # 创建示例文件目录（如果不存在）
    if not os.path.exists(EXAMPLES_DIR):
        os.makedirs(EXAMPLES_DIR)
    
    # 写入新的示例文件
    examples = {
        "denim_shirt_nightie.txt": example_content,
        "denim_shirt_bedpose.txt": example2_content
    }
    
    for filename, content in examples.items():
        example_path = os.path.join(EXAMPLES_DIR, filename)
        with open(example_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"\n已创建新示例文件: {example_path}")

def update_all_files():
    """更新所有目录中的文件"""
    total_processed = 0
    total_updated = 0
    total_skipped = 0
    
    # 处理表情文件
    print("开始处理表情文件...")
    p, u, s = process_directory(EMOTION_PROMPTS_DIR)
    total_processed += p
    total_updated += u
    total_skipped += s
    
    # 处理示例文件
    if os.path.exists(EXAMPLES_DIR):
        print("\n开始处理示例文件...")
        p, u, s = process_directory(EXAMPLES_DIR)
        total_processed += p
        total_updated += u
        total_skipped += s
    
    # 创建新的示例文件
    create_denim_nightie_example()
    
    # 打印总结果
    print(f"\n总处理结果:")
    print(f"- 总共处理了 {total_processed} 个文件")
    print(f"- 更新了 {total_updated} 个文件")
    print(f"- 跳过了 {total_skipped} 个文件")
    
    # 特别更新joy.txt文件
    joy_path = os.path.join(EMOTION_PROMPTS_DIR, "joy.txt")
    if os.path.exists(joy_path):
        try:
            with open(joy_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 确保joy.txt已更新到新的服装描述
            if 'blue denim shirt' not in content or 'white nightie' not in content:
                content = update_outfit_description(content)
                
                with open(joy_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                print(f"\n强制更新了文件: joy.txt")
        except Exception as e:
            print(f"强制更新文件 {joy_path} 时出错: {str(e)}")

if __name__ == "__main__":
    update_all_files() 