#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# 需要处理的表情文件目录
EMOTION_PROMPTS_DIR = "emotion_prompts"
EXAMPLES_DIR = "aidma_examples"

# 需要排除的文件
EXCLUDE_FILES = [
    "README.md",
    "negative_prompt.txt",
    "LEG_POSE_GUIDE.md",
    "FINAL_INSTRUCTIONS.md",
    "EXTREME_NEGATIVE.txt",
    "ULTIMATE_NEGATIVE.txt"
]

def remove_face_shape(text):
    """移除aidmaimageupgrader关键词和脸部形状描述"""
    
    # 移除aidmaimageupgrader关键词
    text = text.replace(", aidmaimageupgrader", "")
    text = text.replace(",aidmaimageupgrader", "")
    text = text.replace(" aidmaimageupgrader,", "")
    text = text.replace("aidmaimageupgrader,", "")
    text = text.replace("aidmaimageupgrader", "")
    
    # 修改刘海和脸部形状描述
    text = re.sub(r'side-swept bangs framing her oval face', 'side-swept bangs framing her face', text)
    text = re.sub(r'side-swept bangs framing her (oval|round|heart-shaped|square|long|diamond|triangular) face', 'side-swept bangs framing her face', text)
    text = re.sub(r'with side-swept bangs framing her (oval|round|heart-shaped|square|long|diamond|triangular) face', 'with side-swept bangs framing her face', text)
    text = re.sub(r'framing her (oval|round|heart-shaped|square|long|diamond|triangular) face', 'framing her face', text)
    
    # 移除其他脸部形状描述
    text = re.sub(r'her (oval|round|heart-shaped|square|long|diamond|triangular) face', 'her face', text)
    text = re.sub(r'with (oval|round|heart-shaped|square|long|diamond|triangular) face', 'with face', text)
    
    # 修复可能出现的"framing her face with"
    text = text.replace("framing her face with.", "framing her face.")
    text = text.replace("framing her face with ", "framing her face. ")
    
    # 处理可能出现的双重逗号和空格问题
    text = text.replace(",,", ",")
    text = re.sub(r'\s+,', ',', text)
    text = re.sub(r',\s+', ', ', text)
    text = re.sub(r'\s+', ' ', text)
    
    return text

def process_file(file_path):
    """处理单个文件"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 更新内容
        updated_content = remove_face_shape(content)
        
        # 如果内容有变化，则更新文件
        if updated_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            print(f"已更新文件: {file_path}")
            return True
        return False
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def update_all_files():
    """处理所有文件"""
    # 创建要处理的文件列表
    all_files = []
    
    # 添加emotion_prompts目录中的文件
    for file_name in os.listdir(EMOTION_PROMPTS_DIR):
        if file_name.endswith(".txt") and file_name not in EXCLUDE_FILES:
            all_files.append(os.path.join(EMOTION_PROMPTS_DIR, file_name))
    
    # 添加aidma_examples目录中的文件
    if os.path.exists(EXAMPLES_DIR):
        for file_name in os.listdir(EXAMPLES_DIR):
            if file_name.endswith(".txt"):
                all_files.append(os.path.join(EXAMPLES_DIR, file_name))
    
    print(f"找到 {len(all_files)} 个文件需要处理")
    
    # 处理所有文件
    updated_files = []
    
    for file_path in all_files:
        if process_file(file_path):
            updated_files.append(os.path.basename(file_path))
    
    print(f"\n总共更新了 {len(updated_files)} 个文件")
    if updated_files:
        print("已更新的文件:")
        for file_name in updated_files:
            print(f"- {file_name}")

if __name__ == "__main__":
    update_all_files() 